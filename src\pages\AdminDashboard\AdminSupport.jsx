import { useState, useEffect, useCallback, useRef } from "react";
import "../Support/Support.page.css";
import TicketDetailsModal from "../Support/components/TicketDetailsModal";
import { Table, Badge, Form, Row, Col, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import useDebounce from "../../utils/use-debounce";
import { format, parseISO } from "date-fns";
import { FaEye } from "react-icons/fa";
import {
  showErrorToast,
  showSuccessToast,
} from "../../utils/toast-success-error";

import {
  getAllTickets,
  getSingleTicket,
  replyToTicket,
  searchTickets,
  updateTicketStatus as updateTicketStatusApi,
  updateTicketPriority as updateTicketPriorityApi,
} from "../../services/tickets/admin";

import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import "./AdminSupport.css";

const AdminSupport = () => {
  useEffect(() => {
    const styleTag = document.createElement("style");
    styleTag.innerHTML = `
      select:disabled {
        color: #212529 !important;
        opacity: 0.8;
        background-color: #e9ecef;
      }
    `;
    document.head.appendChild(styleTag);

    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);

  // State for tickets data
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsToDisplay, setRecordsToDisplay] = useState(0);

  // State for filters
  const [statusFilter, setStatusFilter] = useState("All");
  const [priorityFilter, setPriorityFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearchActive, setIsSearchActive] = useState(false);

  // State for ticket details
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [isLoadingTicketDetails, setIsLoadingTicketDetails] = useState(false);
  const [loadingTicketId, setLoadingTicketId] = useState(null);

  // State for status counts
  const [statusCounts, setStatusCounts] = useState({
    open: 0,
    inprogress: 0,
    closed: 0,
  });

  // State for tracking which ticket is being updated
  const [updatingTicket, setUpdatingTicket] = useState({
    id: null,
    field: null,
  });

  // Use debounce for search term to avoid too many API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Refs to track state
  const initialFetchDone = useRef(false);
  const currentRequest = useRef(null);
  const isUpdatingTicketRef = useRef(false);

  // Helper to compute ticket status counts
  const computeStatusCounts = useCallback((ticketsList) => {
    const counts = { open: 0, inprogress: 0, closed: 0 };
    ticketsList.forEach((t) => {
      const status = t.status?.toLowerCase();
      if (status === "open") counts.open++;
      else if (status === "in progress" || status === "in_progress")
        counts.inprogress++;
      else if (status === "closed") counts.closed++;
    });
    return counts;
  }, []);

  // Fetch tickets data
  const fetchData = useCallback(
    async (
      url = `admin/ticket?page=${currentPage}&number_of_records=${recordsPerPage}`
    ) => {
      // Cancel any in-flight request
      if (currentRequest.current) {
        currentRequest.current.abort();
      }

      // Create a new AbortController
      currentRequest.current = new AbortController();

      try {
        setLoading(true);
        setIsSearchActive(false);

        // Pass the signal to the API call
        const response = await getAllTickets(
          url,
          currentRequest.current.signal
        );

        // If the request was aborted, don't update state
        if (currentRequest.current.signal.aborted) {
          return;
        }

        if (response && response.data) {
          const { data, meta, current_page, per_page, to, total, links } =
            response;
          setTickets(data || []);
          if (meta) {
            setCurrentPage(meta.current_page);
            setRecordsPerPage(meta.per_page);
            setPaginationLinks(meta.links);
            setTotal(meta.total);
            setRecordsToDisplay(meta.to);
          } else {
            setCurrentPage(current_page);
            setRecordsPerPage(per_page);
            setPaginationLinks(links);
            setTotal(total);
            setRecordsToDisplay(to);
          }

          // Count tickets by status
          const counts = computeStatusCounts(data);
          setStatusCounts(counts);
        } else {
          // Handle empty response
          setTickets([]);
          setPaginationLinks([]);
          setTotal(0);
          setRecordsToDisplay(0);
        }
      } catch (error) {
        // Only log and show error if it's not an abort error
        if (error.name !== "AbortError") {
          console.error("Error fetching tickets:", error);
          setTickets([]);
        }
      } finally {
        // Only update loading state if the request wasn't aborted
        if (currentRequest.current && !currentRequest.current.signal.aborted) {
          setLoading(false);
        }
      }
    },
    [currentPage, recordsPerPage]
  );

  // Initial data fetch
  useEffect(() => {
    if (!initialFetchDone.current) {
      fetchData();
      initialFetchDone.current = true;
    }
  }, [fetchData]);

  // Handle search
  useEffect(() => {
    if (debouncedSearchTerm) {
      handleSearch(debouncedSearchTerm);
    } else if (initialFetchDone.current) {
      // Only reset search if we've done the initial fetch and the search term is cleared
      setIsSearchActive(false);
      fetchData();
    }
  }, [debouncedSearchTerm, fetchData]);

  // Handle filter changes
  useEffect(() => {
    if (initialFetchDone.current) {
      // Only apply filters after initial data load
      applyFilters();
    }
  }, [statusFilter, priorityFilter]);

  // Apply filters
  const applyFilters = useCallback(() => {
    // Don't trigger a new fetch if we're already loading
    if (loading) return;

    // Reset to first page when filters change
    setCurrentPage(1);

    let endpoint = `admin/ticket?page=1&number_of_records=${recordsPerPage}`;

    // Add status filter
    if (statusFilter !== "All") {
      endpoint += `&status=${encodeURIComponent(statusFilter)}`;
    }

    // Add priority filter
    if (priorityFilter !== "All") {
      endpoint += `&priority=${encodeURIComponent(priorityFilter)}`;
    }

    fetchData(endpoint);
  }, [statusFilter, priorityFilter, recordsPerPage, loading, fetchData]);

  // Handle search
  const handleSearch = useCallback(
    async (term) => {
      try {
        setLoading(true);
        setIsSearchActive(true);

        // Cancel any in-flight request
        if (currentRequest.current) {
          currentRequest.current.abort();
        }

        // Create a new AbortController
        currentRequest.current = new AbortController();

        const response = await searchTickets(
          term,
          1,
          recordsPerPage,
          statusFilter !== "All" ? statusFilter : null,
          priorityFilter !== "All" ? priorityFilter : null,
          currentRequest.current.signal
        );

        // If the request was aborted, don't update state
        if (currentRequest.current.signal.aborted) {
          return;
        }

        if (response && response.data) {
          const { data, meta } = response;
          setTickets(data || []);
          setCurrentPage(meta.current_page);
          setPaginationLinks(meta.links);
          setTotal(meta.total);
          setRecordsToDisplay(meta.to);

          // Count tickets by status
          const counts = computeStatusCounts(data);
          setStatusCounts(counts);
        } else {
          // Handle empty response
          setTickets([]);
          setPaginationLinks([]);
          setTotal(0);
          setRecordsToDisplay(0);
        }
      } catch (error) {
        // Only log and show error if it's not an abort error
        if (error.name !== "AbortError") {
          console.error("Error searching tickets:", error);
          showErrorToast("Failed to search tickets");
        }
      } finally {
        // Only update loading state if the request wasn't aborted
        if (currentRequest.current && !currentRequest.current.signal.aborted) {
          setLoading(false);
        }
      }
    },
    [recordsPerPage, statusFilter, priorityFilter]
  );

  // Handle filter status change
  const handleFilterStatus = (status) => {
    setStatusFilter(status);
  };

  // Handle filter priority change
  const handleFilterPriority = (priority) => {
    setPriorityFilter(priority);
  };

  // Handle page change
  const handlePageChange = useCallback(
    async (url) => {
      try {
        setLoading(true);

        // Check if the URL already has number_of_records parameter
        if (!url.includes("number_of_records")) {
          // Add the current records per page to the URL
          const separator = url.includes("?") ? "&" : "?";
          url = `${url}${separator}number_of_records=${recordsPerPage}`;
        }

        // Extract page number from URL if possible
        const pageMatch = url.match(/page=(\d+)/);
        if (pageMatch && pageMatch[1]) {
          const newPage = parseInt(pageMatch[1], 10);
          setCurrentPage(newPage);
        }

        // Fetch data with the new URL
        fetchData(url);
      } catch (error) {
        console.error("Error navigating to page:", error);
        showErrorToast("Failed to navigate to page");
        setLoading(false);
      }
    },
    [recordsPerPage, fetchData]
  );

  // Handle records per page change
  const handlePageSizeChange = useCallback(
    async (size) => {
      try {
        setLoading(true);
        setCurrentPage(1);
        setRecordsPerPage(size);

        // Construct URL with current filters if any
        let url = `admin/ticket?page=1&number_of_records=${size}`;

        if (debouncedSearchTerm) {
          url += `&search=${encodeURIComponent(debouncedSearchTerm)}`;
        }

        if (statusFilter !== "All") {
          url += `&status=${encodeURIComponent(statusFilter)}`;
        }

        if (priorityFilter !== "All") {
          url += `&priority=${encodeURIComponent(priorityFilter)}`;
        }

        // Fetch data with the new URL
        fetchData(url);
      } catch (error) {
        console.error("Error changing records per page:", error);
        showErrorToast("Failed to update records per page");
        setLoading(false);
      }
    },
    [debouncedSearchTerm, statusFilter, priorityFilter, fetchData]
  );

  // Handle view ticket
  const handleViewTicket = async (ticketId) => {
    try {
      setIsLoadingTicketDetails(true);
      setLoadingTicketId(ticketId);

      const response = await getSingleTicket(ticketId);
      if (response && response.data) {
        setSelectedTicket(response.data);
        setShowTicketDetails(true);
      } else {
        showErrorToast("Failed to load ticket details");
      }
    } catch (error) {
      console.error("Error fetching ticket details:", error);
      showErrorToast("Failed to load ticket details");
    } finally {
      setIsLoadingTicketDetails(false);
      setLoadingTicketId(null);
    }
  };

  // Handle ticket status change
  const handleTicketStatusChange = async (ticketId, newStatus) => {
    // Prevent multiple simultaneous updates
    if (isUpdatingTicketRef.current) return;
    isUpdatingTicketRef.current = true;

    try {
      // Show loading only for this specific dropdown
      setUpdatingTicket({ id: ticketId, field: "status" });

      // Call the API to update the ticket status
      const response = await updateTicketStatusApi(ticketId, newStatus);

      if (response) {
        // Update the ticket in the tickets list
        setTickets((prevTickets) => {
          const updated = prevTickets.map((ticket) =>
            ticket.id === ticketId ? { ...ticket, status: newStatus } : ticket
          );
          setStatusCounts(computeStatusCounts(updated));
          return updated;
        });

        // Only update selected ticket if necessary
        if (selectedTicket && selectedTicket.id === ticketId) {
          setSelectedTicket((prev) => ({
            ...prev,
            status: newStatus,
          }));
        }
      }
    } catch (error) {
      console.error("Error updating ticket status:", error);
    } finally {
      // Reset loading state for this specific dropdown
      setUpdatingTicket({ id: null, field: null });
      isUpdatingTicketRef.current = false;
    }
  };

  // Get priority badge variant
  const getPriorityBadgeVariant = (priority) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "danger";
      case "medium":
        return "warning";
      case "low":
        return "success";
      default:
        return "secondary";
    }
  };

  // Handle updates coming from TicketDetailsModal
  const handleTicketUpdate = (updatedTicket) => {
    setTickets((prev) => {
      const updatedList = prev.map((t) =>
        t.id === updatedTicket.id ? { ...t, ...updatedTicket } : t
      );
      setStatusCounts(computeStatusCounts(updatedList));
      return updatedList;
    });

    if (selectedTicket && selectedTicket.id === updatedTicket.id) {
      setSelectedTicket(updatedTicket);
    }
  };

  return (
    <div className="p-4 admin-theme content-container">
      <div className="support-header d-flex justify-content-between align-items-center mb-4">
        <h1 className="text-white">Support Tickets Management</h1>
      </div>

      {showTicketDetails && (
        <TicketDetailsModal
          ticket={selectedTicket}
          isLoadingTicket={isLoadingTicketDetails}
          onClose={() => setShowTicketDetails(false)}
          onReply={(id, data) => replyToTicket(id, data)}
          onStatusChange={handleTicketStatusChange}
          isAdmin
          onTicketUpdate={handleTicketUpdate}
        />
      )}

      {/* Filters and search */}
      <div className="filters-container mb-4">
        <Row>
          <Col md={3}>
            <Form.Group>
              <Form.Label>Status</Form.Label>
              <Form.Select
                value={statusFilter}
                onChange={(e) => handleFilterStatus(e.target.value)}
              >
                <option value="All">All Statuses</option>
                <option value="open">Open ({statusCounts.open})</option>
                <option value="in progress">
                  In Progress ({statusCounts.inprogress})
                </option>
                <option value="closed">Closed ({statusCounts.closed})</option>
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={3}>
            <Form.Group>
              <Form.Label>Priority</Form.Label>
              <Form.Select
                value={priorityFilter}
                onChange={(e) => handleFilterPriority(e.target.value)}
              >
                <option value="All">All Priorities</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>Search</Form.Label>
              <Form.Control
                type="text"
                placeholder="Search by ticket title, description"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Form.Group>
          </Col>
        </Row>
      </div>

      {/* Tickets table */}
      <div className="tickets-table-container">
        <Table
          responsive
          hover
          striped
          bordered
          className="text-center position-relative"
        >
          <thead>
            <tr>
              <th>#</th>
              <th>Title</th>
              <th>Client</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan="6">
                  <FetchingDataLoading />
                </td>
              </tr>
            ) : (
              tickets.map((ticket, index) => (
                <tr
                  key={ticket.id}
                  className="client-table-row filter-table-rows"
                >
                  <td>{index + 1}</td>
                  <td>
                    <div
                      className="text-nowrap overflow-hidden text-center mx-auto one-line"
                      style={{ maxWidth: "200px" }}
                    >
                      {ticket.title}
                    </div>
                  </td>
                  <td>
                    <div className="text-center">{ticket?.user?.name}</div>
                  </td>
                  <td>
                    <div className="text-center">
                      <div className="select-container">
                        <Form.Select
                          size="sm"
                          value={ticket.status}
                          onChange={(e) =>
                            handleTicketStatusChange(ticket.id, e.target.value)
                          }
                          disabled={
                            updatingTicket.id === ticket.id &&
                            updatingTicket.field === "status"
                          }
                        >
                          <option value="open">Open</option>
                          <option value="in_progress">In Progress</option>
                          <option value="closed">Closed</option>
                        </Form.Select>
                        {updatingTicket.id === ticket.id &&
                          updatingTicket.field === "status" && (
                            <div className="select-loading-indicator">
                              <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                              >
                                <span className="visually-hidden">
                                  Loading...
                                </span>
                              </div>
                            </div>
                          )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <Badge
                      className="text-center"
                      bg={getPriorityBadgeVariant(ticket.priority)}
                    >
                      {ticket.priority}
                    </Badge>
                  </td>
                  <td>
                    {format(parseISO(ticket.created_at), "yyyy-MM-dd HH:mm")}
                  </td>
                  <td>
                    {isLoadingTicketDetails && loadingTicketId === ticket.id ? (
                      <Button variant="primary" disabled>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                        />
                        <span className="visually-hidden">Loading...</span>
                      </Button>
                    ) : (
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleViewTicket(ticket.id)}
                        disabled={loadingTicketId === ticket.id}
                      >
                        <FaEye className="me-2" />
                        View
                      </Button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </Table>
      </div>

      {!isSearchActive && (
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
          currentPage={currentPage}
        />
      )}
    </div>
  );
};

export default AdminSupport;
