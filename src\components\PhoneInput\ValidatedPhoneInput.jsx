import React from 'react';
import PhoneInput from 'react-phone-number-input';
import { isValidPhoneNumber, isPossiblePhoneNumber } from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import '../Styles/PhoneInput/phone-input-validation.css';

/**
 * ValidatedPhoneInput - A wrapper component for PhoneInput that adds validation styling
 *
 * @param {Object} props - Component props
 * @param {string} props.value - The phone number value
 * @param {Function} props.onChange - Function to call when the value changes
 * @param {boolean} props.international - Whether to show the international format
 * @param {string} props.defaultCountry - The default country code
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.name - Input name
 * @param {Function} props.onBlur - Function to call on blur
 * @param {Object} props.style - Custom styles
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showValidationMessage - Whether to show validation messages
 * @param {string} props.validMessage - Custom valid message
 * @param {string} props.invalidMessage - Custom invalid message
 * @param {boolean} props.touched - Whether the field has been touched (for validation)
 * @param {string} props.error - Error message from form validation
 * @param {boolean} props.required - Whether the field is required
 * @param {boolean} props.usePossibleCheck - Use isPossiblePhoneNumber instead of isValidPhoneNumber
 */
const ValidatedPhoneInput = ({
  value,
  onChange,
  international = false,
  defaultCountry = "EG",
  placeholder = "Enter phone number",
  name,
  onBlur,
  style,
  className = "",
  showValidationMessage = false,
  validMessage = "Valid phone number",
  invalidMessage = "Invalid phone number",
  touched = false,
  error,
  required = false,
  usePossibleCheck = false,
  ...props
}) => {
  // Only validate if there's a value and the field has been touched
  const shouldValidate = value && touched;

  // Determine if the phone number is valid
  const isValid = shouldValidate && value ?
    (usePossibleCheck ? isPossiblePhoneNumber(value) : isValidPhoneNumber(value)) :
    false;

  // Determine if the phone number is invalid
  const isInvalid = shouldValidate && value ?
    !(usePossibleCheck ? isPossiblePhoneNumber(value) : isValidPhoneNumber(value)) :
    false;

  // Determine validation class
  const validationClass = (touched && error) || (required && touched && !value) ? 'is-invalid' : touched && isValid ? 'is-valid' : '';

  const isRTL = document.documentElement.dir === 'rtl';

  // Check if we're in RTL mode by looking at document direction
  return (
    <div className={`validated-phone-input-container ${document.documentElement.dir === 'rtl' ? 'rtl' : ''}`}>
      <PhoneInput
        name={name}
        international={international}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        defaultCountry={defaultCountry}
        className={`PhoneInput ${validationClass} ${className}`}
        style={style}
        {...props}
      />

      {showValidationMessage && isValid && (
        <div className="phone-validation-message valid-feedback" style={{ textAlign: isRTL ? 'right' : 'left' }}>
          {validMessage}
        </div>
      )}

      {(showValidationMessage && isInvalid) || (touched && error) ? (
        <div className="phone-validation-message invalid-feedback" style={{ textAlign: isRTL ? 'right' : 'left' }}>
          {error || invalidMessage}
        </div>
      ) : null}
    </div>
  );
};

export default ValidatedPhoneInput;
