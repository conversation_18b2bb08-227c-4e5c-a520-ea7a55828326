import {Col, Row} from "react-bootstrap";
import {Calendar, momentLocalizer} from "react-big-calendar";
import moment from "moment/moment";
import {IoChevronBack, IoChevronForward} from "react-icons/io5";

const TeamMemberCalendarComponent = () => {
    const localizer = momentLocalizer(moment);
    const CustomToolbar = (toolbar) => {
        const goToBack = () => {
            toolbar.onNavigate("PREV");
        };

        const goToNext = () => {
            toolbar.onNavigate("NEXT");
        };

        const goToToday = () => {
            toolbar.onNavigate("TODAY");
        };

        const goToWeek = () => {
            toolbar.onView("week");
        };

        const goToDay = () => {
            toolbar.onView("day");
        };

        const goToMonth = () => {
            toolbar.onView("month");
        };

        return (<div className="rbc-toolbar justify-content-around">
            <button type="button" className="btn btn-secondary" onClick={goToToday}>
                Today
            </button>

            <div className={"d-flex justify-content-between align-items-center"}>
                <div role={"button"} onClick={goToBack}>
                    <IoChevronBack size={30} className={"bg-white rounded-circle p-1 shadow"}/>
                </div>
                <span className="rbc-toolbar-label">{toolbar.label}</span>
                <div role={"button"} onClick={goToNext}>
                    <IoChevronForward size={30} className={"bg-white rounded-circle p-1 shadow"}/>
                </div>
            </div>

            <div className="btn-group ml-2" role="group">
                <button type="button" className="btn btn-secondary" onClick={goToWeek}>
                    Week
                </button>
                <button type="button" className="btn btn-secondary" onClick={goToDay}>
                    Day
                </button>
                <button type="button" className="btn btn-secondary" onClick={goToMonth}>
                    Month
                </button>
            </div>

        </div>);
    };
    const CustomEvent = ({ event }) => {
        const secondaryColor = event.borderColor;

        const eventStyle = {
            backgroundColor: "transparent",
            borderRadius: "6px",
            backdropFilter: "blur(2px)",
            opacity: 0.8,
            color: secondaryColor,
            // border: `1px solid ${secondaryColor}`,
            display: "block",
            padding: "5px",
            cursor: "pointer",
        };

        const labelStyle = {
            padding: "2px",
        };

        return (
            <div style={eventStyle}>
                {/* Your custom event content */}
                <div className="rbc-event-label" style={labelStyle}>
                    <div>{event.title}</div>
                </div>
            </div>
        );
    };

    const events = [
        {
            id: 1,
            title: "Meeting 1",
            start: new Date(2024, 0, 22, 10, 0), // year, month (0-based), day, hour, minute
            end: new Date(2024, 0, 22, 12, 0),
            borderColor: "#FF5733", // replace with your color logic
        },
        {
            id: 2,
            title: "Meeting 2",
            start: new Date(2024, 0, 23, 14, 0),
            end: new Date(2024, 0, 23, 16, 0),
            borderColor: "#3498db", // replace with your color logic
        },
        // Add more events as needed
    ];

    return (
        <Row>
            <Col lg={12} className={"p-4"}>
                <Calendar
                    localizer={localizer}
                    events={events}
                    // eventPropGetter={eventStyleGetter}
                    // onEventDrop={onEventDrop}
                    // onEventResize={onEventResize}
                    resizable
                    defaultDate={moment().toDate()}
                    defaultView="week"
                    className={"calendar-container"}
                    components={{
                        event: CustomEvent,
                        toolbar: CustomToolbar,
                    }}
                />
            </Col>
        </Row>
    );
};

export default TeamMemberCalendarComponent;