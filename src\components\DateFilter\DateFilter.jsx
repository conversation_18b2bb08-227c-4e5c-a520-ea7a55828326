import React from 'react';
import { Button } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import { FaCalendarAlt } from 'react-icons/fa';
import { LuCalendarSearch } from 'react-icons/lu';
import "react-datepicker/dist/react-datepicker.css";
import { useDropdownClose } from '../../hooks/useDropdownClose';

const DateFilter = ({
  showFilter,
  onToggle,
  dateRange,
  onDateChange,
  onApply,
  onClear
}) => {
  const dropdownRef = useDropdownClose(onToggle);

  return (
    <div className="position-relative" ref={dropdownRef}>
      <Button
        variant="outline-light"
        size="md"
        onClick={() => onToggle(!showFilter)}
        className="d-flex align-items-center gap-2"
      >
        <LuCalendarSearch />
      </Button>

      {showFilter && (
        <div
          className="position-absolute mt-2 bg-dark p-3 rounded shadow"
          style={{ right: 0, zIndex: 1000, minWidth: '300px' }}
        >
          <div className="mb-3">
            <div className="d-flex align-items-center gap-2">
              <DatePicker
                selected={dateRange.startDate}
                onChange={(date) => onDateChange({...dateRange, startDate: date})}
                selectsStart
                startDate={dateRange.startDate}
                endDate={dateRange.endDate}
                placeholderText="Start Date"
                className="form-control bg-secondary text-light border-0"
              />
              <FaCalendarAlt className="text-light" />
            </div>
          </div>
          <div className="mb-3">
            <div className="d-flex align-items-center gap-2">
              <DatePicker
                selected={dateRange.endDate}
                onChange={(date) => onDateChange({...dateRange, endDate: date})}
                selectsEnd
                startDate={dateRange.startDate}
                endDate={dateRange.endDate}
                minDate={dateRange.startDate}
                placeholderText="End Date"
                className="form-control bg-secondary text-light border-0"
              />
              <FaCalendarAlt className="text-light" />
            </div>
          </div>
          <div className="d-flex justify-content-between">
            <Button
              variant="secondary"
              size="sm"
              onClick={onClear}
            >
              Clear
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={onApply}
              disabled={!dateRange.startDate || !dateRange.endDate}
            >
              Apply Filter
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateFilter;

