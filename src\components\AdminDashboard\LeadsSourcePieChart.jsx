import { Col, Dropdown } from "react-bootstrap";
import { HiDotsVertical } from "react-icons/hi";
import { HiMiniArrowUturnRight, HiOutlineXMark } from "react-icons/hi2";
import { FiEdit } from "react-icons/fi";
import { Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js";

ChartJS.register(ArcElement, ChartTooltip, Legend);

const LeadsSourcePieChart = ({ getColor, pieChartRoundedData }) => {
  return (
    <>
      <div className={"d-flex justify-content-between"}>
        <h2 className={"text-white"}>Lead Source</h2>
      </div>
      <div className="position-relative mx-auto">
        <Doughnut
          style={{ width: "300px", height: "300px" }}
          className="doughnut-team-chart mx-auto"
          data={{
            labels: pieChartRoundedData.map((datum) => datum.x),
            datasets: [
              {
                data: pieChartRoundedData.map((datum) => datum.y),
                backgroundColor: pieChartRoundedData.map((datum) =>
                  getColor(datum.x)
                ),
                borderColor: "#fff",
                borderWidth: 4,
              },
            ],
          }}
          options={{
            responsive: true,
            plugins: {
              tooltip: {
                enabled: true,
                boxPadding: 8,
                callbacks: {
                  label: function (context) {
                    const label = context.label || "";
                    const value = context.raw || 0;
                    return `${label}: ${value}%`;
                  },
                },
              },
              legend: {
                display: false,
              },
            },
            elements: {
              arc: {
                borderRadius: 20,
                borderWidth: 4,
              },
            },
            layout: {
              padding: {
                top: 20,
                bottom: 20,
                left: 20,
                right: 20,
              },
            },
            cutout: "80%",
          }}
        />
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
            fontSize: "20px",
            fontWeight: "bold",
            color: "white",
            zIndex: 10,
          }}
        >
          {pieChartRoundedData?.length > 0
            ? `${
                pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b), {
                  x: "",
                  y: 0,
                }).x
              }: ${
                pieChartRoundedData?.reduce((a, b) => (a.y > b.y ? a : b), {
                  x: "",
                  y: 0,
                }).y
              }%`
            : "No data available"}
        </div>
      </div>
      <div
        className="labels-container text-white"
        style={{ maxHeight: "100px", overflowY: "auto" }}
      >
        {pieChartRoundedData
          ?.filter((datum) => datum.y > 0)
          ?.map((datum, index) => (
            <div key={index} className="label-item">
              <span
                className="dot"
                style={{ backgroundColor: getColor(datum.x) }}
              />
              {datum.x}
            </div>
          ))}
      </div>
    </>
  );
};

export default LeadsSourcePieChart;
