import { IoIosLogIn, IoIosLogOut } from "react-icons/io";
import { Dropdown, Navbar } from "react-bootstrap";
// import {useEffect, useState} from "react";
import userImg from "../../assets/media/user.png";
import { Link, useLocation } from "react-router-dom";
import Select from "react-select";
import { useEffect, useState } from "react";
import adminDashboardService from "../../services/admin/admin-dashboard-services";
import { useTranslation } from "react-i18next";
import "../Layout/Header/Header.css";
import useAdmin from "../../redux/hooks/useAdmin";
import useAuth from "../../redux/hooks/useAuth";

const Header = () => {
  const { user, logout } = useAuth();
  const { selectedClient, setSelectedClient } = useAdmin();
  const userData = user?.user;
  const [isLoading, setIsLoading] = useState(false);
  const [clients, setClients] = useState([]);
  const { t, i18n } = useTranslation();

  useEffect(() => {
    if (!user) {
      return;
    }
    const fetchInitialData = async () => {
      setIsLoading(true);
      const clientsData =
        await adminDashboardService.getAdminDashboardClientsApi();
      setClients(
        clientsData?.result?.map((client) => ({
          ...client,
          value: client?.id,
          label: client?.name,
        }))
      );
      setIsLoading(false);
    };

    fetchInitialData();
  }, [user]);

  const handleChange = (selectedOption) => {
    setSelectedClient(selectedOption);
  };

  const location = useLocation();
  const routeState = {
    from: location.pathname,
    signin: location.state?.signin,
  };

  // Function to get page title based on current path using switch case
  const getPageTitle = () => {
    const path = location.pathname;

    // Check if we're on the LeadsForClient page
    const clientIdMatch = path.match(/\/admin\/clients\/leads\/([\d]+)/);
    if (clientIdMatch) {
      // If we're on the LeadsForClient page, return "Leads For Client Name"
      return `${t("sidebar.leads", "Leads")} ${t("common.for", "For")} ${location.state?.clientName || t("common.client", "Client")
        }`;
    }

    switch (true) {
      case path === "/admin/dashboard":
        return t("sidebar.dashboard", "Dashboard");
      case path.includes("/admin/clients"):
        return t("sidebar.clients", "Clients");
      case path.includes("/admin/team"):
        return t("sidebar.team", "Team");
      case path.includes("/admin/reports"):
        return t("sidebar.reports", "Reports");
      case path.includes("/admin/subscription"):
        return t("sidebar.subscriptions", "Subscriptions");
      case path.includes("/admin/settings"):
        return t("sidebar.settings", "Settings");
      case path.includes("admin/clients/leads/profile"):
        return t("sidebar.leadDetails", "Lead Details");
      case path.includes("admin/support"):
        return t("sidebar.support", "Support");
      default:
        return "";
    }
  };

  const conditionalUserImg = user?.user?.photo
    ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT + user?.user?.photo
    : userImg;
  // console.log(location)
  return (
    <header style={{ backgroundColor: "rgb(0, 0, 0)" }}>
      <Navbar
        className={
          "dashboard-header px-3 py-2 justify-content-between align-items-center flex-column flex-lg-row"
        }
      >
        {location.pathname === "/admin/dashboard" ? (
          <Navbar.Brand
            className={
              "d-flex flex-column flex-lg-row justify-content-center align-items-center"
            }
          >
            <div className={"me-2"}>View Dashboard As :</div>
            <Select
              className="select-client"
              placeholder={"Search/Select"}
              isLoading={isLoading}
              isClearable={true}
              name="client"
              options={clients}
              isMulti={false}
              value={selectedClient}
              onChange={handleChange}
            />
          </Navbar.Brand>
        ) : (
          <Navbar.Brand
            className={
              "d-flex flex-column flex-lg-row justify-content-center align-items-center"
            }
          >
            <h4 className="mb-0">{getPageTitle()}</h4>
            {location.pathname.includes("/admin/client/leads/") &&
              location.state?.clientName && (
                <span className="ms-2 text-white-50">
                  ({location.state.clientName})
                </span>
              )}
          </Navbar.Brand>
        )}
        <div
          className={"d-flex justify-content-between align-items-center mx-3"}
        >
          <div
            className={
              "d-flex justify-content-between text-center align-items-center"
            }
          >
            <div className={"mx-3 text-dark"}>
              {selectedClient ? selectedClient?.name : userData?.name || "User"}
            </div>
            <div
              className={
                "d-flex justify-content-center align-items-center gap-3"
              }
            >
              {/*<LanguageSwitcher />*/}
              <Dropdown
                className="d-inline mx-2 profile-dropdown-container"
                drop={i18n.dir() === "rtl" ? "start" : "end"}
              >
                <Dropdown.Toggle
                  id="dropdown-autoclose-true"
                  className={"profile-dropdown-button"}
                >
                  <img
                    src={conditionalUserImg}
                    alt={"Profile Pic"}
                    className={"profile-picture"}
                  />
                </Dropdown.Toggle>

                <Dropdown.Menu className={"profile-dropdown-menu p-3"}>
                  <div className={"text-center"}>
                    <img
                      src={conditionalUserImg}
                      alt={"Profile Pic"}
                      className={"profile-picture-dropdown"}
                    />
                    <p>{userData?.name || "User"}</p>
                    <Link to={"/account-settings"}>
                      <button className={"pricing-button text-uppercase"}>
                        profile
                      </button>
                    </Link>
                    <div className={"mt-3"}>
                      <div>{t("header.userGuide")}</div>
                      <div>{t("header.support")}</div>
                      {user ? (
                        <div
                          className={
                            "d-flex justify-content-center text-danger gap-2"
                          }
                          onClick={() => logout()}
                          role={"button"}
                        >
                          <div>{t("header.logout")}</div>
                          <div>
                            <IoIosLogOut />
                          </div>
                        </div>
                      ) : (
                        <Link
                          to={"/client/login"}
                          state={routeState}
                          className={
                            "d-flex justify-content-between text-success"
                          }
                        >
                          <div>{t("header.login")}</div>
                          <div>
                            <IoIosLogIn />
                          </div>
                        </Link>
                      )}
                    </div>
                  </div>
                </Dropdown.Menu>
              </Dropdown>
            </div>
          </div>
        </div>
        {/* </div> */}
      </Navbar>
    </header>
  );
};

export default Header;
