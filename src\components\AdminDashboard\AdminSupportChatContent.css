.admin-support-chat-content {
  display: flex;
  height: calc(100vh - 120px);
}

.clients-sidebar {
  width: 300px;
  border-right: 1px solid #dee2e6;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-container {
  border-bottom: 1px solid #dee2e6;
}

.clients-list {
  overflow-y: auto;
  flex-grow: 1;
}

.client-item {
  border-left: none !important;
  border-right: none !important;
  padding: 0.75rem 1rem;
  background-color: #212529;
  color: #ffffff;
}

.client-item.active {
  background-color: #e9ecef !important;
  color: #212529 !important;
  border-color: #dee2e6 !important;
}

.client-item.has-unread:not(.active) {
  background-color: rgba(13, 110, 253, 0.05);
}

.client-item.has-unread:not(.active):hover {
  background-color: rgba(13, 110, 253, 0.1);
  color: #007bff;
}

.client-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.last-message {
  font-size: 0.875rem;
  color: #6c757d;
  max-width: 180px;
}

.chat-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-area.full-width {
  width: 100%;
}

.chat-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.message-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  min-width: 100%;
}

.message-right {
  align-self: flex-end;
  align-items: flex-end;
}

.message-left {
  align-self: flex-start;
  align-items: flex-start;
}

[dir="rtl"] .message-right {
  align-self: flex-start;
  align-items: flex-start;
}

[dir="rtl"] .message-left {
  align-self: flex-end;
  align-items: flex-end;
}

.message-bubble {
  display: flex;
  flex-direction: column;
  max-width: 75%;
  padding: 8px 12px;
  border-radius: 12px;
  position: relative;
}

/* For text messages, keep time inline */
.message-bubble.with-text {
  flex-direction: row;
  align-items: flex-end;
}

/* Message content should expand to fill available space */
.message-content {
  width: 100%;
  word-break: break-word;
}

.client-message {
  background-color: #007bff;
  color: white;
  border-bottom-left-radius: 0.25rem;
}

.support-message {
  background-color: #e9ecef;
  color: #212529;
  border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .client-message {
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .support-message {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
}

.message-time {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  opacity: 0.8;
}

.message-sender {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: #6c757d;
}

.message-input-container {
  background-color: #ffffff;
  border-top: 1px solid #dee2e6;
  width: 100%;
  border-bottom-right-radius: 1rem;
}

.attachment-preview {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  padding: 0.4rem 0.6rem;
  background-color: #e9ecef;
  border-radius: 8px;
  max-width: 30vw;
  width: fit-content;
}

.attachment-thumbnail {
  max-width: 25vw;
  max-height: 20vh;
  width: auto;
  height: auto;
  object-fit: cover;
}

.attachment-filename {
  max-width: 40vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-preview .remove-attachment-btn-top {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 24px;
  height: 24px;
  padding: 0 !important;
  background-color: rgba(0,0,0,0.6) !important;
  border: none;
  color: #fff !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.attachment-preview .remove-attachment-btn-top:hover {
  background-color: rgba(0,0,0,0.8) !important;
}

.attachment-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  width: 38px;
  padding: 0;
}

.message-image {
  border-radius: 0.5rem;
  max-width: 100%;
}

.message-audio {
  width: 100%;
  max-width: 300px;
}

.message-video {
  border-radius: 0.5rem;
  max-width: 100%;
}

.file-attachment {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.file-attachment:hover {
  background-color: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.back-button {
  color: #6c757d;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.back-button:hover {
  color: #343a40;
}

/* RTL support for text input */
[dir="rtl"] .form-control {
  text-align: right;
}

.toggle-clients-btn {
  display: none;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem;
}

.flip-horizontal {
  transform: scaleX(-1);
}

@media (max-width: 768px) {
  .admin-support-chat-content {
    flex-direction: column;
    height: calc(100vh - 100px);
  }

  .clients-sidebar {
    width: 100%;
    height: 100%;
    border-right: none;
    border-bottom: 1px solid #dee2e6;
  }

  .message-container {
    max-width: 90%;
  }

  .toggle-clients-btn {
    display: flex;
  }

  /* Ensure the send box stays visible above the mobile footer navigation */
  .message-input-container {
    margin-bottom: 50px; /* approximate footer height */
  }
}

/* Add these styles to your CSS file */
.date-header {
  position: sticky;
  top: 10px;
  z-index: 1;
}

.date-label {
  background-color: rgba(240, 240, 240, 0.9) !important;
  font-size: 0.8rem;
  color: #666;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-right .message-time-inline {
  color: #fff;
}

.message-time-inline {
  font-size: 0.7rem;
  margin-left: 8px;
  margin-top: 2px;
  display: inline-block;
  white-space: nowrap;
}

/* Adjust existing message bubble styles to accommodate inline time */
.support-message, .client-message {
  max-width: calc(60% - 40px);
}

/* File attachment styles */
.file-attachment {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.file-attachment:hover {
  background-color: rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

.message-audio {
  max-width: 250px;
  margin: 5px 0;
}

.message-image, .message-video {
  border-radius: 8px;
  max-width: 200px;
  max-height: 200px;
  object-fit: cover;
}

/* Adjust time display for media messages */
.message-time-inline {
  font-size: 0.7rem;
  color: #888;
  align-self: flex-end;
  margin-top: 4px;
  text-align: right;
}

/* For text messages, keep time inline */
.message-bubble.with-text .message-time-inline {
  margin-left: 8px;
  margin-top: 0;
  white-space: nowrap;
  margin-bottom: 2px;
}

/* Media content specific styles */
.message-image, .message-video {
  border-radius: 8px;
  max-width: 200px;
  max-height: 200px;
  object-fit: cover;
}

.file-attachment {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

/* Attachment thumbnail preview before sending */
.attachment-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

.attachment-thumbnail {
  max-width: 25vw;   /* responsive: up to 25% of viewport width */
  max-height: 20vh;  /* responsive: up to 20% of viewport height */
  width: auto;
  height: auto;
  object-fit: cover;
}

