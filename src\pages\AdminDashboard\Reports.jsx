import { PiExportBold } from "react-icons/pi";
import { Tooltip } from "react-tooltip";
import ActiveProjectsTable from "../../components/AdminDashboard/ActiveProjectsTable";
import { useState } from "react";
import { Button } from "react-bootstrap";
import "../../components/CustomDataTable/datatable.css";
import { useTranslation } from "react-i18next";

const ReportsPage = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [filter, setFilter] = useState("monthly"); // Default filter to monthly

  const handleFilterChange = (selectedFilter) => {
    setFilter(selectedFilter);
  };

  const tableActions = () => {
    return (
      <>
        <span className={"export-reports"}>
          <PiExportBold size={25} />
        </span>
        <Tooltip
          anchorSelect=".clear-filter"
          content={t('tooltips.clearFilter')}
          className={"bg-white text-dark"}
        />
        <Tooltip
          anchorSelect=".export-reports"
          content={t('tooltips.exportReports')}
          className={"bg-white text-dark"}
        />
      </>
    );
  };

  const tableHeader = () => {
    return (
      <div
        aria-label="Filter Buttons"
        className={"mb-3 d-flex justify-content-start align-items-center"}
      >
        <Button
          variant="outline-secondary"
          className={filter === "monthly" ? "active" : ""}
          onClick={() => handleFilterChange("monthly")}
        >
          {t('filters.monthly')}
        </Button>
        <Button
          variant="outline-secondary"
          className={`mx-2 ${filter === "weekly" ? "active" : ""}`}
          onClick={() => handleFilterChange("weekly")}
        >
          {t('filters.weekly')}
        </Button>
        <Button
          variant="outline-secondary"
          className={filter === "daily" ? "active" : ""}
          onClick={() => handleFilterChange("daily")}
        >
          {t('filters.daily')}
        </Button>
      </div>
    );
  };

  const columns = [
    {
      Header: t('tables.headers.totalClients'),
      accessor: "totalClients",
    },
    {
      Header: t('tables.headers.createdTeamMembers'),
      accessor: "createdTeamMembers",
    },
    {
      Header: t('tables.headers.completedLeads'),
      accessor: "completedLeads",
    },
    {
      Header: t('tables.headers.topRatedTeamMember'),
      accessor: "topRatedTeamMember",
    },
    {
      Header: t('tables.headers.mostActiveClient'),
      accessor: "mostActiveClient",
    },
  ];

  let data = [];

  if (filter === "monthly") {
    data = [
      {
        completedLeads: 25,
        createdTeamMembers: 3,
        topRatedTeamMember: "John Doe",
        mostActiveClient: "Client A",
        totalClients: 50,
      },
      {
        completedLeads: 20,
        createdTeamMembers: 2,
        topRatedTeamMember: "Jane Smith",
        mostActiveClient: "Client B",
        totalClients: 40,
      },
    ];
  } else if (filter === "weekly") {
    data = [
      {
        completedLeads: 10,
        createdTeamMembers: 1,
        topRatedTeamMember: "Alice Johnson",
        mostActiveClient: "Client C",
        totalClients: 30,
      },
      {
        completedLeads: 15,
        createdTeamMembers: 2,
        topRatedTeamMember: "Bob Williams",
        mostActiveClient: "Client D",
        totalClients: 35,
      },
    ];
  } else if (filter === "daily") {
    data = [
      {
        completedLeads: 5,
        createdTeamMembers: 1,
        topRatedTeamMember: "Eva Martinez",
        mostActiveClient: "Client E",
        totalClients: 20,
      },
      {
        completedLeads: 8,
        createdTeamMembers: 1,
        topRatedTeamMember: "Frank Wilson",
        mostActiveClient: "Client F",
        totalClients: 25,
      },
    ];
  }

  return (
    <>
      <ActiveProjectsTable
        columns={columns}
        data={data}
        loading={false}
        recordsPerPage={recordsPerPage}
        setRecordsPerPage={setRecordsPerPage}
        totalPages={Math.ceil(data.length / recordsPerPage)}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        tableActions={tableActions()}
        tableHeader={tableHeader()}
        classNames={"admin-theme"}
      />
    </>
  );
};

export default ReportsPage;
