.admin-floating-chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #0D6EFD;
  color: white;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

.admin-floating-chat-widget.rtl {
  right: auto;
  left: 20px;
}

.admin-floating-chat-widget:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.admin-floating-chat-widget.has-new-messages {
  animation: pulse 2s infinite;
}

.widget-icon {
  position: relative;
}

.unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
}

.widget-label {
  font-size: 12px;
  margin-top: 4px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
  }
}

@media (max-width: 768px) {
  .admin-floating-chat-widget {
    bottom: 90px; /* lift above mobile footer navigation */
  }

  /* On mobile, place widget on left for LTR to avoid overlap */
  .admin-floating-chat-widget.ltr {
    left: 20px;
    right: auto;
  }

  /* Place widget on right for RTL */
  .admin-floating-chat-widget.rtl {
    right: 20px;
    left: auto;
  }
}

