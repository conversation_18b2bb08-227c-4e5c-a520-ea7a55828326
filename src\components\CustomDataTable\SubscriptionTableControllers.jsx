import { useState } from 'react';
import { Col, Row } from "react-bootstrap";
import SearchComponent from "./SearchComponent";
import { FaFilterCircleXmark } from "react-icons/fa6";
import { Tooltip } from "react-tooltip";
import { useTranslation } from 'react-i18next';

const SubscriptionTableControllers = ({
    abortController,
    setAbortController,
    fetchData,
    handleFilterSubscriptions,
    setLoading,
    isSearchActive
}) => {
    const { t } = useTranslation();
    const [searchValue, setSearchValue] = useState("");

    const handleClearFilters = () => {
        setSearchValue("");
        if (abortController) abortController.abort();

        const controller = new AbortController();
        setAbortController(controller);
        fetchData(controller);
    };

    const handleSearch = async (term, controller, isCancellation) => {
        if (term !== '' && !isCancellation) {
            try {
                await handleFilterSubscriptions(term, controller);
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error("Search error:", error);
                }
            }
        } else {
            if (abortController) abortController.abort();
            setAbortController(null);

            if (isCancellation && term !== '') {
                await fetchData(new AbortController());
            }
        }
    };

    return (
        <>
            <Row className="justify-content-between align-items-center mb-4">
                <Col lg={9}>
                    <h2 className="text-light mb-0">Subscriptions Management</h2>
                </Col>
                <Col lg={3}>
                    <SearchComponent
                        onSearch={handleSearch}
                        handleFilterLeads={handleFilterSubscriptions}
                        placeholder={t('tableControls.placeholders.searchSubscriptions')}
                        value={searchValue}
                        setValue={setSearchValue}
                        abortController={abortController}
                        setAbortController={setAbortController}
                    />
                </Col>
            </Row>
            <Row className="mt-3 mb-4">
                <Col className="d-flex justify-content-end">
                    <div
                        className="clear-filter"
                        onClick={handleClearFilters}
                        style={{ cursor: 'pointer' }}
                    >
                        <FaFilterCircleXmark size={25} />
                    </div>
                    <Tooltip
                        anchorSelect=".clear-filter"
                        content={t('tableControls.tooltips.clearFilters')}
                    />
                </Col>
            </Row>
        </>
    );
};

export default SubscriptionTableControllers;
