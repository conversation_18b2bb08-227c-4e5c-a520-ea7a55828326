import React from "react";
import { Collapse } from "react-bootstrap";

const CollapsibleSection = ({ title, icon, isOpen, onToggle, children }) => (
    <div
        className={isOpen ? "activity-collapse-btn" : "activity-collapse-btn-collapsed"}
        role={"button"}
    >
        <div
            className={`fw-bold fs-6 d-flex justify-content-between ${isOpen && "mainColor"}`}
            onClick={onToggle}
        >
            <div>{title}</div>
            <div>{icon}</div>
        </div>
        <Collapse in={isOpen}>
            <div id="example-collapse-text" className="mt-3 position-relative">
                {children}
            </div>
        </Collapse>
    </div>
);

export default CollapsibleSection;
