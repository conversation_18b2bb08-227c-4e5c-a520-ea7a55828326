.admin-theme .tickets-table {
  background-color: #2c3e50;
  color: #ecf0f1;
  border-radius: 8px;
  overflow: hidden;
}

.admin-theme .tickets-table thead th {
  background-color: #34495e;
  color: #ecf0f1;
  border-bottom: 2px solid #3498db;
  padding: 12px;
}

.admin-theme .tickets-table tbody tr {
  border-bottom: 1px solid #34495e;
}

.admin-theme .tickets-table tbody tr:hover {
  background-color: #34495e;
}

.admin-theme .sortable-header {
  cursor: pointer;
  user-select: none;
}

.admin-theme .sortable-header:hover {
  background-color: #2980b9;
}

.admin-theme .client-table-row.filter-table-rows:hover td {
  background-color: lightgray;
}

.admin-theme .create-ticket-btn {
  background-color: #2ecc71;
  border-color: #27ae60;
}

.admin-theme .create-ticket-btn:hover {
  background-color: #27ae60;
  border-color: #219653;
}

.admin-theme .view-ticket-btn {
  background-color: #3498db;
  border-color: #2980b9;
}

.admin-theme .view-ticket-btn:hover {
  background-color: #2980b9;
  border-color: #2471a3;
}

.admin-theme .status-counts {
  margin-top: 10px;
}

.admin-theme .no-tickets {
  background-color: #34495e;
  font-style: italic;
}

.admin-theme .tickets-table-container {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Attachment styles */
.attachment-item {
  background-color: #34495e;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.attachment-badge {
  background-color: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* History timeline */
.history-timeline {
  border-left: 2px solid #3498db;
  padding-left: 20px;
  margin-left: 10px;
}

.history-item {
  position: relative;
  margin-bottom: 15px;
  background-color: #34495e;
  border-radius: 4px;
}

.history-item:before {
  content: '';
  position: absolute;
  left: -26px;
  top: 15px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #3498db;
}

/* Dropzone */
.dropzone {
  border: 2px dashed #3498db;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.dropzone:hover {
  border-color: #2ecc71;
}

/* Ensure disabled select elements maintain text visibility */
select:disabled {
  color: #212529 !important;
  opacity: 0.8;
  background-color: #e9ecef;
}

/* Style for the loading spinner container */
.select-loading-indicator {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  z-index: 2;
}

/* Ensure the select container has proper positioning */
.select-container {
  position: relative;
  width: 60%;
  margin: 0 auto;
}
