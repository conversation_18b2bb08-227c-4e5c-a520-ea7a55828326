import { Stack } from "react-bootstrap";
import { ReactSVG } from "react-svg";
import DVLogo from "../../../assets/media/Icons/IntegrationDVLogo.svg";
import integration from "../../../assets/media/animations/integration.json";
import <PERSON><PERSON> from "lottie-react";
import CenteredModal from "../../../components/Shared/modals/CenteredModal/CenteredModal";
import { IoCaretBack } from "react-icons/io5";
import { useNavigate, useParams } from "react-router-dom";
import SuccessModalContent from "./SuccessModalContent";
import useIntegration from "../../../hooks/useIntegration";

const SingleIntegrationPage = () => {
  const {
    integrationData,
    handleSignInWithPlatform,
    setShowCenteredModal,
    showCenteredModal,
  } = useIntegration();
  const params = useParams();
  const id = parseInt(params.id);
  const currentIntegration = integrationData[id - 1];
  const navigate = useNavigate();

  return (
    <>
      <div
        role={"button"}
        onClick={() => navigate(-1)}
        className={"mb-5"}
        title={"Back To Integrations"}
      >
        <IoCaretBack
          color={"#000"}
          className={"bg-white rounded-circle p-1"}
          size={35}
        />
      </div>
      <h2 className={"mb-5 fw-bold text-capitalize"}>
        {currentIntegration?.platform} Integration
      </h2>
      <div className={"content-container"}>
        <Stack
          direction={"horizontal"}
          className={"justify-content-center align-content-center"}
        >
          {currentIntegration?.icon}
          <Lottie
            animationData={integration}
            className={"integration-animation"}
          />
          <ReactSVG src={DVLogo} />
        </Stack>
        <p className={"integration-description my-5"}>
          {currentIntegration?.description}
        </p>
        <div
          className={"submit-btn integration-submit my-5"}
          onClick={() => handleSignInWithPlatform(currentIntegration?.platform)}
        >
          Sign in with {currentIntegration?.platform}
        </div>
        <CenteredModal
          show={showCenteredModal}
          children={
            <SuccessModalContent
              currentIntegration={currentIntegration}
              setShowCenteredModal={setShowCenteredModal}
            />
          }
          onHide={() => setShowCenteredModal(false)}
        />
      </div>
    </>
  );
};

export default SingleIntegrationPage;
