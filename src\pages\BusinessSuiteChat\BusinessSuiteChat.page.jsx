import "./business-chat.css";
import MessagesSide from "./MessagesSide";
import { Col, Dropdown, Form, Row, Tab, Tabs } from "react-bootstrap";
import ChatContent from "./ChatContent";
import { useEffect, useRef, useState } from "react";
import useResponsiveLayout from "./useResponsiveLayout";
import { FaFacebookMessenger, FaInstagram } from "react-icons/fa";
import Cookies from "js-cookie";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "react-toastify";

import { FaWhatsapp } from "react-icons/fa6";
import metaService from "../../services/integrations/meta";

// Import actions and selectors from metaChatSlice
import {
  setActiveFilter,
  setChats,
  setSelectedChat,
  setSelectedPage,
  setMessages,
  setNestedMessages,
  setPageImg,
  setPaginationMeta,
  setLoadingChats as setMetaLoadingChats,
  setHasMore,
  setMessengerChats,
  setInstagramChats,
  resetFilteredChatsPages,
  // Selectors
  selectActiveFilter,
  selectSelectedPage,
  selectChats,
  selectMessengerChats,
  selectInstagramChats,
  selectPaginationMeta,
  selectLoadingPagination,
  selectLoadingChats as selectMetaLoadingChats,
  setWhatsappChats,
  setSelectedPhone,
  setWhatsAppAccounts,
  setSelectedWhatsAccount,
  fetchWhatsAppMessages,
  selectSelectedWhatsAccount,
  selectWhatsAppAccounts,
  selectSelectedPhone,
  selectLoadingChats,
  setLoadingChats,
  fetchWhatsAppLatestMessages,
  handleSelectWhatsAppAccount,
  listenToAllWhatsappMessages,
} from "../../redux/features/metaBusinessChatSlice"; // Adjust path as necessary
import { showErrorToast } from "../../utils/toast-success-error";
import { db } from "../../utils/firebase.config";
import { collection, onSnapshot, orderBy, query } from "firebase/firestore";
import useIntegration from "../../hooks/useIntegration";

const BusinessSuiteChatPage = () => {
  const dispatch = useDispatch();

  // Select state from Redux store for Meta
  const activeFilter = useSelector(selectActiveFilter);
  const selectedPage = useSelector(selectSelectedPage);
  const messengerChats = useSelector(selectMessengerChats);
  const instagramChats = useSelector(selectInstagramChats);
  const paginationMeta = useSelector(selectPaginationMeta);
  const loadingPagination = useSelector(selectLoadingPagination);
  const loadingMetaChats = useSelector(selectLoadingChats);

  // Select state from Redux store for WhatsApp
  const selectedWhatsAccount = useSelector(selectSelectedWhatsAccount);
  console.log("selectedWhatsAccount:", selectedWhatsAccount);

  const whatsAppAccounts = useSelector(selectWhatsAppAccounts);
  console.log("whatsAppAccounts:", whatsAppAccounts);
  const selectedPhone = useSelector(selectSelectedPhone);
  const loadingWhatsappChats = useSelector(selectLoadingChats);

  const { userPages, setUserPages, setIntegratedAlready } = useIntegration();
  const { showMessagesSide, showChatContent, setShowChatContent } =
    useResponsiveLayout();
  const { user } = useSelector((state) => state.auth);
  const accessToken =
    user?.user?.["access-token"] || Cookies.get("access_token");
  const fetchUserPagesAbortController = useRef(null);
  const fetchMessengerChatsAbortController = useRef(null);
  const fetchInstagramChatsAbortController = useRef(null);
  const fetchPageImgAbortController = useRef(null);
  const fetchWhatsappChatsAbortController = useRef(null);
  const [showWhatsappSelect, setShowWhatsappSelect] = useState(false);

  // Start global WhatsApp listener when a phone is selected
  useEffect(() => {
    if (selectedPhone) {
      dispatch(listenToAllWhatsappMessages({ selectedPhone }));
    }
  }, [dispatch, selectedPhone]);

  useEffect(() => {
    const checkAccessToken = async () => {
      if (accessToken) return;
      const response = await metaService.getAccessTokenApi();
      if (response.data && response.data !== accessToken) {
        Cookies.remove("userPages");
      }
      if (response?.data.length !== 0) {
        Cookies.set("access_token", response.data);
        setIntegratedAlready(true);
      } else {
        Cookies.remove("access_token");
        setIntegratedAlready(false);
      }
    };

    checkAccessToken();

    const fetchUserPages = async () => {
      if (!accessToken || userPages.length !== 0) return;

      if (fetchUserPagesAbortController.current) {
        fetchUserPagesAbortController.current.abort();
      }
      fetchUserPagesAbortController.current = new AbortController();
      const signal = fetchUserPagesAbortController.current.signal;

      try {
        const response = await metaService.getUserPagesApi({ signal });
        setUserPages(response);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log("Fetch user pages request aborted");
        } else {
          console.error("Error fetching user pages:", error);
        }
      }
    };
    fetchUserPages();
  }, [accessToken]);

  useEffect(() => {
    return () => {
      dispatch(resetFilteredChatsPages());
    };
  }, []);

  const fetchMessengerChats = async (page) => {
    if (fetchMessengerChatsAbortController.current) {
      fetchMessengerChatsAbortController.current.abort();
    }
    fetchMessengerChatsAbortController.current = new AbortController();
    const signal = fetchMessengerChatsAbortController.current.signal;

    try {
      dispatch(setLoadingChats(true));
      dispatch(setHasMore(true));
      const result = await metaService.getMessengerChatsForPageApi({
        page_id: page?.page_id,
        access_token: page?.page_token,
        signal,
      });
      if (result?.data) {
        const { data, insta_next, messanger_next } = result.data;
        dispatch(
          setPaginationMeta([
            insta_next || paginationMeta[0],
            messanger_next || paginationMeta[1],
          ])
        );
        const sortedChats = data?.sort(
          (a, b) =>
            new Date(b.updated_time).getTime() -
            new Date(a.updated_time).getTime()
        );
        dispatch(setActiveFilter("messenger"));
        dispatch(setMessengerChats(sortedChats));
        dispatch(setMessages([]));
        dispatch(setSelectedChat(null));
      } else {
        console.error("No data in API result:", result);
      }
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch chats request aborted");
      } else {
        if (error?.response?.status === 504) {
          dispatch(setSelectedPage(null));
          showErrorToast("There is a problem with your network connection.");
        }
        console.error("Error fetching chats:", error);
      }
    } finally {
      dispatch(setLoadingChats(false));
    }
  };

  const fetchInstagramChats = async (page) => {
    if (fetchInstagramChatsAbortController.current) {
      fetchInstagramChatsAbortController.current.abort();
    }
    fetchInstagramChatsAbortController.current = new AbortController();
    const signal = fetchInstagramChatsAbortController.current.signal;

    try {
      dispatch(setLoadingChats(true));
      dispatch(setHasMore(true));
      const result = await metaService.getInstagramChatsForPageApi({
        page_id: page?.page_id,
        access_token: page?.page_token,
        signal,
      });
      if (result?.data) {
        const { data, insta_next, messanger_next } = result.data;
        dispatch(
          setPaginationMeta([
            insta_next || paginationMeta[0],
            messanger_next || paginationMeta[1],
          ])
        );
        const sortedChats = data?.sort(
          (a, b) =>
            new Date(b.updated_time).getTime() -
            new Date(a.updated_time).getTime()
        );
        dispatch(setActiveFilter("instagram"));
        dispatch(setInstagramChats(sortedChats));
        dispatch(setMessages([]));
        dispatch(setSelectedChat(null));
      } else {
        console.error("No data in API result:", result);
      }
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch chats request aborted");
      } else {
        if (error?.response?.status === 504) {
          dispatch(setSelectedPage(null));
          showErrorToast("There is a problem with your network connection.");
        }
        console.error("Error fetching chats:", error);
      }
    } finally {
      dispatch(setLoadingChats(false));
    }
  };

  const fetchWhatsappChats = async (phone) => {
    if (fetchWhatsappChatsAbortController.current) {
      fetchWhatsappChatsAbortController.current.abort();
    }
    fetchWhatsappChatsAbortController.current = new AbortController();
    const signal = fetchWhatsappChatsAbortController.current.signal;

    try {
      dispatch(setLoadingChats(true));
      const result = await metaService.getWhatsAppChatsForPageApi({
        id: phone?.id,
        signal,
      });

      if (result?.data) {
        dispatch(setActiveFilter("whatsapp"));
        dispatch(setWhatsappChats(result?.data));
        dispatch(setMessages([]));
        dispatch(setSelectedChat(null));
      } else {
        console.error("No data in API result:", result);
      }
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch chats request aborted");
      } else {
        if (error?.response?.status === 504) {
          dispatch(setSelectedPage(null));
          showErrorToast("There is a problem with your network connection.");
        }
        console.error("Error fetching chats:", error);
      }
    } finally {
      dispatch(setLoadingChats(false));
    }
  };

  const fetchWhatsappAccounts = async () => {    try {
      const result = await metaService.getWhatsAppAccounts(accessToken);
      dispatch(
        setWhatsAppAccounts(result?.owned_whatsapp_business_accounts?.data)
      );

      if (!result?.owned_whatsapp_business_accounts) {
        toast.warn("You don't have Whatsapp Business account", {
          position: "bottom-right",
          theme: "dark",
        });
      }
    } catch (error) {
      console.error("Error fetching WhatsApp accounts:", error);
      toast.error("Error fetching WhatsApp accounts", {
        position: "bottom-right",
        theme: "dark",
      });
    }
  };

  const fetchPageImg = async (page) => {
    if (fetchPageImgAbortController.current) {
      fetchPageImgAbortController.current.abort();
    }
    fetchPageImgAbortController.current = new AbortController();
    const signal = fetchPageImgAbortController.current.signal;

    try {
      const response = await metaService.getPageImgAPI({
        page_id: page?.page_id,
        access_token: page?.page_token,
        signal,
      });
      dispatch(
        setPageImg(
          response?.picture ? response?.picture?.data?.url : response?.data
        )
      );
      Cookies.set(
        "page_img",
        response?.picture ? response?.picture?.data?.url : response?.data
      );
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch page image request aborted");
      } else {
        console.error("Error fetching page image:", error);
      }
    }
  };

  useEffect(() => {
    if (!selectedPage?.page_id) return;

    const chatsCollectionRef = collection(db, "chats");

    const unsubscribe = onSnapshot(chatsCollectionRef, (snapshot) => {
      if (activeFilter === "messenger") {
        setMessengerChats((prevChats) => {
          let updatedChats = [...prevChats];

          snapshot.docChanges().forEach((change) => {
            const messageData = change.doc.data();

            // Find the chat that contains both the sender and recipient
            const chatIndex = updatedChats.findIndex((chat) => {
              const participants = chat.participants.data;
              const participantIds = participants.map((p) => p.id);
              return (
                participantIds.includes(messageData.sender) &&
                participantIds.includes(messageData.recipient)
              );
            });

            if (chatIndex !== -1 && change.type === "modified") {
              updatedChats[chatIndex] = {
                ...updatedChats[chatIndex],
                updated_time:
                  messageData.created_time.replace(" ", "T") + "+0000",
                message_count: (updatedChats[chatIndex].message_count || 0) + 1,
              };
            }
          });

          // Sort chats by updated_time
          return updatedChats.sort(
            (a, b) =>
              new Date(b.updated_time).getTime() -
              new Date(a.updated_time).getTime()
          );
        });
      } else if (activeFilter === "instagram") {
        setInstagramChats((prevChats) => {
          let updatedChats = [...prevChats];

          snapshot.docChanges().forEach((change) => {
            const chatData = change.doc.data();
            const chatId = change.doc.id;
            if (change.type === "modified") {
              const chatIndex = updatedChats.findIndex(
                (chat) => chat.id === chatId
              );
              if (chatIndex !== -1) {
                updatedChats[chatIndex] = {
                  ...updatedChats[chatIndex],
                  ...chatData,
                  updated_time: chatData.created_time, // Use the message creation time as chat update time
                };
              }
            }
          });

          // Sort chats by updated_time
          return updatedChats.sort(
            (a, b) =>
              new Date(b.updated_time).getTime() -
              new Date(a.updated_time).getTime()
          );
        });
      }
    });

    return () => {
      unsubscribe();
      dispatch(setActiveFilter("all"));
      dispatch(setSelectedChat(null));
      dispatch(setMessages([]));
      dispatch(setPaginationMeta(["", ""]));
    };
  }, [selectedPage?.page_id]);

  useEffect(() => {
    if (!selectedWhatsAccount?.id) return;

    const chatsCollectionRef = query(
      collection(db, "whatsApp"),
      orderBy("created_time", "asc")
    );

    const unsubscribe = onSnapshot(chatsCollectionRef, (snapshot) => {
      setWhatsappChats((prevChats) => {
        let updatedChats = [...prevChats];

        snapshot.docChanges().forEach((change) => {
          const chatData = change.doc.data();
          const chatId = change.doc.id;
          if (change.type === "modified") {
            const chatIndex = updatedChats.findIndex(
              (chat) => chat.id === chatId
            );
            if (chatIndex !== -1) {
              updatedChats[chatIndex] = {
                ...updatedChats[chatIndex],
                ...chatData,
                updated_time: chatData.created_time, // Use the message creation time as chat update time
              };
            }
          }
        });

        // Sort chats by updated_time
        return updatedChats.sort(
          (a, b) =>
            new Date(b.updated_time).getTime() -
            new Date(a.updated_time).getTime()
        );
      });
    });

    return () => {
      unsubscribe();
      dispatch(setActiveFilter("all"));
      dispatch(setSelectedChat(null));
      dispatch(setMessages([]));
      dispatch(setPaginationMeta(["", ""]));
    };
  }, [selectedWhatsAccount?.id]);

  const handleFilterChange = (filter) => {
    dispatch(setActiveFilter(filter));
    dispatch(setSelectedChat(null));
    if (filter === "all") {
    } else if (filter === "messenger" && selectedPage?.page_id) {
      if (messengerChats.length === 0) {
        setShowWhatsappSelect(false);
        fetchMessengerChats(selectedPage);
      }
    } else if (filter === "instagram" && selectedPage?.page_id) {
      if (instagramChats.length === 0) {
        setShowWhatsappSelect(false);
        fetchInstagramChats(selectedPage);
      }
    } else if (filter === "whatsapp") {
      // if(whatsappChats.length === 0){
      //   fetchWhatsappChats(selectedPage);
      // }
      setShowWhatsappSelect(true);
      if (whatsAppAccounts.length === 0) {
        fetchWhatsappAccounts();
      }
    } else {
      setShowWhatsappSelect(false);
    }
  };

  return (
    <div className={"business-chat content-container px-3 py-0 pt-3"}>
      <Row
        className={
          "flex-column flex-md-row justify-content-between align-items-center"
        }
      >
        <Col lg={8} md={12}>
          <Tabs
            id="filter-tabs"
            activeKey={activeFilter}
            onSelect={(filter) => handleFilterChange(filter)}
            className="mb-3 meta-tab-custom"
            variant="pills"
          >
            {/*<Tab*/}
            {/*    eventKey="all"*/}
            {/*    title={*/}
            {/*      <div>All</div>*/}
            {/*    }*/}
            {/*    tabClassName={activeFilter === "all" ? "all-tab" : ""}*/}
            {/*    disabled={loadingPagination || loadingChats || !selectedPage}*/}
            {/*/>*/}
            <Tab
              eventKey="messenger"
              title={
                <div className="d-flex justify-content-between align-items-center align-content-center">
                  <FaFacebookMessenger
                    className={"p-1 rounded-circle"}
                    color={"white"}
                    style={{
                      backgroundColor: "rgb(0, 132, 255)",
                      width: 25,
                      height: 25,
                    }}
                  />
                  <div className="ms-2">Messenger</div>
                </div>
              }
              tabClassName={activeFilter === "messenger" ? "messenger-tab" : ""}
              // disabled={loadingPagination || loadingChats || !selectedPage}
            />
            <Tab
              eventKey="instagram"
              title={
                <div className="d-flex justify-content-between align-items-center">
                  <FaInstagram
                    className={"p-1 rounded-circle"}
                    color={"white"}
                    style={{
                      background:
                        "linear-gradient(135.00deg, rgb(250, 225, 0) 14.645%,rgb(252, 183, 32) 25.251%,rgb(255, 121, 80) 35.858%,rgb(255, 28, 116) 50%,rgb(108, 28, 209) 85.355%)",
                      width: 25,
                      height: 25,
                    }}
                  />
                  <div className="ms-2">Instagram</div>
                </div>
              }
              tabClassName={activeFilter === "instagram" ? "instagram-tab" : ""}
              // disabled={loadingPagination || loadingChats || !selectedPage}
            />
            <Tab
              eventKey="whatsapp"
              title={
                <div className="d-flex justify-content-between align-items-center">
                  <FaWhatsapp
                    className={"p-1 rounded-circle"}
                    color={"white"}
                    style={{
                      backgroundColor: "rgb(41, 167, 26)",
                      width: 25,
                      height: 25,
                    }}
                  />
                  <div className="ms-2">WhatsApp</div>
                </div>
              }
              tabClassName={activeFilter === "whatsapp" ? "whatsapp-tab" : ""}
              // disabled={loadingPagination || loadingChats}
            />
          </Tabs>
        </Col>
        <Col lg={4} md={12} className={"mb-3"}>
          {showWhatsappSelect ? (
            <>
              <div className={"text-center text-lg-start"}>
                Select a Whatsapp to view its chats:
              </div>
              <Dropdown className="rounded-pill d-flex justify-content-end">
                <Dropdown.Toggle
                  variant="light"
                  id="whatsapp-dropdown"
                  disabled={loadingMetaChats}
                >
                  {selectedWhatsAccount?.name || "Select a Whatsapp"}
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  {whatsAppAccounts && whatsAppAccounts.length > 0 ? (
                    whatsAppAccounts.map((account) => (
                      <div className={"border-bottom py-2"} key={account.id}>
                        <Dropdown.Item disabled>
                          <div>{account.name}</div>
                        </Dropdown.Item>
                        {account?.phone_numbers?.data &&
                        account.phone_numbers.data.length > 0 ? (
                          account.phone_numbers.data.map((phone) => (
                            <Dropdown.Item
                              key={phone.id}
                              onClick={() => {
                                console.log(
                                  "Selecting WhatsApp account:",
                                  account.name,
                                  "Phone:",
                                  phone.display_phone_number
                                );
                                dispatch(
                                  handleSelectWhatsAppAccount({
                                    whatsAppAccount: account,
                                    phone,
                                  })
                                );
                                dispatch(setSelectedPhone(phone));
                                // Clear existing WhatsApp chats before fetching new ones
                                dispatch(setWhatsappChats([]));
                                fetchWhatsappChats(phone);
                                // Start listening for WhatsApp chat updates after selecting a phone number
                                dispatch(
                                  fetchWhatsAppLatestMessages({
                                    selectedPhone: phone,
                                  })
                                );
                                dispatch(setMessengerChats([]));
                                dispatch(setInstagramChats([]));
                                dispatch(setSelectedChat(null));
                                setShowChatContent(false);
                              }}
                            >
                              {phone?.display_phone_number}
                            </Dropdown.Item>
                          ))
                        ) : (
                          <Dropdown.Item disabled>
                            No phone numbers available
                          </Dropdown.Item>
                        )}
                      </div>
                    ))
                  ) : (
                    <Dropdown.Item disabled>
                      No WhatsApp accounts available
                    </Dropdown.Item>
                  )}
                </Dropdown.Menu>
              </Dropdown>
            </>
          ) : (
            <>
              <div className={"text-center text-lg-start"}>
                Select a page to view its chats:
              </div>
              <div className="d-flex justify-content-end">
                <Form.Select
                  className={"rounded-pill"}
                  aria-label="Default select example"
                  value={
                    selectedPage?.page_id ? selectedPage.page_id.toString() : ""
                  }
                  style={{
                    width: "auto",
                    minWidth: "200px",
                    maxWidth: "100%",
                  }}
                  disabled={loadingMetaChats}
                  onChange={(e) => {
                    const selectedPageId = e.target.value;
                    const page = userPages?.find(
                      (page) =>
                        page?.page_id?.toString() === selectedPageId?.toString()
                    );
                    if (page) {
                      dispatch(setSelectedPage(page));
                      dispatch(setMessengerChats([]));
                      dispatch(setInstagramChats([]));
                      dispatch(setSelectedChat(null));
                      setShowChatContent(false);
                      // fetchChats(page);
                      if (activeFilter === "messenger") {
                        fetchMessengerChats(page);
                      } else if (activeFilter === "instagram") {
                        fetchInstagramChats(page);
                      }
                      fetchPageImg(page);
                    }
                  }}
                >
                  <option>Select a page</option>
                  {userPages?.map((page) => (
                    <option
                      key={page?.page_id}
                      value={page?.page_id?.toString()}
                    >
                      {page?.page_name}
                    </option>
                  ))}
                </Form.Select>
              </div>
            </>
          )}
        </Col>
      </Row>

      {/*{activeFilter === "whatsapp" ? (*/}
      {/*  <Row style={{ borderTop: "1px solid rgb(211, 211, 211)" }}>*/}
      {/*    {showWhatsAppAccountsSide && (*/}
      {/*      <Col*/}
      {/*        sm={showWhatsAppAccountsContent ? 4 : 12}*/}
      {/*        className={"p-0"}*/}
      {/*        style={{*/}
      {/*          borderRight: "1px solid #D3D3D3",*/}
      {/*          minHeight: "600px",*/}
      {/*          transition: "width 0.5s ease-in-out",*/}
      {/*        }}*/}
      {/*      >*/}
      {/*        <WhatsAppAccountsSide />*/}
      {/*      </Col>*/}
      {/*    )}*/}
      {/*    {showWhatsAppAccountsContent && (*/}
      {/*      <Col*/}
      {/*        sm={showWhatsAppAccountsSide ? 8 : 12}*/}
      {/*        className={"p-0 position-relative"}*/}
      {/*        style={{ transition: "width 0.5s ease-in-out" }}*/}
      {/*      >*/}
      {/*        <WhatsAppAccountsContent />*/}
      {/*      </Col>*/}
      {/*    )}*/}
      {/*  </Row>*/}
      {/*) : (*/}
      <Row style={{ borderTop: "1px solid rgb(211, 211, 211)" }}>
        {showMessagesSide && (
          <Col
            sm={showChatContent ? 4 : 12}
            className={"p-0"}
            style={{
              borderRight: "1px solid #D3D3D3",
              minHeight: "600px",
              transition: "width 0.5s ease-in-out",
            }}
          >
            <MessagesSide />
          </Col>
        )}
        {showChatContent && (
          <Col
            sm={showMessagesSide ? 8 : 12}
            className={"p-0 position-relative"}
            style={{ transition: "width 0.5s ease-in-out" }}
          >
            <ChatContent />
          </Col>
        )}
      </Row>
      {/*)}*/}
    </div>
  );
};

export default BusinessSuiteChatPage;
