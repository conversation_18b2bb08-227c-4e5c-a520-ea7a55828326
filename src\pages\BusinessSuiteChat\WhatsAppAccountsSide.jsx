import { Col, Row } from "react-bootstrap";
// import { FaUserCircle } from "react-icons/fa";
// import { ReactSVG } from "react-svg";
// import instagram from "../../assets/media/Icons/icons8-instagram.svg";
// import { TbExclamationMark } from "react-icons/tb";
// import messenger from "../../assets/media/Icons/messenger.svg";
import { FaWhatsapp } from "react-icons/fa6";
import { useEffect } from "react";
import metaService from "../../services/integrations/meta";
import { useDispatch, useSelector } from "react-redux";
import {
  selectWhatsAppAccounts,
  selectSelectedWhatsAccount,
  selectActiveFilter,
  setWhatsAppAccounts,
  handleSelectWhatsAppAccount,
} from "../../redux/features/metaBusinessChatSlice";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

const WhatsAppAccountsSide = () => {
  const {
    whatsAppAccounts,
    setWhatsAppAccounts,
    selectedWhatsAccount,
    handleSelectWhatsAppAccount,
    activeFilter,
  } = useMetaBusinessChatContext();
  const { user } = useSelector((state) => state.auth);
  const accessToken =
    user?.user?.["access-token"] || Cookies.get("access_token");
  useEffect(() => {
    if (activeFilter === "whatsapp") {
      const fetchWhatsappAccounts = async () => {
        const result = await metaService.getWhatsAppAccounts(accessToken);
        setWhatsAppAccounts(result?.owned_whatsapp_business_accounts?.data);
        if (!result?.owned_whatsapp_business_accounts) {
          toast.warn("You don't have Whatsapp Business account", {
            position: "bottom-right",
            theme: "dark",
          });
        }
      };
      fetchWhatsappAccounts();
    }
  }, [activeFilter]);

  return (
    <div className={"d-flex justify-content-between flex-column"}>
      <div className={"chat-container px-3"}>
        {whatsAppAccounts?.length > 0
          ? whatsAppAccounts?.map((chat) => (
              <Row
                key={chat?.id}
                className={`my-3 py-2 single-chat-container ${selectedWhatsAccount?.id === chat?.id ? "selectedWhats-chat" : null}`}
                onClick={() => handleSelectWhatsAppAccount(chat)}
              >
                <Col lg={2}>
                  {chat?.image === "dummy" || !chat.image ? (
                    <div
                      className={`text-center p-2 rounded-circle ${selectedWhatsAccount?.id === chat?.id ? "bg-white" : "bg-grey-suit"}`}
                      style={{ width: 45, height: 45 }}
                    >
                      <FaWhatsapp
                        className={"text-center"}
                        color={"gray"}
                        size={30}
                      />
                    </div>
                  ) : (
                    <img
                      width={60}
                      height={60}
                      src={chat?.image}
                      alt={"User"}
                      className={"integration-account rounded-circle"}
                    />
                  )}
                </Col>
                <Col lg={10}>
                  <div className={"d-flex flex-column"}>
                    <div>{chat?.name}</div>
                    ID: {chat?.id}
                  </div>
                </Col>
              </Row>
            ))
          : null}
      </div>
    </div>
  );
};

export default WhatsAppAccountsSide;
