import { useMemo, useState, useEffect } from "react";
import {
  useAsyncDebounce,
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import { Col, Form, InputGroup, Pagination, Row, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { BiSearch } from "react-icons/bi";
import { useSelector, useDispatch } from "react-redux";
import {
  handleExportClientsThunk,
  handleExportLeadsThunk,
  handleImportLeadsThunk,
} from "../../redux/features/clientSlice";
import { FaFilterCircleXmark, FaUserPlus } from "react-icons/fa6";
import { Tooltip } from "react-tooltip";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import ImportLeadsDropZone from "../Modals/ImportLeadsModal";
import { PiExportBold } from "react-icons/pi";
import FetchingDataAdmin from "../LoadingAnimation/FetchingDataAdmin";
import CreateClientAdminModal from "../Modals/CreateClientAdmin";
import AdminPaginationComponent from "../AdminPagination/AdminPaginationComponent";

function GlobalFilter({
  preGlobalFilteredRows,
  globalFilter,
  setGlobalFilter,
  clearSignal, // new prop
}) {
  const count = preGlobalFilteredRows.length;
  const [value, setValue] = useState(globalFilter);

  useEffect(() => {
    setValue(""); // Reset input when clearSignal changes
  }, [clearSignal]);

  const onChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 200);

  return (
    <div className={"position-relative"}>
      <InputGroup>
        <Form.Control
          aria-label="Default"
          aria-describedby="inputGroup-sizing-default"
          value={value || ""}
          onChange={(e) => {
            setValue(e.target.value);
            onChange(e.target.value);
          }}
          placeholder={`${count} records...`}
          className={"search-input"}
        />
      </InputGroup>
      <div className={"search-icon"}>
        <BiSearch color={"#000"} size={20} />
      </div>
    </div>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const ClientsDataTable = ({
  columns,
  data,
  loading,
  classNames,
  setCurrentPage,
  currentPage,
  recordsPerPage,
  setRecordsPerPage,
  totalPages,
}) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const handleClose = () => setShowCreateModal(false);
  const handleShow = () => setShowCreateModal(true);
  const dispatch = useDispatch();
  const handleExportClients = () => dispatch(handleExportClientsThunk());
  const [showImportLeadModal, setShowImportLeadModal] = useState(false);
  const defaultColumn = useMemo(
    () => ({
      // Default Filter UI
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setFilter,
    canPreviousPage,
    canNextPage,
    gotoPage,
    setPageSize,
    rows,
    state: { pageIndex, pageSize, globalFilter },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const [clearSignal, setClearSignal] = useState(0); // new state

  const handleClearFilters = () => {
    setGlobalFilter("");
    columns.forEach((column) => {
      if (column.accessor) {
        setFilter(column.accessor, undefined);
      }
    });
    setClearSignal((prev) => prev + 1); // trigger input reset
  };

  const handlePageChange = (pageIndex) => {
    setCurrentPage(pageIndex + 1);
    gotoPage(pageIndex);
  };

  const handlePageSizeChange = (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
  };
  return loading ? (
    <FetchingDataAdmin className={"admin-theme"} />
  ) : (
    <>
      <CenteredModal
        size={"lg"}
        show={showImportLeadModal}
        children={
          <ImportLeadsDropZone
            handleClose={() => setShowImportLeadModal(false)}
          />
        }
        onHide={() => setShowImportLeadModal(false)}
      />
      <div className={`all-leads-table ${classNames}`}>
        <Row className={"m-4"}>
          <Row>
            <Col lg={6}>
              <GlobalFilter
                preGlobalFilteredRows={preGlobalFilteredRows}
                globalFilter={globalFilter}
                setGlobalFilter={setGlobalFilter}
                clearSignal={clearSignal} // pass clearSignal
              />
            </Col>
            <Col
              className={
                "d-flex justify-content-center justify-content-lg-end align-items-center my-2 my-lg-0 text-white"
              }
              lg={6}
            >
              <div
                className={"clear-filter shadow-lg p-2"}
                role={"button"}
                onClick={handleClearFilters}
              >
                <FaFilterCircleXmark size={25} />
              </div>
              <span
                className={"export-leads mx-3 shadow-lg p-2"}
                role={"button"}
                onClick={handleExportClients}
              >
                <PiExportBold size={25} />
              </span>
              <div
                className={"add-lead shadow-lg p-2"}
                role={"button"}
                onClick={handleShow}
              >
                <FaUserPlus size={25} />
              </div>
              <Tooltip
                anchorSelect=".clear-filter"
                content="Clear search and filters."
                className={"bg-white text-dark"}
              />
              <Tooltip
                anchorSelect=".add-lead"
                content="Add a new Client."
                className={"bg-white text-dark"}
              />
              <Tooltip
                anchorSelect=".export-leads"
                content="Export Clients to csv, xsxl file."
                className={"bg-white text-dark"}
              />
              <CreateClientAdminModal
                show={showCreateModal}
                onHide={handleClose}
                className={"admin-theme"}
              />
            </Col>
          </Row>
        </Row>
        <Table
          responsive
          className="table text-center position-relative table-hover"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataAdmin className={"admin-theme"} />
          ) : (
            <>
              <thead>
                {headerGroups.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        {/* Render the columns filter UI */}
                        {/*<div>{column.canFilter ? column.render('Filter') : null}</div>*/}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      key={row.original.id}
                    >
                      {row.cells.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <AdminPaginationComponent
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={recordsPerPage}
          onPageChange={(page) => {
            setCurrentPage(page);
          }}
          onItemsPerPageChange={(size) => {
            setRecordsPerPage(size);
            setCurrentPage(1);
          }}
          itemsPerPageOptions={[10, 20, 30, 40, 50]}
        />
      </div>
    </>
  );
};

export default ClientsDataTable;
