import { Dropdown } from "react-bootstrap";
import TeamMembersTableComponent from "../../components/TeamMembers/TeamMembersTable.component";
import { useEffect, useMemo, useState } from "react";
import Form from "react-bootstrap/Form";
import "./Team.css";
import { HiDotsVertical } from "react-icons/hi";
import { HiMiniArrowUturnRight, HiOutlineXMark } from "react-icons/hi2";
import { FiEdit } from "react-icons/fi";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import EditPermissionsModal from "../../components/TeamMembers/EditPermissionsModal";
import { MdEdit } from "react-icons/md";
import { PiTrashFill } from "react-icons/pi";
import { Link } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  setTeamMembers,
  deleteTeamMember,
  setDisableAddMember,
} from "../../redux/features/clientSlice";
import updateTeamMemberApi from "../../services/teams/update-team-member.api";
import getAllTeamMembers from "../../services/teams/get-teams.api";
import DeleteTeamMemberModal from "../../components/Modals/DeleteTeamMemberModal";
import { useTranslation } from "react-i18next";
import { showSuccessToast } from "../../utils/toast-success-error";

const TeamPage = () => {
  const { t } = useTranslation();
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const teamMembers = useSelector((state) => state.client.teamMembers);
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);
  const { currentUserPermissions } = useSelector((state) => state.auth);
  useEffect(() => {
    setLoading(true);
    const fetchData = async () => {
      const teamsData = await getAllTeamMembers();
      dispatch(setTeamMembers(teamsData?.data?.members || []));
      dispatch(setDisableAddMember(teamsData?.data?.quota === 0));
      setLoading(false);
    };
    fetchData();
    setLoading(false);
  }, []);
  const columns = useMemo(
    () => [
      {
        Header: t("team.columns.id"),
        accessor: "clientId",
        Cell: ({ row }) => <span>{row.index + 1}</span>,
      },
      {
        Header: t("team.columns.name"),
        accessor: "name",
        Cell: ({ row }) => (
          <>
            <Link
              to={`/team/${row.original.id}`}
              className={"text-decoration-none team-name-cell"}
            >
              {row.original.name}
            </Link>
          </>
        ),
      },
      {
        Header: t("team.columns.status"),
        accessor: "status",
        Cell: ({ row }) => (
          <Form.Check
            type="switch"
            id={`custom-switch-${row.original.id}`}
            checked={row.original.status === "active"}
            className={"members-status-switch d-flex justify-content-center"}
            onChange={async () => {
              const updatedStatus =
                row.original.status === "active" ? "deactive" : "active";
              const response = await updateTeamMemberApi(
                { status: updatedStatus },
                row.original.id
              );
              showSuccessToast(t("team.toast.updateSuccess"));
              const currentTeamMembers = Array.isArray(teamMembers)
                ? [...teamMembers]
                : [];
              const updatedTeamMembers = currentTeamMembers.map((item) =>
                item.id === row.original.id
                  ? { ...item, ...response.data.Member }
                  : item
              );
              dispatch(setTeamMembers(updatedTeamMembers));
            }}
          />
        ),
      },
      {
        Header: t("team.columns.role"),
        accessor: "role",
        Cell: ({ row }) => {
          const Role = row.original.role;
          let roleLabel;
          switch (Role) {
            case 0:
            case 1:
              roleLabel = t("team.roles.admin");
              break;
            case 2:
              roleLabel = t("team.roles.moderator");
              break;
            case 3:
              roleLabel = t("team.roles.sales");
              break;
            case 4:
              roleLabel = t("team.roles.accountant");
              break;
            case 5:
              roleLabel = t("team.roles.teamMember");
              break;
            default:
              roleLabel = t("team.roles.moderator");
          }
          return (
            <div className={"d-flex justify-content-center align-items-center"}>
              <div className={"shadow-sm rounded-2 p-1"}>{roleLabel}</div>
            </div>
          );
        },
      },
      {
        Header: t("team.columns.actions"),
        Cell: ({ row }) => (
          <div className={"d-flex justify-content-center align-items-center"}>
            {currentUserPermissions?.includes("member-edit") ? (
              <Link
                to={`/team/${row.original.id}`}
                className={"me-3 shadow-sm rounded-2 p-1"}
              >
                <MdEdit size={20} className={"text-dark"} />
              </Link>
            ) : null}
            {currentUserPermissions?.includes("member-delete") ? (
              <div className={"me-3 shadow-sm rounded-2 p-1"}>
                <PiTrashFill
                  onClick={() => {
                    setShowDeleteModal(true);
                    setIdToDelete(row.original.id);
                  }}
                  size={20}
                  className={"text-danger"}
                />
              </div>
            ) : null}
            {currentUserPermissions?.includes("member-edit") ? (
              <Dropdown>
                <Dropdown.Toggle
                  variant="light"
                  id="dropdown-basic"
                  className={"team-actions-button"}
                >
                  <HiDotsVertical />
                </Dropdown.Toggle>

                <Dropdown.Menu className={"team-actions-menu"}>
                  <Dropdown.Item
                    className={
                      "d-flex justify-content-between align-items-center"
                    }
                  >
                    Resend Invite
                    <HiMiniArrowUturnRight />
                  </Dropdown.Item>
                  <Dropdown.Item
                    className={
                      "d-flex justify-content-between align-items-center text-secondary"
                    }
                    onClick={() => setShowCenteredModal(true)}
                  >
                    Edit Permission <FiEdit />
                  </Dropdown.Item>
                  <Dropdown.Item
                    className={
                      "d-flex justify-content-between align-items-center text-danger fw-bold"
                    }
                  >
                    Cancel <HiOutlineXMark />
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            ) : null}
          </div>
        ),
      },
    ],
    [teamMembers, setTeamMembers, currentUserPermissions, t]
  );
  // Ensure teamMembers is an array before mapping
  const indexedData = Array.isArray(teamMembers)
    ? teamMembers.map((item, index) => ({
        ...item,
        clientId: index + 1,
      }))
    : [];
  return (
    <>
      <TeamMembersTableComponent
        data={indexedData}
        columns={columns}
        loading={loading}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <EditPermissionsModal
            handleClose={() => setShowCenteredModal(false)}
          />
        }
        onHide={() => setShowCenteredModal(false)}
      />

      <CenteredModal
        show={showDeleteModal}
        children={
          <DeleteTeamMemberModal
            title={t("team.deleteModal.title")}
            deleteMemberFunction={(params) =>
              dispatch(deleteTeamMember(params))
            }
            id={idToDelete}
            handleClose={() => setShowDeleteModal(false)}
          />
        }
        onHide={() => setShowDeleteModal(false)}
      />
    </>
  );
};

export default TeamPage;
