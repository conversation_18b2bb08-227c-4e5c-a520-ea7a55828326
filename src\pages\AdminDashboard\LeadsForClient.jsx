import { useSelector, useDispatch } from "react-redux";
import {
  setLeads,
  setLeadStatusCounts,
} from "../../redux/features/clientSlice";
import { useEffect, useMemo, useState } from "react";
import leadService from "../../services/leads";
import filterLeadsApi from "../../services/leads";
import { IoSparklesSharp } from "react-icons/io5";
import { ReactSVG } from "react-svg";
import { useParams } from "react-router-dom";
import { format, parseISO } from "date-fns";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import DeleteLeadFromTable from "../../components/Modals/DeleteLeadFromTable";
import clientService from "../../services/clients";
import LeadsDataTableAdmin from "../../components/CustomDataTable/LeadsDataTableAdmin";
import "../../components/ClientsTabsContent/AllClients/Clients.css";
import { sourceToIcon } from "../../constants/sourceIcons";
import { IoCaretBack } from "react-icons/io5";
import { useLocation, useNavigate } from "react-router-dom";
import NavigateBackComponent from "./NavigateBack.component";

const LeadsForClient = () => {
  const dispatch = useDispatch();
  const { leads, leadStatusCounts } = useSelector((state) => state.client);
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);
  const [loading, setLoading] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const { id } = useParams();
  const [clientDetails, setClientDetails] = useState({});
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const clientData = await clientService.showClientDetails(
          id,
          null,
          null,
          null,
          null,
          null,
          null,
          "Fetching client details failed"
        );
        setClientDetails(clientData.data);

        if (clientData?.data?.name) {
          navigate(location.pathname, {
            state: { clientName: clientData.data.name },
            replace: true,
          });
        }
      } catch (error) {
        // Notification handled by apiRequest
      }
      setLoading(false);
    };
    fetchData();
  }, [id]);
  useEffect(() => {
    if (!user) {
      return;
    }
    const fetchInitialData = async () => {
      setLoading(true);
      try {
        const leadsData = await leadService.getAllLeadsApi(
          id,
          recordsPerPage,
          currentPage,
          null,
          null,
          null,
          null,
          "An error occurred fetching initial data"
        );
        if (leadsData) {
          setTotalPages(leadsData.data["Number Of Pages"]);
          setCurrentPage(leadsData.data["Page Number"]);
          dispatch(setLeads(leadsData.data.Leads));
          if (
            leadsData.data.completed ||
            leadsData.data.inprogress ||
            leadsData.data.pendding ||
            leadsData.data.rejected
          ) {
            dispatch(
              setLeadStatusCounts({
                completed: leadsData?.data?.completed || 0,
                assigned: leadsData?.data?.assigned || 0,
                inprogress: leadsData?.data?.inprogress || 0,
                pendding: leadsData?.data?.pendding || 0,
                rejected: leadsData?.data?.rejected || 0,
              })
            );
          }
        }
      } catch (error) {
        // Notification handled by apiRequest
      }
      setLoading(false);
    };
    fetchInitialData();
  }, [user, currentPage, recordsPerPage, id]);
  const handleFilterStatus = async (status) => {
    setLoading(true);
    if (status === "all") {
      const res = await leadService.getAllLeadsApi(
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "Error fetching all leads"
      );
      if (res && res.data) {
        let leadsArray = [];
        if (res.data && Array.isArray(res.data)) {
          leadsArray = res.data;
        } else if (res.data["All Leads"]) {
          leadsArray = res.data["All Leads"];
        } else {
          setLoading(false);
          return;
        }

        dispatch(setLeads(leadsArray));
        const { completed, inprogress, pendding, rejected, assigned } =
          res.data;
        dispatch(
          setLeadStatusCounts({
            completed: completed || 0,
            assigned: assigned || 0,
            inprogress: inprogress || 0,
            pendding: pendding || 0,
            rejected: rejected || 0,
          })
        );
      }
    } else {
      const res = await leadService.filterLeadsApi(
        { status: status },
        null,
        null,
        null,
        null,
        null,
        null,
        "Error filtering leads"
      );
      if (res && res.data) {
        dispatch(setLeads(res.data));
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    setData(
      leads?.map((lead) => ({
        id: lead?.id,
        leadName: lead?.name,
        email: lead?.email,
        source: lead?.source,
        phone: lead?.phone,
        createdBy: lead?.created_by,
        logs: lead?.latestActivity || lead?.activities,
        assignedTo: lead?.assignedTo || lead?.assigned_to?.name,
        createdAt: lead?.created_at,
        status: lead?.status,
      }))
    );
  }, [leads]);

  const columns = useMemo(
    () => [
      {
        Header: " ",
        Cell: ({ row }) => {
          if (
            row.original.assignedTo === null ||
            row.original.assignedTo === undefined
          ) {
            return <IoSparklesSharp size={20} color={"#92C020"} />;
          } else {
            return null;
          }
        },
      },
      {
        Header: "Lead Name",
        accessor: "leadName",
        Cell: ({ row }) => {
          const leadName = row.original.leadName;
          return (
            <div
              className={"text-nowrap overflow-hidden text-center"}
              style={{ maxWidth: "200px" }}
            >
              {leadName}
            </div>
          );
        },
      },
      {
        Header: "Email",
        accessor: "email",
        Cell: ({ row }) => {
          const email = row.original.email;
          return (
            <div
              className={"text-nowrap overflow-hidden text-center"}
              style={{ maxWidth: "200px" }}
            >
              {email}
            </div>
          );
        },
      },
      {
        Header: "Phone",
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return <div className={"text-nowrap"}>{phone}</div>;
        },
      },
      {
        Header: "Source",
        accessor: "source",
        Cell: ({ row }) => {
          const source = row.original.source;
          const IconComponent = sourceToIcon[source] || null;

          return (
            <div className={"mx-auto social-icon-container"}>
              {IconComponent && <ReactSVG src={IconComponent} />}
            </div>
          );
        },
      },
      {
        Header: "Created By",
        accessor: "createdBy",
      },
      {
        Header: "Status",
        accessor: "status",
        Cell: ({ value, row }) => {
          const statusName = (value) => {
            switch (value) {
              case 0:
                return "Pending";
              case 1:
                return "In Progress";
              case 2:
                return "Completed";
              case 3:
                return "Rejected";
              case 4:
                return "Wrong Lead";
              case 5:
                return "Not Qualified";
              case 6:
                return "No Communication";
              case 7:
                return "Booked";
              case 8:
                return "Booked and Reserved";
              case 9:
                return "Canceled";
              default:
                return "Unknown";
            }
          };

          return row.original.status === 2 ? (
            <span className="completed-status">{statusName(value)}</span>
          ) : row.original.status === 0 ? (
            <span className="on-hold-status">{statusName(value)}</span>
          ) : row.original.status === 1 ? (
            <span className="in-progress-status">{statusName(value)}</span>
          ) : row.original.status === 3 ||
            row.original.status === 4 ||
            row.original.status === 5 ||
            row.original.status === 6 ? (
            <span className="not-started-status">{statusName(value)}</span>
          ) : (
            <span>{statusName(value)}</span>
          );
        },
      },
      {
        Header: "Created At",
        accessor: "createdAt",
        Cell: ({ value }) => {
          if (value) {
            const parsedDate = parseISO(value);
            const formattedDate = format(parsedDate, "yyyy-MM-dd");
            return <div>{formattedDate}</div>;
          }
        },
      },
    ],
    [sourceToIcon]
  );
  return (
    <>
      <NavigateBackComponent title={"Back to Clients"} />
      {/* Client name heading moved to Header component */}
      <LeadsDataTableAdmin
        handleFilterStatus={handleFilterStatus}
        showCenteredModal={showCenteredModal}
        setShowCenteredModal={setShowCenteredModal}
        selectedLeadId={selectedLeadId}
        setSelectedLeadId={setSelectedLeadId}
        setCurrentPage={setCurrentPage}
        currentPage={currentPage}
        recordsPerPage={recordsPerPage}
        setRecordsPerPage={setRecordsPerPage}
        sourceToIcon={sourceToIcon}
        leadStatusCounts={leadStatusCounts}
        loading={loading}
        classNames={"admin-theme"}
        data={data}
        columns={columns}
        totalPages={totalPages}
        setTotalPages={setTotalPages}
      />
      <CenteredModal
        show={showCenteredModal}
        children={
          <DeleteLeadFromTable
            leadId={selectedLeadId}
            setShowCenteredModal={setShowCenteredModal}
          />
        }
        onHide={() => {
          setShowCenteredModal(false);
          setSelectedLeadId(null);
        }}
      />
    </>
  );
};

export default LeadsForClient;
