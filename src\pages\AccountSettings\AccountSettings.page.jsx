import { useState, useEffect } from "react";
import ProfilePictureComponent from "../../components/ProfilePicture/ProfilePicture.component";
import { Button, Col, Row, Modal } from "react-bootstrap";
import Form from "react-bootstrap/Form";
import { Formik } from "formik";
import * as yup from "yup";
import clientService from "../../services/auth/client";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import { useTranslation } from "react-i18next";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import useAuth from "../../redux/hooks/useAuth";

const AccountSettingsPage = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";
  const { user, setUser } = useAuth();
  const [accountPicture, setAccountPicture] = useState(null);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showAutoAssignModal, setShowAutoAssignModal] = useState(false);
  const [targetAutoAssignValue, setTargetAutoAssignValue] = useState(0);

  useEffect(() => {
    const getAccountInfo = async () => {
      const res = await clientService.getAccountInfoAPI();
      setUser({ ...user, user: { ...user.user, ...res.data } });
      setAccountPicture(res?.data?.photo);
    };
    getAccountInfo();
  }, []);

  const updateProfileValidationSchema = yup.object().shape({
    name: yup.string().required(t("accountSettings.validation.nameRequired")),
    emailAddress: yup
      .string()
      .required(t("accountSettings.validation.emailRequired"))
      .email(t("accountSettings.validation.invalidEmail")),
    newPassword: yup
      .string()
      .test(
        "password-validation",
        t("accountSettings.validation.passwordLength"),
        function (value) {
          if (!value) return true; // Allow empty password (no password change)
          return value.length >= 8;
        }
      ),
    confirmPassword: yup
      .string()
      .test(
        "passwords-match",
        t("accountSettings.validation.passwordsMustMatch"),
        function (value) {
          return !this.parent.newPassword || value === this.parent.newPassword;
        }
      ),
  });

  const handleUpdateProfile = async (values) => {
    const { name, emailAddress, newPassword, accountPicture, autoAssign } =
      values;
    const {
      name: oldName,
      email: oldEmail,
      photo: oldAccountPicture,
      auto_assign: oldAutoAssign,
    } = user?.user;

    const changedFields = {};

    // Only include fields that have actually changed
    if (name !== oldName) {
      changedFields.name = name;
    }
    if (emailAddress !== oldEmail) {
      changedFields.email = emailAddress;
    }
    if (accountPicture !== oldAccountPicture) {
      changedFields.photo = accountPicture;
    }
    if (newPassword && newPassword.trim() !== "") {
      changedFields.new_password = newPassword;
    }
    if (autoAssign !== Boolean(Number(oldAutoAssign))) {
      // Convert to boolean for comparison
      changedFields.auto_assign = autoAssign ? 1 : 0; // Convert back to number for API
    }

    if (Object.keys(changedFields).length > 0) {
      const formData = new FormData();
      Object.entries(changedFields).forEach(([key, value]) => {
        if (value instanceof Blob) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      });

      try {
        const response = await clientService.updateProfileApi(formData);
        if (response.status === 200) {
          toast.success(t("accountSettings.toasts.success"), {
            position: "bottom-right",
            theme: "dark",
          });

          const updatedUserData = {
            ...user.user,
            ...response.data,
          };

          Cookies.set(
            "userData",
            JSON.stringify({ token: user.token, user: updatedUserData })
          );

          setUser({ ...user, user: updatedUserData });
          setAccountPicture(response?.data?.photo);
        }
      } catch (error) {
        toast.error(
          error.response?.data?.message || t("accountSettings.toasts.error"),
          {
            position: "bottom-right",
            theme: "dark",
          }
        );
      }
    } else {
      toast.warning(t("accountSettings.toasts.noChanges"), {
        position: "bottom-right",
        theme: "dark",
      });
    }
  };
  const roleNumberToLabel = (roleNumber) => {
    switch (Number(roleNumber)) {
      case 0:
      case 1:
        return "Admin";
      case 2:
        return "Moderator";
      case 3:
        return "Sales";
      case 4:
        return "Accountant";
      case 5:
        return "Team Member";
      default:
        return "Moderator";
    }
  };

  const toggleNewPasswordVisibility = () => {
    setShowNewPassword(!showNewPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <Formik
      initialValues={{
        name: user?.user?.name,
        emailAddress: user?.user?.email,
        phoneNumber: user?.user?.phone,
        accountPicture: user?.user?.photo,
        newPassword: "",
        confirmPassword: "",
        autoAssign: Boolean(Number(user?.user?.auto_assign)), // Convert to boolean
      }}
      onSubmit={handleUpdateProfile}
      validationSchema={updateProfileValidationSchema}
      validateOnChange={true}
      validateOnBlur={true}
    >
      {({
        handleSubmit,
        handleChange,
        values,
        touched,
        errors,
        setFieldValue,
        setFieldTouched,
      }) => (
        <Form
          noValidate
          onSubmit={handleSubmit}
          className={"px-3 px-sm-0 account-settings"}
        >
          <h2 className={"page-title"}>{t("accountSettings.title")}</h2>
          <div className={"content-container my-4 p-4 px-2 px-sm-4"}>
            <div className={"profile-pic-settings-container mb-3"}>
              <ProfilePictureComponent
                accountPicture={values.accountPicture || user?.user?.photo}
                setAccountPicture={(pic) => {
                  setFieldValue("accountPicture", pic);
                  setAccountPicture(pic);
                }}
                size={175}
              />
            </div>
            <Row>
              <Col lg={3}>
                <Form.Group controlId={"name"} className={"mb-3"}>
                  <Form.Label>{t("accountSettings.labels.name")}</Form.Label>
                  <Form.Control
                    isInvalid={touched.name && errors.name}
                    isValid={touched.name && !errors.name}
                    type={"text"}
                    value={values.name}
                    onChange={handleChange}
                  />
                  <Form.Control.Feedback type={"invalid"}>
                    {errors.name}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col lg={3}>
                <Form.Group controlId={"phoneNumber"} className={"mb-3"}>
                  <Form.Label>
                    {t("accountSettings.labels.phoneNumber")}
                  </Form.Label>
                  <Form.Control
                    isInvalid={touched.phoneNumber && errors.phoneNumber}
                    isValid={touched.phoneNumber && !errors.phoneNumber}
                    type={"tel"}
                    value={values.phoneNumber}
                    disabled
                  />
                  <Form.Control.Feedback type={"invalid"}>
                    {errors.phoneNumber}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col lg={3}>
                <Form.Group controlId={"emailAddress"} className={"mb-3"}>
                  <Form.Label>{t("accountSettings.labels.email")}</Form.Label>
                  <Form.Control
                    isInvalid={touched.emailAddress && errors.emailAddress}
                    isValid={touched.emailAddress && !errors.emailAddress}
                    type={"email"}
                    value={values.emailAddress}
                    onChange={handleChange}
                  />
                  <Form.Control.Feedback type={"invalid"}>
                    {errors.emailAddress}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col lg={3}>
                <Form.Group controlId={"role"} className={"mb-3"}>
                  <Form.Label>{t("accountSettings.labels.role")}</Form.Label>
                  <Form.Control
                    disabled
                    type={"text"}
                    value={roleNumberToLabel(user?.user?.role)}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row className="mt-5">
              <Col lg={6}>
                <p className={"fs-5 fw-bold"}>
                  {t("accountSettings.changePassword.title")}
                </p>
                <Form.Group className="mb-3 input-with-icon password-input-container">
                  <Form.Label>
                    {t("accountSettings.changePassword.newPassword")}
                  </Form.Label>
                  <div className="input-icon-container">
                    <Form.Control
                      name="newPassword"
                      type={showNewPassword ? "text" : "password"}
                      value={values.newPassword}
                      onChange={(e) => {
                        handleChange(e);
                        setFieldTouched("newPassword", true, true);
                        // Also validate confirm password when new password changes
                        if (values.confirmPassword) {
                          setFieldTouched("confirmPassword", true, true);
                        }
                      }}
                      isInvalid={touched.newPassword && errors.newPassword}
                      isValid={
                        touched.newPassword &&
                        !errors.newPassword &&
                        values.newPassword
                      }
                      style={{
                        paddingRight: isRTL ? "30px" : "40px",
                        paddingLeft: isRTL ? "40px" : "30px",
                      }}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.newPassword}
                    </Form.Control.Feedback>
                    <div
                      className={`password-input-icon ${
                        isRTL ? "password-icon-rtl" : ""
                      }`}
                      onClick={toggleNewPasswordVisibility}
                    >
                      {showNewPassword ? (
                        <AiOutlineEye
                          className="password-toggle-icon"
                          size={25}
                        />
                      ) : (
                        <AiOutlineEyeInvisible
                          className="password-toggle-icon"
                          size={25}
                        />
                      )}
                    </div>
                  </div>
                </Form.Group>
                <Form.Group className="mb-3 input-with-icon password-input-container">
                  <Form.Label>
                    {t("accountSettings.changePassword.confirmPassword")}
                  </Form.Label>
                  <div className="input-icon-container">
                    <Form.Control
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={values.confirmPassword}
                      onChange={(e) => {
                        handleChange(e);
                        setFieldTouched("confirmPassword", true, true);
                      }}
                      isInvalid={
                        touched.confirmPassword && errors.confirmPassword
                      }
                      isValid={
                        touched.confirmPassword &&
                        !errors.confirmPassword &&
                        values.confirmPassword
                      }
                      style={{
                        paddingRight: isRTL ? "30px" : "40px",
                        paddingLeft: isRTL ? "40px" : "30px",
                      }}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.confirmPassword}
                    </Form.Control.Feedback>
                    <div
                      className={`password-input-icon ${
                        isRTL ? "password-icon-rtl" : ""
                      }`}
                      onClick={toggleConfirmPasswordVisibility}
                    >
                      {showConfirmPassword ? (
                        <AiOutlineEye
                          className="password-toggle-icon"
                          size={25}
                        />
                      ) : (
                        <AiOutlineEyeInvisible
                          className="password-toggle-icon"
                          size={25}
                        />
                      )}
                    </div>
                  </div>
                </Form.Group>
              </Col>
              {user?.user?.parent_id === null &&
              (user?.user?.role === 0 || user?.user?.role === 1) ? (
                <Col lg={6}>
                  <Form.Group controlId="autoAssign">
                    <Form.Check
                      type="switch"
                      id="auto-assign-switch"
                      label={t(
                        "accountSettings.labels.autoAssign",
                        "Auto-assign leads"
                      )}
                      className={"fs-5 fw-bold members-status-switch pe-0 ps-5"}
                      checked={values.autoAssign} // Only check against form values
                      onChange={(e) => {
                        // Get the target value
                        const newValue = e.target.checked;

                        // Always show the modal when the switch is clicked
                        setShowAutoAssignModal(true);
                        setTargetAutoAssignValue(newValue ? 1 : 0); // Convert to 1 or 0
                      }}
                      style={{
                        paddingLeft: 0,
                        marginLeft: 0,
                        paddingRight: isRTL ? 0 : "inherit",
                        marginRight: isRTL ? 0 : "inherit",
                      }}
                    />
                    <Form.Text className="text-muted">
                      {t(
                        "accountSettings.help.autoAssign",
                        "When enabled, new leads will be automatically assigned to your account."
                      )}
                    </Form.Text>
                  </Form.Group>
                </Col>
              ) : null}

              {/* Add the confirmation modal */}
              <CenteredModal
                show={showAutoAssignModal}
                onHide={() => setShowAutoAssignModal(false)}
              >
                <Modal.Header closeButton>
                  <Modal.Title>
                    {targetAutoAssignValue
                      ? t(
                          "accountSettings.autoAssign.enableTitle",
                          "Enable Auto-Assignment"
                        )
                      : t(
                          "accountSettings.autoAssign.disableTitle",
                          "Disable Auto-Assignment"
                        )}
                  </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <p>
                    {targetAutoAssignValue
                      ? t(
                          "accountSettings.autoAssign.enableConfirm",
                          "Are you sure you want to enable auto-assignment of leads? When enabled, new leads will be automatically assigned to your account."
                        )
                      : t(
                          "accountSettings.autoAssign.disableConfirm",
                          "Are you sure you want to disable auto-assignment of leads? When disabled, new leads will remain unassigned until manually assigned."
                        )}
                  </p>
                </Modal.Body>
                <Modal.Footer>
                  <Button
                    variant="secondary"
                    onClick={() => setShowAutoAssignModal(false)}
                  >
                    {t("common.cancel", "Cancel")}
                  </Button>
                  <Button
                    className={
                      targetAutoAssignValue === 1 ? "submit-btn" : "btn-danger"
                    }
                    onClick={() => {
                      // Update the form value
                      setFieldValue(
                        "autoAssign",
                        Boolean(targetAutoAssignValue)
                      );
                      // Close the modal
                      setShowAutoAssignModal(false);

                      // Create a copy of the current values with the updated autoAssign value
                      const updatedValues = {
                        ...values,
                        autoAssign: Boolean(targetAutoAssignValue),
                      };

                      // Call handleUpdateProfile with the updated values
                      handleUpdateProfile(updatedValues);
                    }}
                  >
                    {targetAutoAssignValue === 1
                      ? t("accountSettings.autoAssign.enable", "Enable")
                      : t("accountSettings.autoAssign.disable", "Disable")}
                  </Button>
                </Modal.Footer>
              </CenteredModal>
            </Row>

            <center className={"mt-4"}>
              <Button className={"submit-btn"} type={"submit"}>
                {t("accountSettings.buttons.saveChanges")}
              </Button>
            </center>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default AccountSettingsPage;
