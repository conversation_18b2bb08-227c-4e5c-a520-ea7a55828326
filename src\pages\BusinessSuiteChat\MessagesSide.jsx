import { Col, Row } from "react-bootstrap";
import { useEffect, useRef, useState } from "react";
import messengerIcon from "../../assets/media/Icons/messenger.svg";
import instagramIcon from "../../assets/media/Icons/icons8-instagram.svg";
import { ReactSVG } from "react-svg";
import { TbExclamationMark } from "react-icons/tb";
import { useSelector, useDispatch } from "react-redux";
import {
  selectChats,
  selectSelectedChat,
  selectSelectedWhatsappChat,
  selectMessages,
  selectSelectedPage,
  selectActiveFilter,
  selectPaginationMeta,
  selectHasMore,
  selectLoadingChats,
  selectMessengerChats,
  selectChatAsync, // Use the renamed action
  setHasMore,
  selectSelectedPhone,
  selectWhatsappChats,
  selectInstagramChats,
  selectMergedAndSortedChats,
  selectMergedAndSortedWhatsappChats,
  fetchLatestMessages,
  fetchWhatsAppLatestMessages,
  fetchWhatsAppChatsFromAPI,
  setMessengerChats,
  setInstagramChats,
  setPaginationMeta,
  selectWhatsappChat,
  selectWhatsappChatAsync,
  formatDate,
} from "../../redux/features/metaBusinessChatSlice";
import { FaUserCircle } from "react-icons/fa";
import LoadingDotsAnimation from "../../components/LoadingAnimation/LoadingDotsAnimation";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import { FaArrowDown } from "react-icons/fa6";
import "./messeges-side.css";
import { Tooltip } from "react-tooltip";
import { getMediaType } from "../../utils/get-media-type";
import { parseDateCustom } from "../../utils/parseDateCustom";
import whatsapp from "../../assets/media/Icons/icons8-whatsapp.svg";
import { useCallback } from "react";
import { useMemo } from "react";
import metaService from "../../services/integrations/meta";
import { FaVideo, FaMicrophone, FaPaperclip } from "react-icons/fa";

const PLATFORM_MESSENGER = "messenger";
const PLATFORM_INSTAGRAM = "instagram";
const PLATFORM_WHATSAPP = "whatsapp";

const MessagesSide = () => {
  const dispatch = useDispatch();
  const selectedPage = useSelector(selectSelectedPage);
  const activeFilter = useSelector(selectActiveFilter);
  const paginationMeta = useSelector(selectPaginationMeta);
  const hasMore = useSelector(selectHasMore);
  const loadingChats = useSelector(selectLoadingChats);
  const fetchingMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingMessages
  );
  const fetchingWhatsAppMessages = useSelector(
    (state) => state.metaBusinessSuite.loading.fetchingWhatsAppMessages
  );
  const loadingMessages = fetchingMessages || fetchingWhatsAppMessages;
  const [loadingPagination, setLoadingPagination] = useState(false);
  const fetchMoreChatsAbortController = useRef(null);
  const selectedChat = useSelector(selectSelectedChat);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);
  const selectedPhone = useSelector(selectSelectedPhone);
  const whatsappChats = useSelector(selectWhatsappChats);
  const mergedChats = useSelector(selectMergedAndSortedChats);
  const mergedWhatsappChats = useSelector(selectMergedAndSortedWhatsappChats);

  // Use the correct data source based on active filter
  const chats = useMemo(() => {
    if (activeFilter === "whatsapp") {
      return mergedWhatsappChats || [];
    } else {
      return mergedChats || [];
    }
  }, [activeFilter, mergedWhatsappChats, mergedChats]);

  // Function to fetch initial chats (moved from thunk)
  const fetchInitialChats = useCallback(async () => {
    try {
      const response = await metaService.getChatsApi({
        page_id: selectedPage.id,
        access_token: selectedPage.page_token,
      });

      if (response.data) {
        // Update the appropriate chat arrays based on the platform
        if (activeFilter === "messenger") {
          const messengerData = response.data.messanger_threads?.data || [];
          dispatch(setMessengerChats(messengerData));
        } else if (activeFilter === "instagram") {
          const instagramData = response.data.instagram_threads?.data || [];
          dispatch(setInstagramChats(instagramData));
        }

        // Update pagination meta
        dispatch(
          setPaginationMeta([
            response.data.insta_next || null,
            response.data.messanger_next || null,
          ])
        );
      }
    } catch (error) {
      console.error("Error fetching initial chats:", error);
    }
  }, [selectedPage, activeFilter, dispatch]);

  // Fetch initial chats from backend AND latest messages from Firestore
  useEffect(() => {
    if (selectedPage?.id && selectedPage?.page_token) {
      fetchInitialChats();

      // Call the appropriate function based on active filter
      if (activeFilter === PLATFORM_WHATSAPP && selectedPhone) {
        // First sync API data with Firebase
        dispatch(fetchWhatsAppChatsFromAPI({ selectedPhone })).then(() => {
          // Then fetch latest messages from Firebase
          dispatch(fetchWhatsAppLatestMessages({ selectedPhone }));
        });
      } else if (
        activeFilter === PLATFORM_MESSENGER ||
        activeFilter === PLATFORM_INSTAGRAM
      ) {
        dispatch(fetchLatestMessages());
      }
    }
  }, [dispatch, selectedPage, fetchInitialChats, activeFilter, selectedPhone]);

  // Fetch latest messages when filter changes (for Messenger/Instagram)
  useEffect(() => {
    if (
      (activeFilter === PLATFORM_MESSENGER ||
        activeFilter === PLATFORM_INSTAGRAM) &&
      selectedPage?.id
    ) {
      dispatch(fetchLatestMessages());
    }
  }, [dispatch, activeFilter, selectedPage]);

  // Function to fetch more chats (moved from thunk)
  const fetchMoreChats = async ({ selectedPage, paginationMeta }) => {
    if (paginationMeta.length === 0) {
      return null;
    }

    try {
      console.log("Fetching more chats with params:", {
        after:
          activeFilter === "messenger" ? paginationMeta[1] : paginationMeta[0],
        access_token: selectedPage?.page_token,
        activeFilter,
        paginationMeta,
      });

      const result = await metaService.getMoreChatsAPI({
        page_id: selectedPage?.page_id,
        after:
          activeFilter === "messenger" ? paginationMeta[1] : paginationMeta[0],
        access_token: selectedPage?.page_token,
      });

      console.log("Pagination API result:", result);

      if (result?.data) {
        const {
          data,
          insta_next: newInstaNext,
          messanger_next: newMessengerNext,
        } = result.data;

        // Update nextPage with new pagination tokens
        dispatch(
          setPaginationMeta(
            [newInstaNext, newMessengerNext].filter(
              (item) => item && item !== "null" && item.trim() !== ""
            )
          )
        );

        // Merge previous chats with newly fetched chats based on the platform
        if (activeFilter === "messenger") {
          dispatch(setMessengerChats((prevChats) => [...prevChats, ...data]));
        } else if (activeFilter === "instagram") {
          dispatch(setInstagramChats((prevChats) => [...prevChats, ...data]));
        }

        return result;
      } else {
        console.error("No data in API result:", result);
        return null;
      }
    } catch (error) {
      console.error("Error fetching more chats:", error);
      return null;
    }
  };

  const getFormattedDate = (dateStr) => {
    const date = new Date(dateStr);
    if (isNaN(date)) return "Invalid Date"; // Handle invalid date strings

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    if (messageDateOnly.getTime() === today.getTime()) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (messageDateOnly.getTime() === yesterday.getTime()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  // ---------- helpers ----------
  // Converts a media type to a human-readable preview shown in chat list
  const mediaPreview = (content, type, url) => {
    const src = url || content;
    switch (type) {
      case "image":
        return (
          <span className="d-inline-flex align-items-center gap-1">
            <img
              src={src}
              alt="img"
              width={30}
              height={30}
              className="rounded object-fit-cover"
            />
          </span>
        );
      case "video":
        return (
          <span className="d-inline-flex align-items-center gap-1">
            {src ? (
              <video
                src={src}
                width={30}
                height={30}
                muted
                preload="metadata"
                className="rounded object-fit-cover"
              />
            ) : (
              <FaVideo />
            )}
          </span>
        );
      case "audio":
        return (
          <span className="d-inline-flex align-items-center gap-1">
            <FaMicrophone /> Audio
          </span>
        );
      case "sticker":
        return (
          <span className="d-inline-flex align-items-center gap-1">
            <img
              src={src}
              alt="sticker"
              width={30}
              height={30}
              className="rounded object-fit-cover"
            />
          </span>
        );
      case "file":
        return (
          <span className="d-inline-flex align-items-center gap-1">
            <FaPaperclip /> File
          </span>
        );
      default:
        return null;
    }
  };

  // Memoize the renderChats function to avoid unnecessary re-renders
  const renderChats = useCallback(
    (chatsToRender, platform) => {
      if (!chatsToRender || !Array.isArray(chatsToRender)) return [];

      return chatsToRender.map((chat) => {
        if (!chat || !chat.participants || !chat.participants.data) return null; // Defensive check

        const participantIndex = platform === PLATFORM_INSTAGRAM ? 1 : 0;
        const participant = chat?.participants?.data[participantIndex];

        const displayName =
          platform === PLATFORM_INSTAGRAM
            ? participant?.username || "No name"
            : participant?.name || "No name";

        const participantImage = participant?.image || "dummy";
        // Build preview for latest message (supports media types)
        let displayMessage = chat.message || "No message";
        const detectedType = getMediaType({
          message: chat.message,
          type: chat.type,
        });
        const previewNode = mediaPreview(chat.message, detectedType, chat.url);
        if (previewNode) displayMessage = previewNode;
        const displayDate = getFormattedDate(
          chat.updated_time || chat.created_time
        );

        return (
          <Row
            key={`${platform}-${chat.id}`}
            className={`my-3 py-2 single-chat-container text-end text-sm-start ${
              selectedChat?.id === chat?.id ? "selected-chat" : ""
            }`}
            onClick={() => {
              if (loadingMessages) return; // Prevent switching while messages are loading
              dispatch(selectChatAsync({ thread: chat }));
            }}
          >
            <Col sm={2} xs={3} className={"position-relative"}>
              {participantImage === "dummy" ? (
                <FaUserCircle
                  className={"rounded-circle bg-white"}
                  color={"gray"}
                  size={60}
                />
              ) : (
                <img
                  width={60}
                  height={60}
                  src={participantImage}
                  alt={"User"}
                  className={"integration-account rounded-circle"}
                />
              )}
            </Col>
            <Col sm={7} xs={9}>
              <div className={"fw-bold one-line"}>{displayName}</div>
              <div className={"text-secondary one-line"}>
                <div className={"one-line"}>{displayMessage}</div>
              </div>
            </Col>
            <Col
              sm={3}
              xs={12}
              className={"text-secondary text-end text-sm-start"}
            >
              {displayDate}
            </Col>
          </Row>
        );
      });
    },
    [selectedChat, dispatch, loadingMessages] // removed getFormattedDate because it's stable now
  );

  const loadMoreChats = async () => {
    if (fetchMoreChatsAbortController.current) {
      fetchMoreChatsAbortController.current.abort();
    }
    fetchMoreChatsAbortController.current = new AbortController();
    const signal = fetchMoreChatsAbortController.current.signal;

    try {
      setLoadingPagination(true);
      await fetchMoreChats({
        selectedPage,
        paginationMeta,
      });
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch more chats request aborted");
      } else {
        console.error("Error loading more chats:", error);
        dispatch(setHasMore(false));
      }
    } finally {
      setLoadingPagination(false);
    }
  };

  // Sort WhatsApp chats by last message date
  const sortedWhatsappChats = [...(mergedWhatsappChats || [])].sort((a, b) => {
    const dateA = a.last_message?.created_at || a.updated_at || a.created_at;
    const dateB = b.last_message?.created_at || b.updated_at || b.created_at;
    return new Date(dateB) - new Date(dateA);
  });

  const renderWhatsAppChats = (chats) => {
    if (!chats || chats.length === 0) {
      return null;
    }

    return chats.map((chat) => (
      <Row
        key={chat?.id}
        className={`my-3 py-2 single-chat-container ${
          selectedWhatsappChat?.sender_phone_number ===
          chat?.sender_phone_number
            ? "selected-chat"
            : ""
        }`}
        onClick={() => {
          // Prevent switching while messages are loading
          if (loadingMessages) return;

          // Check if required data is available
          if (!selectedPhone) {
            return;
          }

          if (!chat) {
            return;
          }

          dispatch(selectWhatsappChatAsync({ thread: chat, selectedPhone }));
        }}
      >
        <Col lg={3} className={"position-relative"}>
          {chat?.image === "dummy" || !chat?.image ? (
            <FaUserCircle
              className={"rounded-circle bg-white"}
              color={"gray"}
              size={60}
            />
          ) : (
            <img
              width={60}
              height={60}
              src={chat?.image}
              alt={"User"}
              className={"integration-account rounded-circle"}
            />
          )}
          <div className={"chat-user-logo"}>
            <ReactSVG src={whatsapp} />
          </div>
        </Col>
        <Col lg={6}>
          <div className={"d-flex flex-column"}>
            <div>
              {chat?.sender_name || chat?.sender_phone_number}{" "}
              {chat?.isImportant && <TbExclamationMark color={"red"} />}
            </div>
            <div className={"one-line text-secondary"}>
              {(() => {
                const lastMsgContent =
                  chat?.last_message?.message || chat?.message;
                const lastMsgType = chat?.last_message?.type || chat?.type;
                const mediaType = getMediaType({
                  message: lastMsgContent,
                  type: lastMsgType,
                });
                const node = mediaPreview(
                  lastMsgContent,
                  mediaType,
                  chat?.last_message?.url
                );
                return node || lastMsgContent || "No messages";
              })()}
            </div>
          </div>
        </Col>
        <Col lg={3} className={"text-secondary text-center"}>
          {chat?.last_message?.created_at ||
          chat?.updated_at ||
          chat?.created_at
            ? formatDate(
                chat?.last_message?.created_at ||
                  chat?.updated_at ||
                  chat?.created_at
              )
            : ""}
        </Col>
      </Row>
    ));
  };

  // Memoize the rendered chats to avoid unnecessary re-renders
  const renderedChats = useMemo(() => {
    if (activeFilter === PLATFORM_WHATSAPP) {
      return renderWhatsAppChats(sortedWhatsappChats);
    } else {
      return renderChats(chats, activeFilter);
    }
  }, [chats, renderChats, activeFilter, sortedWhatsappChats]);

  return (
    <div className={"d-flex justify-content-between flex-column"}>
      {loadingChats ? (
        <center>
          <FetchingDataLoading />
        </center>
      ) : (
        <div className={"chat-container px-3"}>
          {((activeFilter === PLATFORM_WHATSAPP &&
            sortedWhatsappChats.length > 0) ||
            (activeFilter !== PLATFORM_WHATSAPP && chats.length > 0)) &&
            renderedChats}

          {/* Modified arrow condition */}
          {activeFilter &&
            ((activeFilter === PLATFORM_MESSENGER && chats.length > 0) ||
              (activeFilter === PLATFORM_INSTAGRAM && chats.length > 0) ||
              (activeFilter === PLATFORM_WHATSAPP &&
                sortedWhatsappChats.length > 0)) &&
            hasMore &&
            paginationMeta &&
            ((activeFilter === PLATFORM_INSTAGRAM &&
              paginationMeta[0] &&
              paginationMeta[0] !== "null" &&
              paginationMeta[0].trim() !== "") ||
              (activeFilter === PLATFORM_MESSENGER &&
                paginationMeta[1] &&
                paginationMeta[1] !== "null" &&
                paginationMeta[1].trim() !== "")) && (
              <>
                <div
                  className="arrow-container"
                  role={"button"}
                  onClick={() => loadMoreChats()}
                >
                  <FaArrowDown size={35} className="arrow-down" />
                </div>
                <Tooltip
                  anchorSelect=".arrow-container"
                  content="Show more chats."
                  variant={"success"}
                />
              </>
            )}
        </div>
      )}
    </div>
  );
};

export default MessagesSide;
