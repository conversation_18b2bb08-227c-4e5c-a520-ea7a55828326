import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>} from "react-bootstrap";
import "./AssignTeam.css";
import { showSuccessToast } from "../../utils/toast-success-error";
import { useSelector } from "react-redux";
import {useParams} from "react-router-dom";
import leadService from "../../services/leads";
import {useEffect} from "react";
import { useTranslation } from "react-i18next";

const AssignTeamModal = (props) => {
    const {t} = useTranslation();
    const {onHide, setLeadDetails, handleSelect, selectedTeamMember, setSelectedTeamMember, leadDetails} = props
    const lead_id = useParams();
    const { teamMembers } = useSelector((state) => state.client);
    const filteredTeamMembers = teamMembers?.filter((teamMember) => teamMember?.status !== "deactive");
    useEffect(() => {
        setSelectedTeamMember(leadDetails?.assigned_to?.id);
    }, []);

    const handleAssignment = async () => {
        if (selectedTeamMember) {
                // Check if selectedTeamMember is an object with id or just an id
                const memberId = typeof selectedTeamMember === 'object' ? selectedTeamMember.id : selectedTeamMember;
                const result = await leadService.updateSingleLeadApi(Number(lead_id?.id), {assigned_id: Number(memberId)});
                setLeadDetails(result?.data)
                showSuccessToast('Lead Assigned Successfully');
                onHide();
        } else {
                const result = await leadService.unassignedLeadsApi(Number(lead_id.id));
                setLeadDetails(result?.data)
                showSuccessToast('Lead Unassigned Successfully');
                onHide();
        }
    }
    return (<>
            <Modal
                {...props}
                size="md"
                aria-labelledby="contained-modal-title-vcenter"
                centered
                className={"blur-modal"}
            >
                <Modal.Body className={"text-center"} style={{maxHeight: "500px", overflowY: "auto"}}>
                    <h4 className={"fw-bold"}>{t('leadProfile.assignClient')}</h4>
                    <p className={"my-4"}>{t('leadProfile.assignDesc')}</p>
                    {filteredTeamMembers?.map(member => (<Row key={member.id}
                                                      className={`mb-3 team-member-row ${(selectedTeamMember && (selectedTeamMember.id === member.id || selectedTeamMember === member.id)) ? 'selected' : ''}`}
                                                      onClick={() => handleSelect(member)}>
                            <Col lg={5} className={"d-flex justify-content-start text-start"}>
                                <div className={"ms-3"}>{member?.name}</div>
                            </Col>
                            <Col lg={7} className={"text-end member-email"}>
                                <div>{member?.email}</div>
                            </Col>
                        </Row>))}
                </Modal.Body>
                <Modal.Footer className={"justify-content-evenly"}>
                    {/*<Button onClick={async () => {*/}
                    {/*    try {*/}
                    {/*        const result = await unassignedLeadsApi(Number(lead_id.id));*/}
                    {/*        setLeadDetails(result?.data)*/}
                    {/*        toast.success('Lead Unassigned Successfully', {position: "bottom-right", theme: "dark"});*/}
                    {/*        onHide();*/}
                    {/*    } catch (error) {*/}
                    {/*        toast.error(error?.response?.data?.message, {position: "bottom-right", theme: "dark"});*/}
                    {/*    }*/}
                    {/*}}*/}
                    {/*        className={"rounded-pill"} variant={"danger"}>*/}
                    {/*    Leave Unassigned*/}
                    {/*</Button>*/}
                    <Button className={"apply-btn"}
                            disabled={selectedTeamMember && (selectedTeamMember.id === leadDetails?.assigned_to?.id || selectedTeamMember === leadDetails?.assigned_to?.id)}
                         onClick={selectedTeamMember && (selectedTeamMember.id === leadDetails?.assigned_to?.id || selectedTeamMember === leadDetails?.assigned_to?.id) ? null : () => handleAssignment()}>
                        {t('common.confirm')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </>);
};

export default AssignTeamModal;
