import {Swiper, SwiperSlide} from "swiper/react";
import {Navigation} from "swiper/modules";
import {FaCalendar, FaEnvelope, <PERSON>aEye, FaPhone, FaUserPlus} from "react-icons/fa6";
import {Col, Row} from "react-bootstrap";
import activityService from "../../services/activities";
import { useSelector } from "react-redux";
import {useCallback, useMemo, useReducer, useState} from "react";
import {useParams} from "react-router-dom";
import {useFormik} from "formik";
import {MdOutlineAssignmentTurnedIn} from "react-icons/md";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import EditActivityForm from "./EditActivityForm";
import "swiper/css/navigation";
import "swiper/css";
import "../UserActivitiesTabs/UserActivities.css";
import CollapsibleSection from "./CollapsibleSection";
import {validationSchema} from "./ActivitiesTabsData.module";
import FormikSection from "./FormikSection";
import {FaCalendarAlt, FaCommentAlt} from "react-icons/fa";
import {safeNumber} from "../../utils/safe-number";
import { useTranslation } from "react-i18next";

const initialState = {
    callCollapse: false, noteCollapse: false, messageCollapse: false, meetingCollapse: false,
};

const reducer = (state, action) => {
    switch (action.type) {
        case "TOGGLE_COLLAPSE":
            return {...state, [action.payload]: !state[action.payload]};
        default:
            return state;
    }
};

const ActivityTab = ({leadDetails, setLeadDetails}) => {
    const params = useParams();
    const [state, dispatch] = useReducer(reducer, initialState);
    const { user, currentUserPermissions } = useSelector((state) => state.auth);
    const [showCenteredModal, setShowCenteredModal] = useState(false);
    const [selectedActivity, setSelectedActivity] = useState(null);
    const [actionProven, setActionProven] = useState(null);
    // const [quotationOffer, setQuotationOffer] = useState(null);
    const [openSectionKey, setOpenSectionKey] = useState(null);
    const {t} = useTranslation();
    const activitySections = useMemo(() => [
        { title: t('leadProfile.call'), icon: <FaPhone />, stateKey: "call", type: 3 },
        {
            title: t('leadProfile.meeting'),
            icon: <FaCalendarAlt />,
            stateKey: "meeting",
            type: 4,
        },
        {
            title: t('leadProfile.message'),
            icon: <FaCommentAlt />,
            stateKey: "message",
            type: 5,
        },
    ], []);

    const handleSaveClick = async (values, resetForm, sectionKey) => {
        // Get the action type from the activity section based on openSectionKey
        const section = activitySections.find(section => section.stateKey === openSectionKey);
        const action = section ? section.type : 2; // Default to 2 if no section is found
        const leadId = params.id;
        const formattedDate = values.next_date.toISOString();

        const activityData = {
            ...values,
            name: leadDetails.name,
            lead_id: safeNumber(leadId),
            next_date: formattedDate,
            client_id: safeNumber(user.user.id),
            // lead_status: safeNumber(values.lead_status) || safeNumber(leadDetails.status),
            action: safeNumber(action),
            amount: safeNumber(values.amount) || 0,
            // quotation_amount: safeNumber(values.quotation_amount) || 0,
            action_proven: values.action_proven || actionProven || null,
            // quotation_offer: values.quotation_offer || quotationOffer || null,
            // quotation_status: safeNumber(values.quotation_status) || safeNumber(leadDetails.quotation_status),
            // refuse_reason: values.refuse_reason || leadDetails.refuse_reason,
        };
        if (safeNumber(values.lead_status) !== 8) {
            delete activityData.amount;
        }
        // if (safeNumber(values.lead_status) !== 10) {
        //     delete activityData.quotation_amount;
        //     delete activityData.quotation_status;
        //     delete activityData.quotation_offer;
        //     delete activityData.refuse_reason;
        // }
        // if (values.refuse_reason === undefined || values.refuse_reason === null) {
        //     delete values.refuse_reason;
        // }
        if (values.action_proven === null) {
            delete values.action_proven;
        }
        try {
            const response = await activityService.createActivityApi(activityData);
            setLeadDetails(prevData => ({
                ...prevData,
                status: safeNumber(response?.data?.lead_status),
                activities: [...prevData.activities, response.data]
            }));

            // Keep the section open after submission
            // setOpenSectionKey(null);

            resetForm();
        } catch (error) {
            // Handle error if needed
            console.error('Error saving activity:', error);
        }
    };

    // Formik instances for different sections
    const formikInstances = {
        call: useFormik({
            initialValues: {
                note: "",
                next_date: "",
                result: "",
                // lead_status: leadDetails?.status || "",
                action: null,
                amount: "",
                // quotation_amount: "",
                // quotation_offer: null,
                // quotation_status: null,
                // refuse_reason: "",
                action_proven: null,
            },
            validationSchema: validationSchema,
            onSubmit: async (values, { resetForm }) => {
                await handleSaveClick(values, resetForm, 'call');
            },
        }),
        meeting: useFormik({
            initialValues: {
                note: "",
                next_date: "",
                result: "",
                // lead_status: leadDetails?.status || "",
                action: null,
                amount: "",
                // quotation_amount: "",
                // quotation_offer: null,
                // quotation_status: null,
                // refuse_reason: "",
                action_proven: null,
            },
            validationSchema: validationSchema,
            onSubmit: async (values, { resetForm }) => {
                await handleSaveClick(values, resetForm, 'meeting');
            },
        }),
        message: useFormik({
            initialValues: {
                note: "",
                next_date: "",
                result: "",
                // lead_status: leadDetails?.status || "",
                action: null,
                amount: "",
                // quotation_amount: "",
                // quotation_offer: null,
                // quotation_status: null,
                // refuse_reason: "",
                action_proven: null,
            },
            validationSchema: validationSchema,
            onSubmit: async (values, { resetForm }) => {
                await handleSaveClick(values, resetForm, 'message');
            },
        }),
    };

    const handleToggle = useCallback((key) => {
        setOpenSectionKey((prevKey) => (prevKey === key ? null : key));
    }, []);


    return (<>
            <Swiper
                slidesPerView={1}
                spaceBetween={10}
                breakpoints={{
                    640: {
                        slidesPerView: 2, spaceBetween: 5,
                    }, 768: {
                        slidesPerView: 3, spaceBetween: 10,
                    }, 1024: {
                        slidesPerView: 4, spaceBetween: 20,
                    },
                }}
                modules={[Navigation]}
                navigation={true}
                className="activities-swiper my-3 px-5"
                style={{width: "80%"}}
            >
                <div className={"log-list-container-profile"}>
                    {leadDetails?.activities?.map((activity, index) => (<SwiperSlide
                        key={index}
                        className={"activitySlice"}
                        onClick={ () => {
                            setSelectedActivity(activity);
                            setShowCenteredModal(true);
                        }}
                    >
                        <div
                            className={`activity-container-profile overflow-y-auto ${activity?.status === 2 && "completedStatus"}`}
                        >
                            <center className={"my-2"}>
                                {activity?.action === 1 && (<FaUserPlus size={20} className={"mainColor"}/>)}
                                {activity?.action === 2 && (<MdOutlineAssignmentTurnedIn
                                    size={20}
                                    className={"mainColor"}
                                />)}
                                {activity?.action === 3 && (<FaPhone size={20} className={"mainColor"}/>)}
                                {activity?.action === 4 && (<FaCalendar size={20} className={"mainColor"}/>)}
                                {activity?.action === 5 && (<FaEnvelope size={20} className={"mainColor"}/>)}
                            </center>
                            {activity?.next_date && activity?.result ? (<div
                                className={"d-flex justify-content-between align-items-center flex-column opacity-50  text-container-truncated"}
                            >
                                <div>{activity?.result}</div>
                                <div>{activity?.next_date}</div>
                            </div>) : (<p className={"opacity-50"}>
                                {/*make new line after the word (at) inside the note*/}
                                {activity?.note?.replace(/\b(at)\b/g, `at\n`)}
                            </p>)}
                            <div className="overlay-activity">
                                <FaEye className="eye-icon"/>
                            </div>
                        </div>
                        <div className={"note-dot"}></div>
                    </SwiperSlide>))}
                </div>
            </Swiper>
        {leadDetails?.assigned_to || user?.user?.role === 0 ? (currentUserPermissions?.includes("activity-create") || user?.user?.role === 0 ? (
                    <Row className={"my-5 justify-content-between"}>
                        {activitySections.map((section) => (
                            <Col lg={4} md={6} sm={12} key={section.stateKey} className={"mb-3"}>
                                <CollapsibleSection
                                    title={section.title}
                                    icon={section.icon}
                                    isOpen={openSectionKey === section.stateKey}
                                    onToggle={() => handleToggle(section.stateKey)}
                                >
                                    {openSectionKey === section.stateKey && (
                                        <FormikSection
                                            formik={formikInstances[section.stateKey]}
                                            setActionProven={setActionProven}
                                            // setQuotationOffer={setQuotationOffer}
                                            leadDetails={leadDetails}
                                        />
                                    )}
                                </CollapsibleSection>
                            </Col>))}
                    </Row>) : null) : null}
            <CenteredModal
                show={showCenteredModal}
                children={<EditActivityForm
                    activity={selectedActivity}
                    handleClose={() => setShowCenteredModal(false)}
                />}
                onHide={() => setShowCenteredModal(false)}
            />
        </>);
};

export default ActivityTab;
