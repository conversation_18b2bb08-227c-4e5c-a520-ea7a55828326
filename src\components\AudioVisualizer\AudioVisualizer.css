/* Audio Visualizer Styles */
.audio-visualizer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin-top: 10px;
}

.recording-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background-color: #dc3545;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

.recording-time {
  font-size: 14px;
  font-weight: 600;
  color: #dc3545;
}

.visualizer-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  height: 60px;
  width: 100%;
  padding: 0 15px;
}

.visualizer-bar {
  width: 4px;
  background-color: #dc3545;
  border-radius: 2px;
  transition: height 0.1s ease;
}

/* Audio Recording Button Styles */
.audio-record-btn, .audio-stop-btn {
  border: none;
  background: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.audio-record-btn::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.audio-record-btn:hover::before {
  transform: scale(1);
}

.audio-stop-btn {
  background-color: rgba(220, 53, 69, 0.15);
  animation: pulse 1.5s infinite;
}

.audio-record-btn:disabled,
.audio-stop-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Audio Preview Styles */
.audio-preview {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.audio-player {
  width: 100%;
  height: 40px;
}

.remove-btn {
  background: none;
  border: none;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.remove-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
