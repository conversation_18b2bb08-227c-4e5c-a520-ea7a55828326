import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { FaMeta, FaTiktok } from "react-icons/fa6";
import {
  FaInstagram,
  Fa<PERSON><PERSON><PERSON><PERSON>,
  FaSnapchatGhost,
  FaTwitter,
} from "react-icons/fa";
import {
  disconnect<PERSON><PERSON><PERSON>,
  FacebookAuth,
  GoogleAuth,
} from "../../utils/firebase.config";
import { FcGoogle } from "react-icons/fc";
import Cookies from "js-cookie";
import metaService from "../../services/integrations/meta";
import tiktokService from "../../services/integrations/tiktok";
import { showSuccessToast, showErrorToast } from "../../utils/toast-success-error";

// Async thunks for handling platform authentication
export const signInWithPlatform = createAsyncThunk(
  'integration/signInWithPlatform',
  async ({ platform, navigate, t }, { dispatch, rejectWithValue }) => {
    try {
      const userCredential = await authenticatePlatform(platform);
      if (!userCredential) return rejectWithValue('Authentication failed');

      const { user, _tokenResponse } = userCredential;
      if (user && _tokenResponse?.oauthAccessToken) {
        const userData = buildUserData(user.email, _tokenResponse.oauthAccessToken, platform);
        const result = await handleUserAuthentication(userData, _tokenResponse, navigate, dispatch);
        return result;
      } else {
        return rejectWithValue('Invalid user or token response');
      }
    } catch (error) {
      if (error.code === "auth/popup-closed-by-user") {
        return rejectWithValue('Authentication popup closed by the user');
      }
      return rejectWithValue(error.message);
    }
  }
);

export const disconnectFromFacebook = createAsyncThunk(
  'integration/disconnectFromFacebook',
  async ({ FBAccID, navigate }, { dispatch, rejectWithValue }) => {
    try {
      const response = await disconnectFromFB(FBAccID);

      let user = Cookies.get("userData");
      const token = Cookies.get("access_token");
      Cookies.remove("userPages");

      if (user) {
        user = JSON.parse(user);
        if (token) {
          user.user["access-token"] = null;
          user.user.access_token_status = false;
          Cookies.set("userData", JSON.stringify(user));
          Cookies.remove("access_token");
          navigate("/integrations");
          if (window.location.pathname === "/integrations") {
            window.location.reload();
          }
        }
      }

      showSuccessToast(response?.data?.message);
      return response;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const disconnectFromTiktok = createAsyncThunk(
  'integration/disconnectFromTiktok',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const response = await tiktokService.disconnectFromTikTokAPI();
      Cookies.remove("access_token_tiktok");
      return response;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const fetchTikTokPages = createAsyncThunk(
  'integration/fetchTikTokPages',
  async (advertiserID, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoadingPages(true));
      const response = await tiktokService.getUserPagesTikTokAPI(advertiserID);
      return response?.data?.data?.list || [];
    } catch (error) {
      return rejectWithValue(error.message);
    } finally {
      dispatch(setLoadingPages(false));
    }
  }
);

export const downloadLeadsTiktok = createAsyncThunk(
  'integration/downloadLeadsTiktok',
  async ({ advertiserID, pageID }, { rejectWithValue }) => {
    try {
      const response = await tiktokService.downloadTikTokLeadsAPI(advertiserID, pageID);
      return response;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Helper functions (moved from context)
const authenticatePlatform = async (platform) => {
  switch (platform) {
    case "Google":
      return await GoogleAuth();
    case "Facebook":
      return await FacebookAuth();
    case "Tiktok":
      console.log("tiktok");
      return null;
    default:
      return await FacebookAuth();
  }
};

const buildUserData = (email, accessToken, platform) => ({
  username: email,
  access_token: accessToken,
  flag: platform === "Google" ? "g" : "fb",
});

const handleUserAuthentication = async (userData, _tokenResponse, navigate, dispatch) => {
  const [response, result] = await Promise.allSettled([
    metaService.updateAccessTokenApi(userData),
    metaService.getUserPagesApi({ signal: new AbortController().signal })
  ]);

  Cookies.set("userPages", JSON.stringify(result.value));

  if (result?.value?.message === "success") {
    dispatch(setUserPages(result.value));
  }

  if (response?.status === "fulfilled" && response.value?.success) {
    await updateLocalUserData(_tokenResponse.oauthAccessToken);
    dispatch(setIntegratedAlready(true));

    showSuccessToast(response?.value?.data?.message, {
      position: "bottom-right",
      theme: "dark",
    });

    navigateOrReload("/integrations/facebook/connected", navigate);
  } else {
    showErrorToast(response?.value?.data?.message, {
      position: "bottom-right",
      theme: "dark",
    });
    navigateOrReload("/integrations/facebook/connected", navigate);
  }

  return response;
};

const updateLocalUserData = async (accessToken) => {
  const storedUserData = Cookies.get("userData");
  if (storedUserData) {
    const parsedUserData = JSON.parse(storedUserData);
    if (parsedUserData?.user) {
      parsedUserData.user["access-token"] = accessToken;
      parsedUserData.access_token_status = true;
      Cookies.set("userData", JSON.stringify(parsedUserData));
      Cookies.set("access_token", accessToken);
    }
  }
};

const navigateOrReload = (path, navigate) => {
  if (window.location.pathname === path) {
    window.location.reload();
  } else {
    navigate(path, { state: { source: "integrationContext" }, replace: true });
  }
};

// Integration data function (will be used in components)
export const getIntegrationData = (t) => [
  {
    id: 1,
    icon: <FaMeta color="#1877f2" size={60} />,
    successIcon: <FaMeta style={{ color: "#92C020" }} size={80} />,
    platform: "Meta",
    description: t("integrations.facebook.description"),
  },
  {
    id: 2,
    icon: <FaTiktok color="#00000" size={60} />,
    successIcon: <FaMeta style={{ color: "#92C020" }} size={80} />,
    platform: "Tiktok",
    description: t("integrations.tiktok.description"),
  },
  {
    id: 3,
    icon: <FcGoogle color="#1877f2" size={46} />,
    successIcon: <FcGoogle style={{ color: "#92C020" }} size={80} />,
    platform: "Google",
    description: t("integrations.google.description"),
  },
  {
    id: 4,
    icon: <FaLinkedin color="#1877f2" size={46} />,
    successIcon: <FaLinkedin style={{ color: "#92C020" }} size={80} />,
    platform: "LinkedIn",
    description: t("integrations.linkedin.description"),
  },
  {
    id: 5,
    icon: <FaInstagram color="#e4405f" size={46} />,
    successIcon: <FaInstagram style={{ color: "#92C020" }} size={80} />,
    platform: "Instagram",
    description: t("integrations.instagram.description"),
  },
  {
    id: 6,
    icon: <FaTwitter color="#1da1f2" size={46} />,
    successIcon: <FaTwitter style={{ color: "#92C020" }} size={80} />,
    platform: "Twitter",
    description: t("integrations.twitter.description"),
  },
  {
    id: 7,
    icon: <FaSnapchatGhost color="#fffc00" size={46} />,
    successIcon: <FaSnapchatGhost style={{ color: "#92C020" }} size={80} />,
    platform: "Snapchat",
    description: t("integrations.snapchat.description"),
  },
];

const initialState = {
  searchTerm: '',
  currentPage: 1,
  filteredData: [],
  showIntegrationModal: false,
  accounts: [],
  advertisers: [],
  tiktokIntegrated: null,
  showCancelIntegrationModal: false,
  showCancelIntegrationModal2: false,
  integratedAlready: null,
  tiktokPages: [],
  loadingPages: false,
  selectedAdvertiser: null,
  showTikTokAuthModal: false,
  userPages: [],
  // Loading states for async operations
  isAuthenticating: false,
  isDisconnecting: false,
  isFetchingPages: false,
  isDownloadingLeads: false,
  // Error states
  authError: null,
  disconnectError: null,
  fetchPagesError: null,
  downloadLeadsError: null,
};

const integrationSlice = createSlice({
  name: 'integration',
  initialState,
  reducers: {
    setSearchTerm: (state, action) => { state.searchTerm = action.payload; },
    setCurrentPage: (state, action) => { state.currentPage = action.payload; },
    setFilteredData: (state, action) => { state.filteredData = action.payload; },
    setShowIntegrationModal: (state, action) => { state.showIntegrationModal = action.payload; },
    setAccounts: (state, action) => { state.accounts = action.payload; },
    setAdvertisers: (state, action) => { state.advertisers = action.payload; },
    setTiktokIntegrated: (state, action) => { state.tiktokIntegrated = action.payload; },
    setShowCancelIntegrationModal: (state, action) => { state.showCancelIntegrationModal = action.payload; },
    setShowCancelIntegrationModal2: (state, action) => { state.showCancelIntegrationModal2 = action.payload; },
    setIntegratedAlready: (state, action) => { state.integratedAlready = action.payload; },
    setTiktokPages: (state, action) => { state.tiktokPages = action.payload; },
    setLoadingPages: (state, action) => { state.loadingPages = action.payload; },
    setSelectedAdvertiser: (state, action) => { state.selectedAdvertiser = action.payload; },
    setShowTikTokAuthModal: (state, action) => { state.showTikTokAuthModal = action.payload; },
    setUserPages: (state, action) => { state.userPages = action.payload; },
    // Clear error actions
    clearAuthError: (state) => { state.authError = null; },
    clearDisconnectError: (state) => { state.disconnectError = null; },
    clearFetchPagesError: (state) => { state.fetchPagesError = null; },
    clearDownloadLeadsError: (state) => { state.downloadLeadsError = null; },
  },
  extraReducers: (builder) => {
    builder
      // Sign in with platform
      .addCase(signInWithPlatform.pending, (state) => {
        state.isAuthenticating = true;
        state.authError = null;
      })
      .addCase(signInWithPlatform.fulfilled, (state, action) => {
        state.isAuthenticating = false;
        // Additional state updates can be handled here based on the response
      })
      .addCase(signInWithPlatform.rejected, (state, action) => {
        state.isAuthenticating = false;
        state.authError = action.payload;
      })

      // Disconnect from Facebook
      .addCase(disconnectFromFacebook.pending, (state) => {
        state.isDisconnecting = true;
        state.disconnectError = null;
      })
      .addCase(disconnectFromFacebook.fulfilled, (state) => {
        state.isDisconnecting = false;
        state.integratedAlready = false;
        state.showCancelIntegrationModal = false;
      })
      .addCase(disconnectFromFacebook.rejected, (state, action) => {
        state.isDisconnecting = false;
        state.disconnectError = action.payload;
      })

      // Disconnect from TikTok
      .addCase(disconnectFromTiktok.pending, (state) => {
        state.isDisconnecting = true;
        state.disconnectError = null;
      })
      .addCase(disconnectFromTiktok.fulfilled, (state) => {
        state.isDisconnecting = false;
        state.tiktokIntegrated = false;
        state.showCancelIntegrationModal2 = false;
      })
      .addCase(disconnectFromTiktok.rejected, (state, action) => {
        state.isDisconnecting = false;
        state.disconnectError = action.payload;
      })

      // Fetch TikTok pages
      .addCase(fetchTikTokPages.pending, (state) => {
        state.isFetchingPages = true;
        state.fetchPagesError = null;
      })
      .addCase(fetchTikTokPages.fulfilled, (state, action) => {
        state.isFetchingPages = false;
        state.tiktokPages = action.payload;
        state.loadingPages = false;
      })
      .addCase(fetchTikTokPages.rejected, (state, action) => {
        state.isFetchingPages = false;
        state.fetchPagesError = action.payload;
        state.loadingPages = false;
      })

      // Download TikTok leads
      .addCase(downloadLeadsTiktok.pending, (state) => {
        state.isDownloadingLeads = true;
        state.downloadLeadsError = null;
      })
      .addCase(downloadLeadsTiktok.fulfilled, (state) => {
        state.isDownloadingLeads = false;
      })
      .addCase(downloadLeadsTiktok.rejected, (state, action) => {
        state.isDownloadingLeads = false;
        state.downloadLeadsError = action.payload;
      });
  },
});

export const {
  setSearchTerm,
  setCurrentPage,
  setFilteredData,
  setShowIntegrationModal,
  setAccounts,
  setAdvertisers,
  setTiktokIntegrated,
  setShowCancelIntegrationModal,
  setShowCancelIntegrationModal2,
  setIntegratedAlready,
  setTiktokPages,
  setLoadingPages,
  setSelectedAdvertiser,
  setShowTikTokAuthModal,
  setUserPages,
  clearAuthError,
  clearDisconnectError,
  clearFetchPagesError,
  clearDownloadLeadsError,
} = integrationSlice.actions;

export default integrationSlice.reducer;
