import { useEffect, useRef, useState, useMemo } from "react";
import { Button, Form, ListGroup, Badge } from "react-bootstrap";
import {
  FaPaperPlane,
  FaPaperclip,
  FaTimes,
  FaArrowLeft,
  FaFilePdf,
  FaImage,
  FaFileVideo,
  FaFileAudio,
} from "react-icons/fa";
import {
  collection,
  doc,
  onSnapshot,
  orderBy,
  query,
  getDocs,
  where,
} from "firebase/firestore";
import { db } from "../../utils/firebase.config";
import { useSelector, useDispatch } from "react-redux";
import "./AdminSupportChatContent.css";
import notificationSound from "../../assets/media/notification_sound.wav";
import { getFileType } from "../../utils/getFileType";
import FilePreviewModal from "../Shared/modals/FilePreviewModal/FilePreviewModal";
import {
  fetchClientChats,
  setActiveClient,
  markClientMessagesAsRead,
  removeUnreadChat,
  setMessages,
  selectClientChats,
  selectActiveClientId,
  selectActiveClientName,
  selectMessages,
  sendMessageAsSupport,
  setMessageText,
  setAttachment,
  selectMessageText,
  selectAttachment,
  selectLoading,
  selectUnreadChats,
} from "../../redux/features/adminSupportChatSlice";
import { format, isToday, isYesterday, isSameDay } from "date-fns";

const AdminSupportChatContent = () => {
  const dispatch = useDispatch();
  const clientChats = useSelector(selectClientChats);
  const activeClientId = useSelector(selectActiveClientId);
  const activeClientName = useSelector(selectActiveClientName);
  const messages = useSelector(selectMessages);
  const messageText = useSelector(selectMessageText);
  const attachment = useSelector(selectAttachment);
  const loading = useSelector(selectLoading);
  const unreadChats = useSelector(selectUnreadChats);
  const user = useSelector((state) => state.auth.user);

  const [searchTerm, setSearchTerm] = useState("");
  const [showClientsList, setShowClientsList] = useState(true);
  const messagesEndRef = useRef(null);
  const messagesRef = useRef(null);
  const notificationSoundRef = useRef(new Audio(notificationSound));
  const prevUnreadChatsRef = useRef([]);
  const isMobileView = window.innerWidth < 768;

  // Add refs for input focus
  const messageInputRef = useRef(null);
  const activeClientRef = useRef(null);

  // Track if the chat is currently focused/active
  const [isChatFocused, setIsChatFocused] = useState(false);

  // Add state to track which messages have their time visible
  const [visibleTimes, setVisibleTimes] = useState({});

  // Function to toggle message time visibility
  const toggleMessageTime = (messageId) => {
    setVisibleTimes((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  // Format time for display
  const formatTime = (timestamp) => {
    if (!timestamp) return "";

    let date;
    if (typeof timestamp === "string") {
      date = new Date(timestamp);
    } else if (timestamp.toDate && typeof timestamp.toDate === "function") {
      date = timestamp.toDate();
    } else {
      date = new Date(timestamp);
    }

    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return (
        date.toLocaleDateString([], {
          month: "short",
          day: "numeric",
        }) +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  };

  // Function to add a temporary message while sending
  const addSendingMessage = (message) => {
    const tempId = `temp-${Date.now()}`;
    setSendingMessages((prev) => [...prev, { ...message, id: tempId }]);
    return tempId;
  };

  // Function to remove a temporary message
  const removeSendingMessage = (tempId) => {
    setSendingMessages((prev) => prev.filter((msg) => msg.id !== tempId));
  };

  // Add this ref to track if initial scroll has happened
  const initialScrollDoneRef = useRef(false);

  // Improve the scroll to bottom function
  const scrollToBottom = (force = false) => {
    if (!messagesEndRef.current) return;

    // Only scroll if forced or if this is the initial load
    if (force || !initialScrollDoneRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      initialScrollDoneRef.current = true;
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messages.length > 0) {
      groupMessagesByDate(messages);

      // Force scroll to bottom on initial load
      if (!initialScrollDoneRef.current) {
        scrollToBottom(true);
      }
    }
  }, [messages]);

  // Reset the initial scroll flag when changing active client
  useEffect(() => {
    if (activeClientId) {
      // Reset the scroll flag when changing clients
      initialScrollDoneRef.current = false;
    }
  }, [activeClientId]);

  // Filter clients based on search term
  const filteredClients = useMemo(() => {
    return clientChats.filter((client) => {
      const clientName = client.client_name || "";
      const latestMessage = client.latest_message || "";
      const searchLower = searchTerm.toLowerCase();
      return (
        clientName.toLowerCase().includes(searchLower) ||
        latestMessage.toLowerCase().includes(searchLower)
      );
    });
  }, [clientChats, searchTerm]);

  // Fetch client chats when component mounts
  useEffect(() => {
    dispatch(fetchClientChats());
    // On mobile, show clients list by default
    if (isMobileView) {
      setShowClientsList(true);
    }
  }, [dispatch, isMobileView]);

  // Set up listener for messages when active client changes
  useEffect(() => {
    if (activeClientId) {
      try {
        // Reset scroll flag when changing clients
        initialScrollDoneRef.current = false;

        // Set up listener for messages
        const chatDocRef = doc(db, "support", activeClientId);
        const messagesCollectionRef = collection(chatDocRef, "messages");
        const messagesQuery = query(
          messagesCollectionRef,
          orderBy("created_time", "asc")
        );

        const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
          const newMessages = snapshot.docs.map((doc) => {
            const data = doc.data();

            // Convert Firebase Timestamp to serializable format
            if (
              data.created_time &&
              typeof data.created_time.toDate === "function"
            ) {
              return {
                id: doc.id,
                ...data,
                created_time: data.created_time.toDate().toISOString(),
              };
            }

            return {
              id: doc.id,
              ...data,
            };
          });

          // Ensure messages are properly sorted by created_time
          const sortedMessages = [...newMessages].sort((a, b) => {
            const dateA = new Date(a.created_time || a.client_time);
            const dateB = new Date(b.created_time || b.client_time);
            return dateA - dateB; // Ascending order (oldest first)
          });

          dispatch(setMessages(sortedMessages));

          // Force scroll to bottom after messages are loaded
          setTimeout(() => {
            scrollToBottom(true);
          }, 100);
        });

        // Mark client messages as read when opening the chat
        dispatch(markClientMessagesAsRead(activeClientId));
        dispatch(removeUnreadChat(activeClientId));

        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error("Error setting up message listener:", error);
      }
    }
  }, [activeClientId, dispatch]);

  // Play notification sound when new unread messages arrive
  useEffect(() => {
    // Check if there are new unread chats compared to previous state
    const hasNewUnreadChats = unreadChats.some(
      (chatId) => !prevUnreadChatsRef.current.includes(chatId)
    );

    // Only play sound if:
    // 1. There are new unread chats AND
    // 2. Either the chat with new messages is not the active one OR the active chat is not focused
    if (
      hasNewUnreadChats &&
      (!unreadChats.includes(activeClientId) || !isChatFocused)
    ) {
      notificationSoundRef.current.play().catch((error) => {
        console.error("Error playing notification sound:", error);
      });
    }

    // Update the previous unread chats reference
    prevUnreadChatsRef.current = unreadChats;
  }, [unreadChats, activeClientId, isChatFocused]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const newIsMobileView = window.innerWidth < 768;
      if (newIsMobileView !== isMobileView) {
        window.location.reload();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [isMobileView]);

  // Maintain focus on message input after sending
  useEffect(() => {
    if (!loading && messageInputRef.current && isChatFocused) {
      messageInputRef.current.focus();
    }
  }, [loading, isChatFocused]);

  // Set up focus tracking for the active chat
  useEffect(() => {
    if (activeClientId) {
      setIsChatFocused(true);

      // Focus the message input when a chat is selected
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }

      // Mark messages as read when the chat is focused
      dispatch(markClientMessagesAsRead(activeClientId));
      dispatch(removeUnreadChat(activeClientId));
    } else {
      setIsChatFocused(false);
    }
  }, [activeClientId, dispatch]);

  const [sendingMessages, setSendingMessages] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [showFilePreview, setShowFilePreview] = useState(false);
  const [showFilePreviewModal, setShowFilePreviewModal] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [dateGroups, setDateGroups] = useState({});

  // Add this function to group messages by date
  const groupMessagesByDate = (messages) => {
    const groups = {};

    messages.forEach((msg) => {
      const date = new Date(msg.created_time);
      const dateKey = date.toISOString().split("T")[0]; // YYYY-MM-DD format

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }

      groups[dateKey].push(msg);
    });

    setDateGroups(groups);
  };

  // Call this in your useEffect where messages are updated
  useEffect(() => {
    if (messages.length > 0) {
      groupMessagesByDate(messages);
    }
    scrollToBottom();
  }, [messages]);

  // Function to get display date for headers
  const getDateDisplay = (dateStr) => {
    const date = new Date(dateStr);

    if (isToday(date)) {
      return "Today";
    } else if (isYesterday(date)) {
      return "Yesterday";
    } else {
      return format(date, "MMMM d, yyyy");
    }
  };

  // Function to handle file preview
  const handleFilePreview = (file) => {
    // Create a file object with the necessary properties for the FilePreviewModal
    const fileForPreview = {
      url: file.message,
      type: file.type || getFileTypeFromUrl(file.message),
      filename: file.filename || getFilenameFromUrl(file.message) || "File",
      // Add a flag to prevent download managers from intercepting
      preventDownload: true,
    };

    // Set the file and show the modal
    setPreviewFile(fileForPreview);
    setShowFilePreviewModal(true);
  };

  // Helper function to get file type from URL
  const getFileTypeFromUrl = (url) => {
    if (!url || typeof url !== "string") return "file";

    const extension = url.split(".").pop().toLowerCase();

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension)) {
      return "image";
    } else if (["mp4", "webm", "mov"].includes(extension)) {
      return "video";
    } else if (["pdf"].includes(extension)) {
      return "pdf";
    } else if (["doc", "docx"].includes(extension)) {
      return "document";
    } else if (["mp3", "wav", "ogg"].includes(extension)) {
      return "audio";
    }

    return "file";
  };

  // Helper function to get filename from URL
  const getFilenameFromUrl = (url) => {
    if (!url || typeof url !== "string") return "File";

    // Extract filename from URL
    const parts = url.split("/");
    let filename = parts[parts.length - 1];

    // Remove query parameters if any
    filename = filename.split("?")[0];

    // Decode URI components
    try {
      filename = decodeURIComponent(filename);
    } catch (e) {
      console.error("Error decoding filename:", e);
    }

    return filename;
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!messageText.trim() && !attachment) return;
    if (!activeClientId) return;

    const supportUserId = user?.user?.id;
    const supportUserName = user?.user?.name || "Support Agent";

    // Create temporary message for UI with all required properties
    const tempMessage = {
      message: messageText,
      sender_id: supportUserId,
      sender_name: supportUserName,
      flag: "support",
      created_time: new Date().toISOString(),
      type: attachment ? getFileType(attachment) : "text",
      filename: attachment?.name,
      id: `temp-${Date.now()}`, // Add a temporary ID
    };

    // Add to sending messages
    setSendingMessages((prev) => [...prev, tempMessage]);

    dispatch(
      sendMessageAsSupport({
        clientId: activeClientId,
        clientName: activeClientName,
        messageText,
        attachment,
        supportUserId,
        supportUserName,
      })
    )
      .then(() => {
        // Remove from sending messages when done
        setSendingMessages((prev) =>
          prev.filter((msg) => msg.id !== tempMessage.id)
        );
      })
      .catch(() => {
        // Also remove on error
        setSendingMessages((prev) =>
          prev.filter((msg) => msg.id !== tempMessage.id)
        );
      });

    // Clear message text after sending
    dispatch(setMessageText(""));

    // Clear attachment if any
    if (attachment) {
      if (attachment.preview) {
        URL.revokeObjectURL(attachment.preview);
      }
      dispatch(setAttachment(null));
    }

    // Set a short timeout to refocus the input after Redux state updates
    setTimeout(() => {
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }
    }, 10);
  };

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      const file = e.target.files[0];
      // Add a preview URL so we can display image thumbnails before sending
      const fileWithPreview = Object.assign(file, {
        preview: URL.createObjectURL(file),
      });
      dispatch(setAttachment(fileWithPreview));
    }
  };

  const removeAttachment = () => {
    // Revoke the preview URL to avoid memory leaks
    if (attachment && attachment.preview) {
      URL.revokeObjectURL(attachment.preview);
    }
    dispatch(setAttachment(null));
  };

  const handleSelectClient = (clientId, clientName) => {
    // Reset scroll flag when selecting a new client
    initialScrollDoneRef.current = false;

    dispatch(setActiveClient({ id: clientId, name: clientName }));

    // Mark client messages as read when selected
    dispatch(markClientMessagesAsRead(clientId));
    dispatch(removeUnreadChat(clientId));

    // Set chat as focused
    setIsChatFocused(true);

    // On mobile, hide clients list when a chat is selected
    if (isMobileView) {
      setShowClientsList(false);
    }

    // Focus the message input after a short delay to allow state updates
    setTimeout(() => {
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }

      // Force scroll to bottom
      scrollToBottom(true);
    }, 100);
  };

  const toggleClientsList = () => {
    setShowClientsList(!showClientsList);
  };

  // Sort clients by last message time
  const sortedClients = [...filteredClients].sort((a, b) => {
    if (!a.lastMessageTime) return 1;
    if (!b.lastMessageTime) return -1;
    return new Date(b.lastMessageTime) - new Date(a.lastMessageTime);
  });

  // Add a function to handle focus events
  const handleChatFocus = () => {
    if (activeClientId) {
      setIsChatFocused(true);
      dispatch(markClientMessagesAsRead(activeClientId));
      dispatch(removeUnreadChat(activeClientId));
    }
  };

  // Add a function to handle blur events
  const handleChatBlur = () => {
    setIsChatFocused(false);
  };

  // Add this function to determine if a message is the latest from its sender
  const isLatestMessageFromSender = (message, index) => {
    // Safely get the date key
    if (!message.created_time) return true; // If no timestamp, assume it's latest

    const dateKey = message.created_time.split("T")[0];
    const messagesForDate = dateGroups[dateKey];

    // If we can't find the messages group, return true to be safe
    if (!messagesForDate || !Array.isArray(messagesForDate)) return true;

    // If it's the last message of the day, it's the latest from its sender
    if (index === messagesForDate.length - 1) return true;

    // Check if the next message is from a different sender
    const nextMessage = messagesForDate[index + 1];
    if (!nextMessage) return true;

    return message.flag !== nextMessage.flag;
  };

  // Create a Message component for sending messages
  const Message = ({ message, isSending }) => {
    return (
      <div
        className={`message-container ${
          message.flag === "support" ? "message-right" : "message-left"
        }`}
      >
        <div
          className={`message-bubble ${
            message.flag === "support" ? "support-message" : "client-message"
          }`}
        >
          <div className="message-content">
            {(() => {
              if (isSending) {
                if (message.type && message.type !== "text") {
                  return (
                    <div className="position-relative">
                      <span>Sending {message.type}...</span>
                      <div
                        className="spinner-border spinner-border-sm ms-2"
                        role="status"
                      >
                        <span className="visually-hidden">Sending...</span>
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div className="position-relative">
                      <span
                        style={{ whiteSpace: "pre-wrap", filter: "blur(1px)" }}
                      >
                        {message.message}
                      </span>
                      <div className="position-absolute top-50 start-50 translate-middle">
                        <div
                          className="spinner-border spinner-border-sm text-light"
                          role="status"
                        >
                          <span className="visually-hidden">Sending...</span>
                        </div>
                      </div>
                    </div>
                  );
                }
              }

              // Default case for non-sending messages
              return (
                <span style={{ whiteSpace: "pre-wrap" }}>
                  {message.message}
                </span>
              );
            })()}
          </div>
          <div className="message-time-inline">
            {message.created_time
              ? format(new Date(message.created_time), "h:mm a")
              : "Sending..."}
          </div>
        </div>
        <div className="message-sender">{message.sender_name}</div>
      </div>
    );
  };

  // Helper to render preview for latest message
  const renderLatestMessagePreview = (client) => {
    let msg = client.latest_message || "";
    if (msg.startsWith("http")) {
      // First, replace `api//public` with `api/public`
      msg = msg.replace(/api\/\/public/, "api/public");

      // Remove any double slashes in the path (leave protocol intact)
      const parts = msg.split("://");
      if (parts.length === 2) {
        const protocol = parts[0];
        let path = parts[1];
        path = path.replace(/\/\/+/, "/"); // replace consecutive slashes with single
        msg = `${protocol}://${path}`;
      }
    }

    // Image URL detection
    const isImageUrl = msg.match(/\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i);
    const isVideoUrl = msg.match(/\.(mp4|mov|webm)(\?.*)?$/i);
    const isAudioUrl = msg.match(/\.(mp3|wav|ogg)(\?.*)?$/i);

    if (isImageUrl) {
      return (
        <img
          src={msg}
          alt="img"
          style={{ width: 28, height: 28, objectFit: "cover", borderRadius: 4 }}
          className="me-1"
        />
      );
    }

    if (isVideoUrl) {
      return (
        <>
          <FaFileVideo className="text-success me-1" />
          <span>Video</span>
        </>
      );
    }

    if (isAudioUrl) {
      return (
        <>
          <FaFileAudio className="text-warning me-1" />
          <span>Audio</span>
        </>
      );
    }

    // Handle [File: filename.ext] pattern
    if (msg.startsWith("[File:")) {
      const filename = msg.slice(6, -1).trim(); // extract between [File: and ]
      const lower = filename.toLowerCase();

      const baseUrl = process.env.REACT_APP_API_URL || "";

      if (lower.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
        const url = `${baseUrl}/public/uploads/images/${filename}`;
        return (
          <img
            src={url}
            alt="img"
            style={{
              width: 28,
              height: 28,
              objectFit: "cover",
              borderRadius: 4,
            }}
            className="me-1"
          />
        );
      }

      if (lower.match(/\.(mp4|mov|webm)$/)) {
        const url = `${baseUrl}/public/uploads/videos/${filename}`;
        return <FaFileVideo className="text-success me-1" />;
      }

      if (lower.match(/\.(mp3|wav|ogg)$/)) {
        const url = `${baseUrl}/public/uploads/audios/${filename}`;
        return <FaFileAudio className="text-warning me-1" />;
      }

      return (
        <>
          <FaPaperclip className="me-1" />
          <span>File</span>
        </>
      );
    }

    return msg;
  };

  return (
    <div className="admin-support-chat-content">
      {/* Clients sidebar */}
      {showClientsList && (
        <div className="clients-sidebar border-success bg-dark text-light">
          <div className="search-container p-2">
            <Form.Control
              type="text"
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mb-2"
            />
          </div>
          <ListGroup variant="flush" className="clients-list">
            {sortedClients.length === 0 ? (
              <div className="text-center text-muted my-3">
                <p>No clients found</p>
              </div>
            ) : (
              sortedClients.map((client) => (
                <ListGroup.Item
                  key={client.id}
                  action
                  active={client.id === activeClientId}
                  onClick={() =>
                    handleSelectClient(
                      client.id,
                      client.client_name || "Unknown Client"
                    )
                  }
                  className={`client-item ${
                    unreadChats.includes(client.id) ? "has-unread" : ""
                  } ${client.id === activeClientId ? "active" : ""}`}
                  ref={client.id === activeClientId ? activeClientRef : null}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <div className="client-name">
                        {client.client_name || "Unknown Client"}
                      </div>
                      <div
                        className={`last-message text-truncate ${
                          client.lastMessageSender === "support"
                            ? "text-primary"
                            : ""
                        }`}
                      >
                        {client.lastMessageSender === "support" ? "You: " : ""}
                        {renderLatestMessagePreview(client) ||
                          "No messages yet"}
                      </div>
                    </div>
                    <div className="d-flex flex-column align-items-end">
                      <small className="text-muted">
                        {client.lastMessageTime
                          ? formatTime(client.lastMessageTime)
                          : ""}
                      </small>
                      {unreadChats.includes(client.id) && (
                        <Badge bg="danger" pill className="mt-1">
                          New
                        </Badge>
                      )}
                    </div>
                  </div>
                </ListGroup.Item>
              ))
            )}
          </ListGroup>
        </div>
      )}

      {/* Chat area with focus handling */}
      <div
        className={`chat-area ${!showClientsList ? "full-width" : ""}`}
        onFocus={handleChatFocus}
        onBlur={handleChatBlur}
        tabIndex="-1" // Make div focusable but not in tab order
      >
        {!activeClientId ? (
          <div className="text-center text-muted my-5">
            <p>Select a client to view their chat</p>
          </div>
        ) : (
          <>
            {/* Mobile toggle button */}
            {isMobileView && (
              <Button
                variant="light"
                className="toggle-clients-btn mb-2"
                onClick={toggleClientsList}
              >
                <FaArrowLeft
                  className={showClientsList ? "" : "flip-horizontal"}
                />
                {showClientsList ? "Hide Clients" : "Show Clients"}
              </Button>
            )}
            <div className="chat-messages-container">
              {messages.length === 0 && sendingMessages.length === 0 ? (
                <div className="text-center text-muted my-5">
                  <p>No messages yet</p>
                </div>
              ) : (
                <>
                  {/* Render messages grouped by date */}
                  {Object.keys(dateGroups)
                    .sort()
                    .map((dateKey) => (
                      <div key={dateKey}>
                        {/* Date header */}
                        <div className="date-header text-center my-3">
                          <span className="date-label px-3 py-1 rounded-pill bg-light">
                            {getDateDisplay(dateKey)}
                          </span>
                        </div>

                        {/* Messages for this date */}
                        {dateGroups[dateKey].map((msg, index) => (
                          <div
                            key={msg.id}
                            className={`message-container ${
                              msg.flag === "support"
                                ? "message-right"
                                : "message-left"
                            }`}
                          >
                            <div
                              className={`message-bubble ${
                                msg.flag === "support"
                                  ? "support-message"
                                  : "client-message"
                              } ${
                                !msg.type || msg.type === "text"
                                  ? "with-text"
                                  : ""
                              }`}
                            >
                              <div className="message-content">
                                {(() => {
                                  // Handle different message types
                                  if (msg.type) {
                                    if (msg.type === "image") {
                                      return (
                                        <img
                                          src={msg.message}
                                          alt="Shared"
                                          className="message-image"
                                          style={{
                                            maxWidth: "200px",
                                            maxHeight: "200px",
                                            cursor: "pointer",
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        />
                                      );
                                    } else if (
                                      msg.type === "file" ||
                                      msg.type === "document" ||
                                      msg.type === "pdf"
                                    ) {
                                      return (
                                        <a
                                          href="#"
                                          className="file-attachment"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        >
                                          📎{" "}
                                          {msg.filename ||
                                            getFilenameFromUrl(msg.message) ||
                                            "View file"}
                                        </a>
                                      );
                                    } else if (msg.type === "audio") {
                                      return (
                                        <audio
                                          controls
                                          className="message-audio"
                                          onClick={(e) => e.stopPropagation()}
                                        >
                                          <source
                                            src={msg.message}
                                            type="audio/mpeg"
                                          />
                                          Your browser does not support the
                                          audio element.
                                        </audio>
                                      );
                                    } else if (msg.type === "video") {
                                      return (
                                        <video
                                          controls
                                          className="message-video"
                                          style={{
                                            maxWidth: "200px",
                                            maxHeight: "200px",
                                            cursor: "pointer",
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        >
                                          <source
                                            src={msg.message}
                                            type="video/mp4"
                                          />
                                          Your browser does not support the
                                          video element.
                                        </video>
                                      );
                                    }
                                  }

                                  // Default case: text message or unknown type
                                  return (
                                    <span style={{ whiteSpace: "pre-wrap" }}>
                                      {msg.message}
                                    </span>
                                  );
                                })()}
                              </div>
                              <div className="message-time-inline">
                                {format(new Date(msg.created_time), "h:mm a")}
                              </div>
                            </div>
                            {isLatestMessageFromSender(msg, index) && (
                              <div className="message-sender">
                                {msg.sender_name}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}

                  {/* Render sending messages separately */}
                  {sendingMessages.length > 0 && (
                    <div>
                      {/* Today header for sending messages if needed */}
                      {messages.length === 0 && (
                        <div className="date-header text-center my-3">
                          <span className="date-label px-3 py-1 rounded-pill bg-light">
                            Today
                          </span>
                        </div>
                      )}

                      {/* Sending messages */}
                      {sendingMessages.map((message) => (
                        <Message
                          key={message.id}
                          message={message}
                          isSending={true}
                        />
                      ))}
                    </div>
                  )}
                </>
              )}
              <div ref={messagesEndRef} />
            </div>

            <Form
              onSubmit={handleSendMessage}
              className="message-input-container mt-3 border-success bg-dark text-light"
            >
              {attachment && (
                <div className="attachment-preview">
                  {attachment.type &&
                  attachment.type.startsWith("image/") &&
                  attachment.preview ? (
                    <img
                      src={attachment.preview}
                      alt={attachment.name}
                      className="attachment-thumbnail me-2 rounded"
                    />
                  ) : (
                    <>
                      <FaFilePdf className="text-danger me-2" size={24} />
                      <span className="attachment-filename text-truncate me-2">
                        {attachment.name}
                      </span>
                    </>
                  )}
                  <Button
                    variant="light"
                    size="sm"
                    onClick={removeAttachment}
                    className="remove-attachment-btn-top rounded-circle d-flex align-items-center justify-content-center"
                    aria-label="Remove attachment"
                    style={{ width: "24px", height: "24px" }}
                  >
                    <FaTimes size={14} />
                  </Button>
                </div>
              )}

              <div className="d-flex">
                <Form.Group className="me-2 position-relative flex-grow-1">
                  <Form.Control
                    ref={messageInputRef}
                    type="text"
                    placeholder="Type your message..."
                    value={messageText}
                    onChange={(e) => dispatch(setMessageText(e.target.value))}
                    disabled={loading}
                    className="message-input"
                    onFocus={() => {
                      if (activeClientId) {
                        dispatch(markClientMessagesAsRead(activeClientId));
                        dispatch(removeUnreadChat(activeClientId));
                      }
                    }}
                  />
                </Form.Group>

                <label className="btn btn-outline-secondary me-2 attachment-btn">
                  <FaPaperclip />
                  <input
                    type="file"
                    hidden
                    onChange={handleFileChange}
                    disabled={loading}
                  />
                </label>

                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading || (!messageText.trim() && !attachment)}
                >
                  {loading ? (
                    <span
                      className="spinner-border spinner-border-sm"
                      role="status"
                      aria-hidden="true"
                    ></span>
                  ) : (
                    <FaPaperPlane />
                  )}
                </Button>
              </div>
            </Form>
          </>
        )}
      </div>

      <FilePreviewModal
        file={selectedFile}
        show={showFilePreview}
        handleClose={() => setShowFilePreview(false)}
      />
      <FilePreviewModal
        show={showFilePreviewModal}
        handleClose={() => setShowFilePreviewModal(false)}
        file={previewFile}
      />
    </div>
  );
};

export default AdminSupportChatContent;
