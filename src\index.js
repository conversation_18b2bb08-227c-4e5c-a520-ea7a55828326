import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { <PERSON><PERSON><PERSON>Router as Router } from "react-router-dom";
import { ThemeProvider } from "react-bootstrap";
import i18n from "./utils/i18n";
import { Provider } from 'react-redux';
import store from './redux/store';

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
    <React.StrictMode>
        <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <Provider store={store}>
                <ThemeProvider dir={i18n.dir()}>
                    <App />
                </ThemeProvider>
            </Provider>
        </Router>
    </React.StrictMode>,
);

