import apiRequest from "../../utils/apiRequest";
import { createFormDataWithAttachments, hasAttachments } from "../../utils/handle-attachments";

// Get all tickets with pagination
const getAllTickets = async (url, signal) => {
    // Log the URL to debug
    return await apiRequest(url, "get", null, {}, signal);
};

// Get a single ticket by ID
const getSingleTicket = async (id, signal) => {
    return await apiRequest(`admin/ticket/${id}`, "get", null, {}, signal);
};

// Create a new ticket
const createTicket = async (data) => {
    // Check if there are attachments in the data
    if (hasAttachments(data)) {
        // Create FormData with attachments
        const formData = createFormDataWithAttachments(data);

        // Send request with FormData and appropriate headers
        return await apiRequest(
            "admin/ticket",
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // If no attachments, proceed with regular JSON request
    return await apiRequest("admin/ticket", "post", data);
};

// Reply to a ticket
const replyToTicket = async (id, data) => {
    // Check if data is already FormData
    if (data instanceof FormData) {

        // Send request with FormData and appropriate headers
        return await apiRequest(
            `admin/ticket/${id}`,
            "post",
            data,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // Check if there are attachments in the data
    if (hasAttachments(data)) {
        // Create FormData with attachments
        const formData = createFormDataWithAttachments(data);

        // Log the FormData entries to debug
        console.log('FormData entries in replyToTicket:');
        for (let pair of formData.entries()) {
            console.log(pair[0], pair[1]);
        }

        // Send request with FormData and appropriate headers
        return await apiRequest(
            `admin/ticket/${id}`,
            "post",
            formData,
            {
                "Content-Type": "multipart/form-data"
            }
        );
    }

    // If no attachments, proceed with regular JSON request
    return await apiRequest(`admin/ticket/${id}`, "post", data);
};

// Search tickets with filters
const searchTickets = async (searchTerm, page = 1, number_of_records = 10, status = null, priority = null, signal) => {
    let url = `admin/ticket?page=${page}&number_of_records=${number_of_records}`;

    if (searchTerm) {
        url += `&search=${encodeURIComponent(searchTerm)}`;
    }

    if (status && status !== "All") {
        url += `&status=${encodeURIComponent(status)}`;
    }

    if (priority && priority !== "All") {
        url += `&priority=${encodeURIComponent(priority)}`;
    }

    console.log("Searching admin tickets with URL:", url);
    return await apiRequest(url, "get", null, {}, signal);
};

// Update ticket status
const updateTicketStatus = async (id, status) => {
    return await apiRequest(`admin/ticket/${id}/status`, "post", { status });
};

// Update ticket priority
const updateTicketPriority = async (id, priority) => {
    return await apiRequest(`admin/ticket/${id}/priority`, "post", { priority });
};

// Remove the assignTicket function
// const assignTicket = async (id, adminId) => {
//     return await apiRequest(`admin/ticket/${id}/assign`, "post", { admin_id: adminId });
// };

export {
    getAllTickets,
    getSingleTicket,
    createTicket,
    replyToTicket,
    searchTickets,
    updateTicketStatus,
    updateTicketPriority,
    // Remove assignTicket from exports
};
