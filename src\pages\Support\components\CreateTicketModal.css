/* Styles for the CreateTicketModal using React Bootstrap components */

/* Dropzone styles */
.dropzone {
  border: 2px dashed #dee2e6;
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: #f8f9fa;
}

.dropzone:hover {
  border-color: #0d6efd;
}

.dropzone.active {
  border-color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

/* Enhanced attachment grid styles */
.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.attachment-card {
  border: 1px solid #dee2e6;
  border-radius: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.attachment-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #adb5bd;
}

.attachment-preview {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 15px;
  flex-grow: 1;
}

.img-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.file-icon-container {
  font-size: 3.5rem;
  color: #6c757d;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.file-icon-container svg {
  margin-bottom: 10px;
}

.file-icon-container .file-type {
  font-size: 0.8rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.attachment-info {
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.attachment-filename {
  font-size: 0.9rem;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #495057;
}

.remove-attachment-btn {
  width: 100%;
  font-size: 0.85rem;
  padding: 6px 12px;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .attachments-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }

  .attachment-preview {
    height: 120px;
  }
}

@media (min-width: 992px) {
  .attachments-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.attachments-preview h6 {
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #6c757d;
}

/* General form styling if needed, though React Bootstrap handles most of it */
.ticket-form .form-label {
  font-weight: 500; /* Make labels slightly bolder */
}

.ticket-form .form-control:focus {
  border-color: #80bdff; /* Bootstrap focus color */
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Bootstrap focus shadow */
}

/* Modal Footer buttons - React Bootstrap Button variants will handle styling,
   but you can add custom classes if needed for specific spacing or overrides. */
.modal-footer .cancel-btn {
  margin-right: 0.5rem; /* Space between cancel and submit */
}

/* Ensure the modal itself is scrollable if content overflows */
.modal-body {
  max-height: 60vh; /* Adjust as needed */
  overflow-y: auto;
}
