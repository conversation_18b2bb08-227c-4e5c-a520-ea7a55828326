import { Badge, Dropdown, Stack, Modal } from "react-bootstrap";
import userImg from "../../../assets/media/user.png";
import headerLogo from "../../../assets/media/logo.svg";
import "./Header.css";
import { FaBell } from "react-icons/fa6";
import { Link } from "react-router-dom";
import { IoIosLogIn, IoIosLogOut } from "react-icons/io";
import useAuth from "../../../redux/hooks/useAuth";
import { useEffect, useState } from "react";
import getAllNotificationsApi from "../../../services/notifications/get-all-notifications.api";
import readSingleNotificationApi from "../../../services/notifications/read-notification.api";
import NotificationItem from "./NotificationItem.component";
import markAllAsReadApi from "../../../services/notifications/mark-all.api";
import { TbBellZFilled } from "react-icons/tb";
import getCurrentFormattedDate from "../../../utils/get-current-formatted-date";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";
import { useLocation } from "react-router-dom";
import { showErrorToast } from "../../../utils/toast-success-error";

const HeaderComponent = () => {
  const { user, logout } = useAuth();
  const userData = user?.user;
  const [notifications, setNotifications] = useState({
    all: [],
    read: [],
    unread: [],
    count: 0,
    activeTab: "all",
  });
  const [showNotifications, setShowNotifications] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (showNotifications) {
      const modalBody = document.querySelector(".notifications-menu");
      const header = document.querySelector(".notifications-header");

      const handleScroll = () => {
        if (modalBody.scrollTop > 0) {
          header.classList.add("shadow");
        } else {
          header.classList.remove("shadow");
        }
      };

      modalBody.addEventListener("scroll", handleScroll);
      return () => modalBody.removeEventListener("scroll", handleScroll);
    }
  }, [showNotifications]);

  useEffect(() => {
    if (!user) return;
    const fetchData = async () => {
      try {
        const response = await getAllNotificationsApi();
        const count = response?.data?.count;
        const allNotifications = response?.data?.notifications;
        const unreadNotifications = allNotifications?.filter(
          (notification) => !notification?.read_at
        );
        const readNotifications = allNotifications.filter(
          (notification) => notification?.read_at
        );

        setNotifications({
          all: allNotifications,
          read: readNotifications,
          unread: unreadNotifications,
          count: count,
          activeTab: "all",
        });
      } catch (error) {
        console.error("Error fetching notifications:", error);
        // Handle error, show error message, or redirect if needed
      }
    };

    fetchData();
  }, []);

  const readSingleNotification = async (notificationId) => {
    try {
      await readSingleNotificationApi(notificationId);

      setNotifications((prevNotifications) => {
        if (!prevNotifications) {
          return prevNotifications;
        }

        const updatedNotifications = prevNotifications.all.map(
          (notification) => {
            if (notification.id === notificationId) {
              return { ...notification, read_at: getCurrentFormattedDate() };
            }
            return notification;
          }
        );

        const updatedUnreadNotifications = updatedNotifications.filter(
          (notification) => !notification?.read_at
        );
        const updatedReadNotifications = updatedNotifications.filter(
          (notification) => notification?.read_at
        );

        // Decrement count when marking a notification as read
        const updatedCount =
          prevNotifications?.count > 0 ? prevNotifications?.count - 1 : 0;

        return {
          ...prevNotifications,
          all: updatedNotifications,
          read: updatedReadNotifications,
          unread: updatedUnreadNotifications,
          count: updatedCount,
        };
      });
    } catch (error) {
      showErrorToast(
        error.response?.data?.message || "Error marking notification as read"
      );
    }
  };

  const markAllAsRead = async () => {
    try {
      await markAllAsReadApi();

      // Update the state to mark all unread notifications as read
      setNotifications((prevNotifications) => ({
        ...prevNotifications,
        all: prevNotifications?.all?.map((notification) => ({
          ...notification,
          read_at: getCurrentFormattedDate(),
        })),
        read: [...prevNotifications?.read, ...prevNotifications?.unread],
        unread: [],
        count: 0,
        activeTab: "all", // Set the activeTab to 'all' after marking all as read
      }));
    } catch (error) {
      showErrorToast(error?.response?.data?.message || "An error occurred");
    }
  };

  const conditionalUserImg = user?.user?.photo
    ? process.env.REACT_APP_PROFILE_PIC_ENDPOINT + user?.user?.photo
    : userImg;

  const location = useLocation();
  const routeState = {
    from: location.pathname,
    signin: location.state?.signin,
  };

  return (
    <>
      <div className="header-container">
        <Stack
          direction={"horizontal"}
          className={
            "justify-content-between align-items-center flex-column flex-lg-row"
          }
        >
          <Link to={"/"}>
            <img
              src={headerLogo}
              width={135}
              height={40}
              alt={"DV Connect Logo"}
            />
          </Link>
          {/*<div className={"search-box my-2"}>*/}
          {/*    <div className={"search-box_input"}>*/}
          {/*        <InputGroup>*/}
          {/*            <Form.Control*/}
          {/*                type={"search"}*/}
          {/*                placeholder={"Search"}*/}
          {/*                aria-label="Large"*/}
          {/*                aria-describedby="inputGroup-sizing-sm"*/}
          {/*            />*/}
          {/*        </InputGroup>*/}
          {/*        <BiSearch className={"search-icon"} size={30} color={"white"}/>*/}
          {/*    </div>*/}
          {/*</div>*/}
          {user ? (
            <div
              className={"d-flex justify-content-between align-items-center"}
            >
              <div
                className="d-inline mx-2 notification-dropdown"
                onClick={() => setShowNotifications(true)}
                role="button"
              >
                <FaBell size={25} color={"white"} />
                <Badge pill className={"notification-badge"}>
                  {notifications?.count || 0}
                </Badge>
              </div>
              <div className={"mx-3 text-white"}>
                {userData?.name || "User"}
              </div>
              <div
                className={
                  "d-flex justify-content-center align-items-center gap-3"
                }
              >
                {/*<LanguageSwitcher />*/}
                <Dropdown className="d-inline mx-2 profile-dropdown-container">
                  <Dropdown.Toggle
                    id="dropdown-autoclose-true"
                    className={"profile-dropdown-button"}
                  >
                    <img
                      src={conditionalUserImg}
                      alt={"Profile Pic"}
                      className={"profile-picture"}
                    />
                  </Dropdown.Toggle>

                  <Dropdown.Menu className={"profile-dropdown-menu p-3"}>
                    <div className={"text-center"}>
                      <img
                        src={conditionalUserImg}
                        alt={"Profile Pic"}
                        className={"profile-picture-dropdown"}
                      />
                      <p>{userData?.name || "User"}</p>
                      <Link to={"/account-settings"}>
                        <button className={"pricing-button text-uppercase"}>
                          profile
                        </button>
                      </Link>
                      <div className={"mt-3"}>
                        <div>{t("header.userGuide")}</div>
                        <div>{t("header.support")}</div>
                        {user ? (
                          <div
                            className={
                              "d-flex justify-content-center text-danger gap-2"
                            }
                            onClick={() => logout()}
                            role={"button"}
                          >
                            <div>{t("header.logout")}</div>
                            <div>
                              <IoIosLogOut />
                            </div>
                          </div>
                        ) : (
                          <Link
                            to={"/client/login"}
                            state={routeState}
                            className={
                              "d-flex justify-content-between text-success"
                            }
                          >
                            <div>{t("header.login")}</div>
                            <div>
                              <IoIosLogIn />
                            </div>
                          </Link>
                        )}
                      </div>
                    </div>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </div>
          ) : (
            <Link
              state={routeState}
              to={"/client/login"}
              className={"d-flex justify-content-between text-white"}
            >
              <div>{t("header.login")}</div>
              <div>
                <IoIosLogIn />
              </div>
            </Link>
          )}
        </Stack>
      </div>

      <Modal
        show={showNotifications}
        onHide={() => setShowNotifications(false)}
        centered
        className="notifications-modal"
      >
        <Modal.Body className="notifications-menu">
          <div className="notifications-header">
            {notifications?.unread?.length > 0 ? (
              <div className="w-100 d-flex justify-content-between align-items-center">
                <div className="d-flex flex-column">
                  <div className="mainColor fw-bold fs-6">
                    New for you{" "}
                    {notifications?.unread?.length
                      ? notifications?.unread?.length
                      : null}
                  </div>
                  <small className="text-muted">
                    Read Notifications{" "}
                    {notifications?.read?.length
                      ? notifications?.read?.length
                      : null}
                  </small>
                </div>

                <div onClick={markAllAsRead} role="button">
                  Mark All As Read
                </div>
              </div>
            ) : (
              <div className="mainColor fw-bold fs-6 text-center">
                You're all caught up! No new notifications.
              </div>
            )}
          </div>

          <div className="notifications-content">
            {notifications.all.length > 0 ? (
              <NotificationItem
                userImg={conditionalUserImg}
                notifications={notifications?.all}
                onRead={readSingleNotification}
              />
            ) : (
              <center className="empty-placeholder">
                <TbBellZFilled size={100} className="mainColor opacity-50" />
              </center>
            )}
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default HeaderComponent;
