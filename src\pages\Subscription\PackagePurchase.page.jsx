import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { showErrorToast } from "../../utils/toast-success-error";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import useAuth from "../../redux/hooks/useAuth";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";

const PackagePurchasePage = () => {
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { setPackageData, handlePurchase, user, packageData, processing } =
    useAuth();
  // Parse URL parameters on component mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const total = searchParams.get("total");
    const productname = searchParams.get("productname");
    const packageId = Number(searchParams.get("packageId"));

    if (total && productname) {
      // Save to session storage
      const data = { total, productname, packageId };
      sessionStorage.setItem("packagePurchase", JSON.stringify(data));
      setPackageData(data);
    } else {
      // Try to get from session storage if not in URL
      const storedData = sessionStorage.getItem("packagePurchase");
      if (storedData) {
        setPackageData(JSON.parse(storedData));
      } else {
        // No data found, redirect to subscription page
        showErrorToast("No package information found");
        navigate("/subscription");
      }
    }
    setLoading(false);
  }, []);

  // Check if user is logged in
  useEffect(() => {
    if (packageData && !user) {
      // User is not logged in, show login modal
      setShowLoginModal(true);
    }
  }, [packageData, user]);

  // Handle login modal actions
  const handleLoginRedirect = () => {
    navigate("/client/login", { state: { signin: false } });
  };

  if (loading) {
    return <FetchingDataLoading />;
  }

  return (
    <div className="container mt-5">
      <Modal
        show={showLoginModal}
        onHide={() => handleLoginRedirect()}
        centered
      >
        <Modal.Header>
          <Modal.Title>Login Required</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>You need to login to purchase this package.</p>
          <p>
            Package: <strong>{packageData?.productname}</strong>
          </p>
          <p>
            Price: <strong>{packageData?.total}</strong>
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => {
              sessionStorage.removeItem("packagePurchase");
              navigate("/subscription");
            }}
          >
            Cancel
          </Button>
          <Button variant="primary" onClick={handleLoginRedirect}>
            Login
          </Button>
        </Modal.Footer>
      </Modal>

      {!showLoginModal && (
        <div className="text-center p-5 shadow-sm rounded">
          <h2>Complete Your Purchase</h2>
          <div className="my-4 p-4 border rounded bg-light">
            <h4>Package Details</h4>
            <p>
              Package: <strong>{packageData?.productname}</strong>
            </p>
            <p>
              Price: <strong>${packageData?.total}</strong>
            </p>
          </div>

          <Button
            variant="primary"
            size="lg"
            onClick={() => handlePurchase()}
            disabled={processing}
            className="mt-3"
          >
            {processing ? (
              <>
                <span
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                  aria-hidden="true"
                ></span>
                Processing...
              </>
            ) : (
              "Complete Purchase"
            )}
          </Button>

          <Button
            variant="outline-secondary"
            className="mt-3 ms-3"
            onClick={() => navigate("/subscription")}
          >
            Cancel
          </Button>
        </div>
      )}
    </div>
  );
};

export default PackagePurchasePage;
