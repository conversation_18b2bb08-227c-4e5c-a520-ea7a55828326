import React from "react";
import Container from "react-bootstrap/Container";
import Modal from "react-bootstrap/Modal";
import { <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import Form from "react-bootstrap/Form";
import { Formik } from "formik";
import * as Yup from "yup";
import useAdmin from "../../redux/hooks/useAdmin";

const EditClientModal = (props) => {
  const { showEditModal, setShowEditModal, modalData } = props;
  const clientData = modalData?.values;
  const handleClose = () => setShowEditModal(false);
  const { handleUpdateClient } = useAdmin();

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      await handleUpdateClient(values, clientData, handleClose).unwrap();
    } catch (error) {
      console.error("Error updating client:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const editClientValidationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    phone: Yup.string().required("Phone is required"),
  });
  return (
    <Modal
      {...props}
      show={showEditModal}
      aria-labelledby="contained-modal-title-vcenter"
      centered
      // className={"integration-modal admin-theme"}
    >
      <Formik
        initialValues={{
          name: clientData?.name || "",
          email: clientData?.email || "",
          phone: clientData?.phone || "",
        }}
        onSubmit={handleSubmit}
        validationSchema={editClientValidationSchema}
      >
        {({ handleSubmit, handleChange, values, touched, errors }) => (
          <Form
            noValidate
            onSubmit={handleSubmit}
            className={"admin-theme text-white"}
          >
            <Container>
              <Row>
                <div
                  className={
                    "d-flex align-items-center flex-column text-center my-3"
                  }
                >
                  <h2>Edit Data for</h2>
                  <span>{clientData.name}</span>
                </div>
                <Form.Group controlId={"name"} className={"mb-3"}>
                  <Form.Label>Name</Form.Label>
                  <Form.Control
                    name={"name"}
                    className={"mb-3"}
                    type="text"
                    placeholder="Name"
                    value={values.name}
                    onChange={handleChange}
                    isValid={touched.name && !errors.name}
                    isInvalid={touched.name && errors.name}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.name}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group controlId={"email"} className={"mb-3"}>
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    name={"email"}
                    className={"mb-3"}
                    type="email"
                    placeholder="Email"
                    value={values.email}
                    onChange={handleChange}
                    isValid={touched.email && !errors.email}
                    isInvalid={touched.email && errors.email}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.email}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group controlId={"phone"} className={"mb-3"}>
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    name={"phone"}
                    className={"mb-3"}
                    type="text"
                    placeholder="Phone"
                    value={values.phone}
                    onChange={handleChange}
                    isValid={touched.phone && !errors.phone}
                    isInvalid={touched.phone && errors.phone}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.phone}
                  </Form.Control.Feedback>
                </Form.Group>
              </Row>
              <center className={"my-3"}>
                <Button
                  className={"submit-btn text-uppercase fs-6 py-2 px-3"}
                  type={"submit"}
                >
                  Save
                </Button>
                <Button
                  className={"text-uppercase ms-3 rounded-pill"}
                  variant={"outline-danger"}
                  onClick={handleClose}
                >
                  Cancel
                </Button>
              </center>
            </Container>
          </Form>
        )}
      </Formik>
    </Modal>
  );
};

export default EditClientModal;
