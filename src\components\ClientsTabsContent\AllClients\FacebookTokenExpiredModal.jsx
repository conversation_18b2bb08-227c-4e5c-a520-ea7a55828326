import { Button } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { HiMiniXMark } from "react-icons/hi2";

const FacebookTokenExpiredModal = ({ onHide }) => {
  const navigate = useNavigate();

  const handleReintegrate = () => {
    navigate("/integrations");
    onHide();
  };

  return (
    <div className="text-center d-flex flex-column align-items-center justify-content-between px-4 pb-4 container">
      <div className="position-relative w-100 py-3">
            <h4 className="mb-4 w-75 mx-auto">Facebook Integration Expired</h4>
          <div className={"member-modal-close-icon"} onClick={onHide}><HiMiniXMark color={"#E35757"} size={25}/></div>
          </div>

      <p className="mb-4">
        Your integration with Facebook has expired. Please reintegrate to continue accessing your leads.
      </p>
      <div className="d-flex gap-3">
        <Button className="submit-btn" onClick={handleReintegrate}>
          Reintegrate
        </Button>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </div>
    </div>
  );
};

export default FacebookTokenExpiredModal;