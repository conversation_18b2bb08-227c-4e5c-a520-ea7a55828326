import ActiveProjectsTable from "./ActiveProjectsTable";
import React, {useMemo, useState} from "react";
import person1 from "../../assets/media/Icons/Ellipse2.svg";
import person2 from "../../assets/media/Icons/Ellipse4.svg";
import person3 from "../../assets/media/Icons/Ellipse5.svg";
import { useTranslation } from "react-i18next";

const StatisticsForClient = () => {
    const { t } = useTranslation();
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const columns = useMemo(() => [
        {
            Header: t('tables.headers.id'),
            accessor: 'clientId',
            Cell: ({ row }) => <span>{row.index + 1}</span>,
        },
        {
            Header: t('tables.headers.employeeName'),
            accessor: "name",
            Cell: ({ row }) => (
                <div className="d-flex justify-content-evenly align-items-center">
                    <img src={row.original.img} alt="Person" className="w-6 h-6 rounded-full mr-2" />
                    <div className={"d-flex justify-content-between flex-column"}>
                        <span>{row.original.name}</span>
                        <span>{row.original.title}</span>
                    </div>
                </div>
            )
        },
        {
            Header: t('tables.headers.emailAddress'),
            accessor: "email"
        },
        {
            Header: t('tables.headers.contactNumber'),
            accessor: "phone"
        },
        {
            Header: t('tables.headers.gender'),
            accessor: "gender",
        },
        {
            Header: t('tables.headers.location'),
            accessor: "location",
        },
        {
            Header: t('tables.headers.status'),
            accessor: "status",
            Cell: ({ value, row }) => (
                row.original.status === "Active" ?
                    <span className="completed-status">{t('status.active')}</span> :
                    row.original.status === "On Hold" ?
                        <span className="on-hold-status">{t('status.onHold')}</span> :
                        row.original.status === "In Progress" ?
                            <span className="in-progress-status">{t('status.inProgress')}</span> :
                            row.original.status === "Pending"
                                ? <span className="not-started-status">{t('status.pending')}</span> :
                                <span>{value}</span>
            )
        }
    ],[t]);

    const data = [
        { img: person1, name: "John Doe", title: "Team Lead", email: "<EMAIL>", phone: "555-1234", gender: "Male", location: "New York", status: "Active"},
        { img: person2, name: "Jane Smith", title: "Team Lead", email: "<EMAIL>", phone: "555-5678", gender: "Female", location: "Los Angeles", status: "Pending"},
        { img: person3, name: "Bob Johnson", title: "Team Lead", email: "<EMAIL>", phone: "555-9012", gender: "Male", location: "Chicago", status: "Active"}
    ]
    const indexedData = data.map((item, index) => ({...item, clientId: index + 1}));

    const tableHeader = () => {
        return (
            <div className={"fs-5 fw-bold text-white mb-3"}>
                {t('tables.titles.employees')}
            </div>
        )
    }

    return (
        <ActiveProjectsTable
            columns={columns}
            data={indexedData}
            classNames={"admin-theme"}
            loading={false}
            recordsPerPage={recordsPerPage}
            setRecordsPerPage={setRecordsPerPage}
            totalPages={Math.ceil(indexedData.length / recordsPerPage)}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            tableHeader={tableHeader()}
        />
    );
};

export default StatisticsForClient;
