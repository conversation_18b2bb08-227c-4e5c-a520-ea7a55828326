.admin-page {
    background-color: rgb(0, 0, 0);
}

body:has(.admin-page) {
    background-color: rgb(0, 0, 0);
}

.dashboard-header {
    border-radius: 0 0 6px 6px;
    box-shadow: 0 -1px 55px 0 rgba(0, 0, 0, 0.1);
    background: rgb(255, 255, 255);
}

.adminPic svg {
    width: 2rem;
    height: 2rem;
}

.pieChart-widget .card-body {
    padding: 0 0 0 10px;
}

.completed-label::before {
    content: '';
    display: inline-block;
    border-radius: 3px;
    background-color: #3AC977;
    margin-right: 5px;
    width: 10px;
    height: 10px;
}

.pending-label::before {
    content: '';
    display: inline-block;
    border-radius: 3px;
    background-color: #0D99FF;
    margin-right: 5px;
    width: 10px;
    height: 10px;
}

.not-started-label::before {
    content: '';
    display: inline-block;
    border-radius: 3px;
    background-color: #FF9F00;
    margin-right: 5px;
    width: 10px;
    height: 10px;
}

.cancelled-label::before {
    content: '';
    display: inline-block;
    border-radius: 3px;
    background-color: #FF5E5E;
    margin-right: 5px;
    width: 10px;
    height: 10px;
}

.assignee-container {
    position: relative;
}

.assignee-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.assignee-image {
    border: 2px solid white; /* Optional: add a border to distinguish overlapping images */
    border-radius: 50%; /* Optional: round the corners of the images */
    margin-left: -10px; /* Adjust the negative margin to control the overlap */
}

.completed-status {
    border-radius: 3px;
    background: rgba(58, 201, 119, 0.10);
    color: #3AC977;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.on-hold-status {
    border-radius: 3px;
    background: rgba(191, 137, 0, 0.21);
    color: #E4AB15;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.not-started-status {
    border-radius: 3px;
    background: rgba(255, 94, 94, 0.15);
    color: #FF5E5E;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.in-progress-status {
    border-radius: 3px;
    background: #1E1E1E;
    color: #0D99FF;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.worldmap__figure-caption {
    color: white;
}

.progress-bar-container {
    display: flex;
    justify-content: space-between;
}

.admin-theme .import-leads, .admin-theme .export-leads, .admin-theme .clear-filter {
    color: #FFFFFF;
    border-radius: 10px;
}

.admin-theme .import-leads:hover, .import-leads:focus, .export-leads:hover, .export-leads:focus, .clear-filter:hover, .clear-filter:focus{
    background: #FFFFFF;
    color: #242424;
    transition: all 0.25s ease-in-out;
}

.leads-count-widget {
    border: 1px solid rgb(68, 68, 68);
    border-radius: 8px;
    background: rgb(36, 36, 36);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: white;
}

.select-client {
    min-width: 250px;
    max-width: 250px;
}