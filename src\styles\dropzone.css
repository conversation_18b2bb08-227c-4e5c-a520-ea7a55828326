.upload-container {
    width: 100%;
    margin-bottom: 1rem;
}

.dropzone-container {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    background-color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropzone-container:hover {
    border-color: #666;
}

.dropzone-container.active {
    border-color: #28a745;
}

.dropzone-container.error {
    border-color: #dc3545;
}

.upload-placeholder {
    text-align: center;
    color: #666;
}

.upload-placeholder p {
    margin-bottom: 8px;
}

.upload-placeholder em {
    font-size: 0.9em;
    color: #888;
}

.file-preview-container {
    width: 100%;
}

.file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.file-name {
    font-size: 0.9em;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80%;
}

.remove-file-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.preview-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;
    background-color: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
}

.file-preview-image {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}

.pdf-preview {
    width: 100%;
    height: 300px;
}

.pdf-preview embed {
    width: 100%;
    height: 100%;
}

.document-preview {
    text-align: center;
    color: #666;
}

.document-preview svg {
    margin-bottom: 10px;
}

.document-preview p {
    margin-bottom: 8px;
}

.document-preview a {
    color: #0d6efd;
    text-decoration: none;
}

.document-preview a:hover {
    text-decoration: underline;
}

.error-message {
    color: #dc3545;
    font-size: 0.9em;
    margin-top: 5px;
}
