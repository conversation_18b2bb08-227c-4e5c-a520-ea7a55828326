import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  collection,
  doc,
  getDocs,
  query,
  orderBy,
  onSnapshot,
  addDoc,
  serverTimestamp,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import { getFileType } from '../../utils/getFileType';
import uploadAttachmentApi from '../../services/upload-attachments';

// Helper function to convert Firebase timestamps to ISO strings
const convertTimestamps = (obj) => {
  if (!obj) return obj;

  const newObj = { ...obj };

  // Convert known timestamp fields
  if (newObj.created_at && typeof newObj.created_at.toDate === 'function') {
    newObj.created_at = newObj.created_at.toDate().toISOString();
  }

  if (newObj.lastMessageTime && typeof newObj.lastMessageTime.toDate === 'function') {
    newObj.lastMessageTime = newObj.lastMessageTime.toDate().toISOString();
  }

  return newObj;
};

// Initial state
const initialState = {
  clientChats: [],
  activeClientId: null,
  activeClientName: null,
  messages: [],
  messageText: '',
  attachment: null,
  loading: false,
  error: null,
  unreadChats: [],
  showChatModal: false,
  isChatFocused: false, // New state property
};

// Async thunks
export const fetchClientChats = createAsyncThunk(
  'adminSupportChat/fetchClientChats',
  async (_, { rejectWithValue }) => {
    try {
      const supportCollectionRef = collection(db, 'support');
      const querySnapshot = await getDocs(supportCollectionRef);

      const chats = querySnapshot.docs.map(doc => {
        const data = doc.data();
        // Convert any Firebase timestamps to ISO strings
        return {
          id: doc.id,
          ...convertTimestamps(data)
        };
      });

      return chats;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const markClientMessagesAsRead = createAsyncThunk(
  'adminSupportChat/markClientMessagesAsRead',
  async (clientId, { rejectWithValue }) => {
    try {
      const chatDocRef = doc(db, 'support', clientId);
      const messagesCollectionRef = collection(chatDocRef, 'messages');
      const unreadMessagesQuery = query(
        messagesCollectionRef,
        where('flag', '==', 'client'),
        where('read', '==', false)
      );

      const querySnapshot = await getDocs(unreadMessagesQuery);

      if (!querySnapshot.empty) {
        const batch = writeBatch(db);

        querySnapshot.forEach((doc) => {
          const messageDocRef = doc.ref;
          batch.update(messageDocRef, { read: true });
        });

        await batch.commit();
      }

      return clientId;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const sendMessageAsSupport = createAsyncThunk(
  'adminSupportChat/sendMessageAsSupport',
  async ({ clientId, clientName, messageText, attachment, supportUserId, supportUserName }, { rejectWithValue }) => {
    try {
      const formattedDate = new Date().toISOString();
      const chatDocRef = doc(db, 'support', clientId);
      const messagesCollectionRef = collection(chatDocRef, 'messages');

      // Client-side timestamp for immediate consistency in UI
      const clientTimestamp = new Date();

      // Handle file upload if attachment exists
      let fileUrl = null;
      let fileType = 'text';
      let filename = null;

      if (attachment && attachment instanceof File) {
        fileType = getFileType(attachment);
        filename = attachment.name;

        // Use uploadAttachmentApi for file uploads
        const formData = new FormData();
        formData.append('file', attachment);

        const response = await uploadAttachmentApi(formData);

        if (response && response.success && response.file_path) {
          // Sanitize URL: remove '/../' and duplicate slashes, collapse
          fileUrl = response.file_path.replace(/\/\.\.\//g, "/");
          fileUrl = fileUrl.replace(/([^:])\/\/+/, '$1/');
          fileUrl = fileUrl.replace(/\/public\/public\//, '/public/');
        } else {
          return rejectWithValue('Upload failed: No URL returned');
        }
      }

      // Create message data - use fileUrl as the message content for attachments
      const messageData = {
        message: fileUrl || messageText, // This sets the file_path as the message content
        sender_id: supportUserId,
        sender_name: supportUserName,
        flag: 'support',
        created_time: serverTimestamp(),
        client_time: clientTimestamp.toISOString(),
        read: false,
        type: attachment ? fileType : 'text', // Explicitly set text type when no attachment
      };

      // Add filename if it exists
      if (filename) {
        messageData.filename = filename;
      }

      // Add new message
      const messageRef = await addDoc(messagesCollectionRef, messageData);

      // Update chat document with latest message info
      await updateDoc(chatDocRef, {
        latest_message: fileUrl || messageText,
        lastMessageTime: formattedDate,
        lastMessageSender: 'support',
        has_unread_from_support: true,
        has_unread_from_client: false,
      });

      // Return data for immediate UI update
      return {
        messageId: messageRef.id,
        messageText: fileUrl || messageText,
        supportUserName,
        supportUserId,
        clientTime: clientTimestamp.toISOString(),
        type: fileType,
        filename: filename
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const setupGlobalAdminChatListener = createAsyncThunk(
  'adminSupportChat/setupGlobalAdminChatListener',
  async (_, { dispatch }) => {
    try {
      const supportCollectionRef = collection(db, 'support');

      const unsubscribe = onSnapshot(supportCollectionRef, (snapshot) => {
        const updatedChats = [];

        snapshot.docChanges().forEach((change) => {
          const chatData = {
            id: change.doc.id,
            ...convertTimestamps(change.doc.data())
          };

          if (change.type === 'added' || change.type === 'modified') {
            // Check if this chat has unread client messages
            const messagesCollectionRef = collection(change.doc.ref, 'messages');
            const unreadMessagesQuery = query(
              messagesCollectionRef,
              where('flag', '==', 'client'),
              where('read', '==', false)
            );

            getDocs(unreadMessagesQuery).then((querySnapshot) => {
              if (!querySnapshot.empty) {
                dispatch(addUnreadChat(change.doc.id));
              }
            });

            updatedChats.push(chatData);
          }
        });

        // Update chats in Redux store
        updatedChats.forEach(chat => {
          dispatch(updateClientChat(chat));
        });
      });

      // Store the unsubscribe function in a global variable or context instead of returning it
      // We'll return a simple success message instead
      window.__adminChatUnsubscribe = unsubscribe;

      return { success: true };
    } catch (error) {
      console.error('Error setting up global admin chat listener:', error);
      return { success: false, error: error.message };
    }
  }
);

// Slice
const adminSupportChatSlice = createSlice({
  name: 'adminSupportChat',
  initialState,
  reducers: {
    setActiveClient: (state, action) => {
      state.activeClientId = action.payload.id;
      state.activeClientName = action.payload.name;
    },
    setMessages: (state, action) => {
      state.messages = action.payload;
    },
    setMessageText: (state, action) => {
      state.messageText = action.payload;
    },
    setAttachment: (state, action) => {
      state.attachment = action.payload;
    },
    addUnreadChat: (state, action) => {
      if (!state.unreadChats.includes(action.payload)) {
        state.unreadChats.push(action.payload);
      }
    },
    removeUnreadChat: (state, action) => {
      state.unreadChats = state.unreadChats.filter(id => id !== action.payload);
    },
    updateClientChat: (state, action) => {
      const payload = action.payload;
      // Ensure payload is serializable (timestamps are already converted by this point)
      const index = state.clientChats.findIndex(chat => chat.id === payload.id);
      if (index !== -1) {
        state.clientChats[index] = payload;
      } else {
        state.clientChats.push(payload);
      }
      // Sort chats by last message time
      state.clientChats.sort((a, b) => {
        if (!a.lastMessageTime) return 1;
        if (!b.lastMessageTime) return -1;
        return new Date(b.lastMessageTime) - new Date(a.lastMessageTime);
      });
    },
    setChatModalVisibility: (state, action) => {
      state.showChatModal = action.payload;
      // Reset state when modal is closed
      if (!action.payload) {
        state.activeClientId = null;
        state.activeClientName = null;
        state.messages = [];
        state.messageText = '';
        state.attachment = null;
      }
    },
    resetChatState: (state) => {
      state.activeClientId = null;
      state.activeClientName = null;
      state.messages = [];
      state.messageText = '';
      state.attachment = null;
    },
    setChatFocusState: (state, action) => {
      state.isChatFocused = action.payload;
    },
    markClientMessagesAsRead: (state, action) => {
      // If the client ID matches the active client, set focus to true
      if (state.activeClientId === action.payload) {
        state.isChatFocused = true;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchClientChats.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchClientChats.fulfilled, (state, action) => {
        state.loading = false;
        state.clientChats = action.payload.sort((a, b) => {
          if (!a.lastMessageTime) return 1;
          if (!b.lastMessageTime) return -1;
          return new Date(b.lastMessageTime) - new Date(a.lastMessageTime);
        });
      })
      .addCase(fetchClientChats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(sendMessageAsSupport.pending, (state) => {
        state.loading = true;
      })
      .addCase(sendMessageAsSupport.fulfilled, (state, action) => {
        state.loading = false;
        state.messageText = '';
        state.attachment = null;
      })
      .addCase(sendMessageAsSupport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const {
  setActiveClient,
  setMessages,
  setMessageText,
  setAttachment,
  addUnreadChat,
  removeUnreadChat,
  updateClientChat,
  setChatModalVisibility,
  resetChatState,
  setChatFocusState,
} = adminSupportChatSlice.actions;

// Selectors
export const selectClientChats = (state) => state.adminSupportChat.clientChats;
export const selectActiveClientId = (state) => state.adminSupportChat.activeClientId;
export const selectActiveClientName = (state) => state.adminSupportChat.activeClientName;
export const selectMessages = (state) => state.adminSupportChat.messages;
export const selectMessageText = (state) => state.adminSupportChat.messageText;
export const selectAttachment = (state) => state.adminSupportChat.attachment;
export const selectLoading = (state) => state.adminSupportChat.loading;
export const selectError = (state) => state.adminSupportChat.error;
export const selectUnreadChats = (state) => state.adminSupportChat.unreadChats;
export const selectShowChatModal = (state) => state.adminSupportChat.showChatModal;
export const selectIsChatFocused = (state) => state.adminSupportChat.isChatFocused;

export default adminSupportChatSlice.reducer;
