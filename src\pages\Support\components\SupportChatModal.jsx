import { useEffect, useRef, useState } from "react";
import { Button, Form } from "react-bootstrap";
import { FaPaper<PERSON>lane, FaPaperclip, FaTimes, FaFilePdf } from "react-icons/fa";
import { useSelector, useDispatch } from "react-redux";
import "./SupportChatModal.css";
import FilePreviewModal from "../../../components/Shared/modals/FilePreviewModal/FilePreviewModal";
import notificationSound from "../../../assets/media/notification_sound.wav";
import {
  sendMessage,
  markMessagesAsRead,
  setMessages,
  selectMessages,
  setupGlobalChatListener,
  setMessageText,
  setAttachment,
  selectMessageText,
  selectAttachment,
  selectLoading,
  selectHasNewMessages,
  selectUnreadCount,
} from "../../../redux/features/supportChatSlice";
import { getFileType } from "../../../utils/getFileType";
import { format, isToday, isYesterday } from "date-fns";

const SupportChatModal = ({ show, onClose }) => {
  const dispatch = useDispatch();
  const messages = useSelector(selectMessages);
  const messageText = useSelector(selectMessageText);
  const attachment = useSelector(selectAttachment);
  const loading = useSelector(selectLoading);
  const hasNewMessages = useSelector(selectHasNewMessages);
  const unreadCount = useSelector(selectUnreadCount);
  const user = useSelector((state) => state.auth.user);

  const messagesEndRef = useRef(null);
  const notificationSoundRef = useRef(new Audio(notificationSound));
  const prevUnreadCountRef = useRef(0);
  const chatListenerRef = useRef(null);
  const messageInputRef = useRef(null);
  const [animationComplete, setAnimationComplete] = useState(false);
  const [sendingMessages, setSendingMessages] = useState([]);
  // Add state to track which messages have their time visible
  const [visibleTimes, setVisibleTimes] = useState({});
  // Add a ref to track if initial scroll has happened
  const initialScrollDoneRef = useRef(false);
  const [dateGroups, setDateGroups] = useState({});

  // Function to toggle time visibility for a specific message
  const toggleMessageTime = (messageId) => {
    setVisibleTimes((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  // Reset visible times when modal is closed
  useEffect(() => {
    if (!show) {
      setVisibleTimes({});
    }
  }, [show]);

  // Scroll to bottom of messages
  const scrollToBottom = (force = false) => {
    if (!messagesEndRef.current) return;

    const chatContainer = messagesEndRef.current.parentElement;
    if (!chatContainer) return;

    // Only scroll if forced or if this is the initial load
    if (force || !initialScrollDoneRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      initialScrollDoneRef.current = true;
    }
  };

  useEffect(() => {
    // Ensure messages are properly sorted before scrolling
    const sortedMessages = [...messages].sort((a, b) => {
      const dateA = new Date(a.created_time);
      const dateB = new Date(b.created_time);
      return dateA - dateB;
    });

    if (JSON.stringify(sortedMessages) !== JSON.stringify(messages)) {
      dispatch(setMessages(sortedMessages));
    }

    // Only scroll on initial load
    if (messages.length > 0 && !initialScrollDoneRef.current) {
      scrollToBottom(true);
    }
  }, [messages, dispatch]);

  // Reset the initial scroll flag when the modal is closed
  useEffect(() => {
    if (!show) {
      initialScrollDoneRef.current = false;
    } else {
      // When modal just opened, ensure we scroll to bottom
      setTimeout(() => {
        scrollToBottom(true);
      }, 0);
    }
  }, [show]);

  // Set up listener for messages when modal opens
  useEffect(() => {
    if (show && user?.user?.id) {
      // Mark messages as read when opening the chat
      dispatch(markMessagesAsRead(user.user.id));

      // Set up listener for messages
      dispatch(setupGlobalChatListener(user.user.id)).then((result) => {
        if (typeof result.payload === "function") {
          chatListenerRef.current = result.payload;
        }
      });
    }

    return () => {
      // Clean up listener when component unmounts
      if (typeof chatListenerRef.current === "function") {
        chatListenerRef.current();
        chatListenerRef.current = null;
      }

      // Reset message text when modal closes
      if (!show) {
        dispatch(setMessageText(""));
        dispatch(setAttachment(null));
      }
    };
  }, [show, dispatch, user?.user?.id]);

  // Play notification sound when new messages arrive
  useEffect(() => {
    // If there are new unread messages and the count has increased, play sound
    if (hasNewMessages && unreadCount > prevUnreadCountRef.current && !show) {
      notificationSoundRef.current.play().catch((error) => {
        console.error("Error playing notification sound:", error);
      });
    }

    // Update the previous unread count reference
    prevUnreadCountRef.current = unreadCount;
  }, [hasNewMessages, unreadCount, show]);

  /*
   * Keep messages marked as read while the chat modal is open.
   * This prevents the global unread counter from increasing in the background
   * when the user is actively viewing the chat.
   */
  useEffect(() => {
    if (show && user?.user?.id && messages.length > 0) {
      dispatch(markMessagesAsRead(user.user.id));
    }
  }, [show, messages, dispatch, user?.user?.id]);

  // Maintain focus on message input after sending
  useEffect(() => {
    if (show && !loading && messageInputRef.current) {
      messageInputRef.current.focus();
    }
  }, [show, loading]);

  // Add this effect to handle the animation
  useEffect(() => {
    if (show) {
      // Small delay to ensure DOM is ready
      setTimeout(() => {
        setAnimationComplete(true);
      }, 50);
    } else {
      setAnimationComplete(false);
    }
  }, [show]);

  const [selectedFile, setSelectedFile] = useState(null);
  const [showFilePreview, setShowFilePreview] = useState(false);
  const [showFilePreviewModal, setShowFilePreviewModal] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);

  // Function to handle file preview
  const handleFilePreview = (file) => {
    // Create a file object with the necessary properties for the FilePreviewModal
    const fileForPreview = {
      url: file.message,
      type: file.type || getFileTypeFromUrl(file.message),
      filename: file.filename || getFilenameFromUrl(file.message) || "File",
      // Add a timestamp or random parameter to prevent caching and interception
      preventDownload: true,
    };

    // Set the file and show the modal
    setPreviewFile(fileForPreview);
    setShowFilePreviewModal(true);
  };

  // Helper function to get file type from URL
  const getFileTypeFromUrl = (url) => {
    if (!url || typeof url !== "string") return "file";

    const extension = url.split(".").pop().toLowerCase().split("?")[0];

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension)) {
      return "image";
    } else if (["mp4", "webm", "mov"].includes(extension)) {
      return "video";
    } else if (["pdf"].includes(extension)) {
      return "pdf";
    } else if (["doc", "docx"].includes(extension)) {
      return "document";
    } else if (["mp3", "wav", "ogg"].includes(extension)) {
      return "audio";
    }

    return "file";
  };

  // Helper function to get filename from URL
  const getFilenameFromUrl = (url) => {
    if (!url || typeof url !== "string") return "File";

    // Extract filename from URL
    const parts = url.split("/");
    let filename = parts[parts.length - 1];

    // Remove query parameters if any
    filename = filename.split("?")[0];

    // Decode URI components
    try {
      filename = decodeURIComponent(filename);
    } catch (e) {
      console.error("Error decoding filename:", e);
    }

    return filename;
  };

  // Function to add a temporary sending message
  const addSendingMessage = (message) => {
    const tempId = `temp_${Date.now()}`;
    setSendingMessages((prev) => [
      ...prev,
      { ...message, id: tempId, isSending: true },
    ]);
    return tempId;
  };

  // Function to remove a sending message when it's done
  const removeSendingMessage = (id) => {
    setSendingMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!messageText.trim() && !attachment) return;
    if (!user?.user?.id) return;

    // Create temporary message for UI
    const tempMessage = {
      message: messageText,
      sender_id: user.user.id,
      sender_name: user.user.name || "User",
      flag: "client",
      created_time: new Date().toISOString(),
      type: attachment ? getFileType(attachment) : "text",
      filename: attachment?.name,
    };

    // Add to sending messages
    const tempId = addSendingMessage(tempMessage);

    // Dispatch the actual send action
    dispatch(
      sendMessage({
        clientId: user.user.id,
        clientName: user.user.name || "User",
        messageText,
        attachment,
      })
    )
      .then(() => {
        // Remove from sending messages when done
        removeSendingMessage(tempId);
      })
      .catch(() => {
        // Also remove on error
        removeSendingMessage(tempId);
      });

    // Clear attachment after sending
    if (attachment) {
      dispatch(setAttachment(null));
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      const file = e.target.files[0];
      // Create preview URL for images so we can show thumbnail
      const fileWithPreview = Object.assign(file, {
        preview: URL.createObjectURL(file),
      });
      dispatch(setAttachment(fileWithPreview));
    }
  };

  const removeAttachment = () => {
    if (attachment && attachment.preview) {
      URL.revokeObjectURL(attachment.preview);
    }
    dispatch(setAttachment(null));
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return "";

    let date;
    // Handle different timestamp formats
    if (typeof timestamp === "string") {
      // ISO string format
      date = new Date(timestamp);
    } else if (timestamp.toDate && typeof timestamp.toDate === "function") {
      // Firebase Timestamp object
      date = timestamp.toDate();
    } else {
      // Regular Date object or timestamp number
      date = new Date(timestamp);
    }

    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return (
        date.toLocaleDateString([], {
          month: "short",
          day: "numeric",
        }) +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  };

  // Function to determine if a message is the latest from its sender
  const isLatestMessageFromSender = (message, index) => {
    // Safely get the date key
    if (!message.created_time) return true; // If no timestamp, assume it's latest

    const dateKey = message.created_time.split("T")[0];
    const messagesForDate = dateGroups[dateKey];

    // If we can't find the messages group, return true to be safe
    if (!messagesForDate || !Array.isArray(messagesForDate)) return true;

    // If it's the last message of the day, it's the latest from its sender
    if (index === messagesForDate.length - 1) return true;

    // Check if the next message is from a different sender
    const nextMessage = messagesForDate[index + 1];
    if (!nextMessage) return true;

    return message.flag !== nextMessage.flag;
  };

  // Add this function before the return statement
  const handleModalClick = (e) => {
    // Prevent clicks inside the modal from propagating to document
    e.stopPropagation();
  };

  // Add this function to group messages by date
  const groupMessagesByDate = (messages) => {
    const groups = {};

    messages.forEach((msg) => {
      if (!msg.created_time) return; // Skip messages without timestamps

      const date = new Date(msg.created_time);
      const dateKey = date.toISOString().split("T")[0]; // YYYY-MM-DD format

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }

      groups[dateKey].push(msg);
    });

    setDateGroups(groups);
  };

  // Function to get display date for headers
  const getDateDisplay = (dateStr) => {
    const date = new Date(dateStr);

    if (isToday(date)) {
      return "Today";
    } else if (isYesterday(date)) {
      return "Yesterday";
    } else {
      return format(date, "MMMM d, yyyy");
    }
  };

  // Call this in your useEffect where messages are updated
  useEffect(() => {
    if (messages.length > 0) {
      groupMessagesByDate(messages);
    }

    // Only scroll on initial load or when explicitly needed
    if (messages.length > 0 && !initialScrollDoneRef.current) {
      scrollToBottom(true);
      initialScrollDoneRef.current = true;
    }
  }, [messages]);

  // Ensure we scroll to bottom when the modal is opened for the first time
  // (after messages have been fetched/rendered)
  useEffect(() => {
    if (show && messages.length > 0 && !initialScrollDoneRef.current) {
      // Small timeout to wait for modal animation/layout
      const timeout = setTimeout(() => {
        scrollToBottom(true);
      }, 100);

      return () => clearTimeout(timeout);
    }
  }, [show, messages]);

  return (
    <>
      <div
        className={`floating-chat-modal-container ${show ? "show" : ""} ${
          animationComplete ? "animation-complete" : ""
        }`}
        onClick={handleModalClick}
      >
        <div className="floating-chat-modal-content">
          <div className="floating-chat-modal-header">
            <h5 className="floating-chat-modal-title">Support Chat</h5>
            <Button
              variant="link"
              className="btn-close"
              onClick={onClose}
              aria-label="Close"
            ></Button>
          </div>
          <div className="floating-chat-modal-body">
            <div className="chat-messages-container">
              {messages.length === 0 && sendingMessages.length === 0 ? (
                <div className="text-center text-muted my-5">
                  <p>
                    No messages yet. Start a conversation with our support team!
                  </p>
                </div>
              ) : (
                <>
                  {/* Render messages grouped by date */}
                  {Object.keys(dateGroups)
                    .sort()
                    .map((dateKey) => (
                      <div key={dateKey}>
                        {/* Date header */}
                        <div className="date-header text-center my-3">
                          <span className="date-label px-3 py-1 rounded-pill bg-light">
                            {getDateDisplay(dateKey)}
                          </span>
                        </div>

                        {/* Messages for this date */}
                        {dateGroups[dateKey].map((msg, index) => (
                          <div
                            key={msg.id}
                            className={`message-container ${
                              msg.flag === "client"
                                ? "message-right"
                                : "message-left"
                            }`}
                          >
                            <div
                              className={`message-bubble ${
                                msg.flag === "client"
                                  ? "client-message"
                                  : "support-message"
                              } ${
                                !msg.type || msg.type === "text"
                                  ? "with-text"
                                  : ""
                              }`}
                            >
                              <div className="message-content">
                                {(() => {
                                  // Check if message has a type property and it's not 'text'
                                  if (msg.type && msg.type !== "text") {
                                    if (msg.type === "image") {
                                      return (
                                        <img
                                          src={msg.message}
                                          alt="Image"
                                          className="message-image"
                                          style={{
                                            maxWidth: "200px",
                                            maxHeight: "200px",
                                            cursor: "pointer",
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        />
                                      );
                                    } else if (
                                      msg.type === "file" ||
                                      msg.type === "document" ||
                                      msg.type === "pdf"
                                    ) {
                                      return (
                                        <a
                                          href="#"
                                          className="file-attachment"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        >
                                          📎{" "}
                                          {msg.filename ||
                                            getFilenameFromUrl(msg.message) ||
                                            "View file"}
                                        </a>
                                      );
                                    } else if (msg.type === "video") {
                                      return (
                                        <video
                                          controls
                                          className="message-video"
                                          style={{
                                            maxWidth: "200px",
                                            maxHeight: "200px",
                                            cursor: "pointer",
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleFilePreview(msg);
                                          }}
                                        >
                                          <source
                                            src={msg.message}
                                            type="video/mp4"
                                          />
                                          Your browser does not support the
                                          video element.
                                        </video>
                                      );
                                    }
                                  }

                                  // Default case: text message or unknown type
                                  return (
                                    <span style={{ whiteSpace: "pre-wrap" }}>
                                      {msg.message}
                                    </span>
                                  );
                                })()}
                              </div>
                              <div className="message-time-inline">
                                {format(new Date(msg.created_time), "h:mm a")}
                              </div>
                            </div>
                            {/* Only show sender name if it's the latest message from this sender */}
                            {isLatestMessageFromSender(msg, index) && (
                              <div className="message-sender">
                                {msg.sender_name}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}

                  {/* Render sending messages separately */}
                  {sendingMessages.length > 0 && (
                    <div>
                      {/* Today header for sending messages if needed */}
                      {messages.length === 0 && (
                        <div className="date-header text-center my-3">
                          <span className="date-label px-3 py-1 rounded-pill bg-light">
                            Today
                          </span>
                        </div>
                      )}

                      {/* Sending messages */}
                      {sendingMessages.map((message) => (
                        <div
                          key={message.id}
                          className={`message-container ${
                            message.flag === "client"
                              ? "message-right"
                              : "message-left"
                          }`}
                        >
                          <div
                            className={`message-bubble ${
                              message.flag === "client"
                                ? "client-message"
                                : "support-message"
                            }`}
                          >
                            <div className="message-content">
                              {message.type && message.type !== "text" ? (
                                <div className="position-relative">
                                  <span>Sending {message.type}...</span>
                                  <div
                                    className="spinner-border spinner-border-sm ms-2"
                                    role="status"
                                  >
                                    <span className="visually-hidden">
                                      Sending...
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <div className="position-relative">
                                  <span
                                    style={{
                                      whiteSpace: "pre-wrap",
                                      filter: "blur(1px)",
                                    }}
                                  >
                                    {message.message}
                                  </span>
                                  <div className="position-absolute top-50 start-50 translate-middle">
                                    <div
                                      className="spinner-border spinner-border-sm text-light"
                                      role="status"
                                    >
                                      <span className="visually-hidden">
                                        Sending...
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="message-time-inline">
                              Sending...
                            </div>
                          </div>
                          <div className="message-sender">
                            {message.sender_name}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
              <div ref={messagesEndRef} />
            </div>

            <form onSubmit={handleSendMessage} className="message-input-form">
              {attachment && (
                <div className="attachment-preview">
                  {attachment.type &&
                  attachment.type.startsWith("image/") &&
                  attachment.preview ? (
                    <img
                      src={attachment.preview}
                      alt={attachment.name}
                      className="attachment-thumbnail me-2 rounded"
                    />
                  ) : (
                    <>
                      <FaFilePdf className="text-danger me-2" size={24} />
                      <span className="attachment-filename text-truncate me-2">
                        {attachment.name}
                      </span>
                    </>
                  )}
                  <Button
                    variant="light"
                    size="sm"
                    className="remove-attachment-btn-top rounded-circle d-flex align-items-center justify-content-center"
                    onClick={removeAttachment}
                    aria-label="Remove attachment"
                    style={{ width: "24px", height: "24px" }}
                  >
                    <FaTimes size={14} />
                  </Button>
                </div>
              )}
              <div className="input-group">
                <Form.Control
                  type="text"
                  className="message-input"
                  placeholder="Type a message..."
                  value={messageText}
                  onChange={(e) => dispatch(setMessageText(e.target.value))}
                  disabled={loading}
                  ref={messageInputRef}
                />
                <label className="input-group-text attachment-button">
                  <input
                    type="file"
                    className="d-none"
                    onChange={handleFileChange}
                    disabled={loading}
                  />
                  <FaPaperclip />
                </label>
                <Button
                  className="btn btn-primary send-button"
                  type="submit"
                  disabled={loading || (!messageText.trim() && !attachment)}
                >
                  <FaPaperPlane />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* File Preview Modal */}
      <FilePreviewModal
        show={showFilePreviewModal}
        handleClose={() => setShowFilePreviewModal(false)}
        file={previewFile}
      />
    </>
  );
};

export default SupportChatModal;
