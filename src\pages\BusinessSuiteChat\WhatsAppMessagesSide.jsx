import React, {useEffect} from 'react';
import {useMetaBusinessChatContext} from "../../context/MetaBusinessChatContext";
import {Col, Row} from "react-bootstrap";
import {FaUserCircle} from "react-icons/fa";
import {ReactSVG} from "react-svg";
import whatsapp from "../../assets/media/Icons/icons8-whatsapp.svg";
import { TbExclamationMark } from "react-icons/tb";
import {
  fetchWhatsAppChats,
  selectWhatsappChatAndFetchMessages, // Use the renamed action
  selectWhatsappChats,
  selectSelectedWhatsAccount,
  selectSelectedPhone,
} from "../../redux/features/metaBusinessChatSlice";
import { format, isToday, isYesterday } from "date-fns";

const WhatsAppMessagesSide = () => {
    // const [value, setValue] = useState("");
    const {whatsappChats, selectWhatsappChat, selectChat, formatDate, fetchWhatsAppChats, selectedWhatsAccount, latestMessages, selectedPage, selectedChat} = useMetaBusinessChatContext();
    useEffect(() => {
        fetchWhatsAppChats();
    }, []);
    return (
        <div className={"d-flex justify-content-between flex-column"}>
            <div className={"chat-container px-3"}>
                {whatsappChats?.length > 0 ?
                    whatsappChats?.map(chat => (
                            <Row key={chat?.id} className={`my-3 py-2 single-chat-container ${selectedWhatsAccount?.id === chat?.id ? "selectedWhats-chat" : null}`} onClick={() => selectWhatsappChat(chat)}>
                                <Col lg={3} className={'position-relative'}>
                                    {/* You may need to replace these static values with actual data */}
                                    {/*<img src={adidasPage} alt={'User'} className={'integration-account'}/>*/}
                                    {/*<img width={60} height={60}*/}
                                    {/*     src={"https://platform-lookaside.fbsbx.com/platform/profilepic/?eai=AXEFctAM1rZJdnhI-0CnCJQ_L58nJPLIQHEx-7XshrToZPsXewpuhMX27zoA9LEHTBR1hC9nDhIW&psid=****************&width=1024&ext=**********&hash=AbbPUnOKRmm1n4NZaB2NZWIU"}*/}
                                    {/*     alt={'User'} className={'integration-account rounded-circle'}/>*/}
                                    {chat?.image === "dummy" || !chat?.image ?
                                        <FaUserCircle className={"rounded-circle bg-white"} color={"gray"} size={60}/> :
                                        <img width={60} height={60}
                                             src={chat?.image}
                                             alt={'User'}
                                             className={'integration-account rounded-circle'}/>}


                                    <div className={'chat-user-logo'}>
                                        <ReactSVG src={whatsapp}/>
                                    </div>
                                </Col>
                                <Col lg={6}>
                                    <div className={"d-flex flex-column"}>
                                        <div>
                                            {chat?.sender} {chat?.isImportant &&
                                            <TbExclamationMark color={"red"}/>}
                                        </div>
                                        {chat?.message}
                                    </div>
                                    {/*<div className={"one-line " + (chat.isNew ? "text-dark fw-bold" : "text-secondary")}>*/}
                                    {/*    You: {chat.latest_message}*/}
                                    {/*</div>*/}
                                </Col>
                                <Col lg={3} className={"text-secondary text-center"}>
                                    {formatDate(chat?.created_time)}
                                </Col>
                            </Row>
                    ))
                    : null}
            </div>

        </div>
    );
};

export default WhatsAppMessagesSide;