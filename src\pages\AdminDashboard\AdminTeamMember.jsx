import React, { useState } from 'react';
import { PiTrashFill } from 'react-icons/pi';
import { MdEdit, MdOutlineMailOutline } from 'react-icons/md';
import Form from 'react-bootstrap/Form';
import {<PERSON><PERSON>, Col, Row} from 'react-bootstrap';
import { CiLock } from 'react-icons/ci';
import { Formik } from 'formik';
import * as Yup from 'yup';
import {toast} from "react-toastify";
import {FaPhone} from "react-icons/fa6";
import updateTeamMemberApi from "../../services/teams/update-team-member.api";
import ProfilePictureComponent from "../../components/ProfilePicture/ProfilePicture.component";

const AdminTeamMember = ({ teamMember, setTeamMember }) => {
    const [editMode, setEditMode] = useState(false);
    const validationSchema = Yup.object().shape({
        phone: Yup.string().required('Phone is required'),
        email: Yup.string().email('Invalid email address').required('Email is required'),
    });

    const handleEditClick = () => {
        setEditMode(true);
    };

    const handleDeleteClick = async () => {
        // Implement delete functionality using your API or other methods
        // For example:
        // await deleteSingleLeadApi(teamMember?.id);
    };

    const handleSubmit = async (values, { setSubmitting }) => {
        try {
            setTeamMember((prevTeamMember) => ({
                ...prevTeamMember,
                phone: values.phone,
                email: values.email,
                status: values.status, // Assuming you have a status field in your form
            }));

            if (editMode) {
                const updateData = {
                    phone: values.phone,
                    email: values.email,
                    status: values.status === "active" ? "active" : "deactive",
                };
                if (values.password !== '') {
                    updateData.password = values.password;
                }
                await updateTeamMemberApi(updateData, teamMember?.id, "admin");
            }

        } finally {
            setSubmitting(false);
            setEditMode(false);
            toast.success("Team Member Updated Successfully", {position: "bottom-right", theme: "dark"})
        }
    };
    return (
        <Formik
            enableReinitialize
            initialValues={{
                phone: teamMember?.phone || '',
                email: teamMember?.email || '',
                password: teamMember?.password || '',
                status: teamMember?.status || "deactive",
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
        >
            {({ values, handleChange, handleBlur, handleSubmit, isSubmitting, errors, touched }) => (
                <Form noValidate onSubmit={handleSubmit}>
                    <div className={'TM-info-container w-75 mx-auto admin-theme'}>
                        <div className={'d-flex justify-content-between align-items-start'}>
                            {editMode ? null : (
                                <div
                                    className={'shadow-sm rounded-2 p-1'}
                                    role={'button'}
                                    onClick={handleDeleteClick}
                                >
                                    <PiTrashFill size={20} className={'text-danger'}/>
                                </div>
                            )}
                            <ProfilePictureComponent/>
                            {editMode ? null : (
                                <div
                                    className={'shadow-sm rounded-2 p-1'}
                                    role={'button'}
                                    onClick={handleEditClick}
                                >
                                    <MdEdit size={20} className={'mainColor'}/>
                                </div>
                            )}
                        </div>
                        <center className={'fw-bold fs-5 text-white'}>{teamMember?.name}</center>
                        {editMode ? (
                            <div className={'d-flex justify-content-center align-items-center text-white'}>
                                <Form.Check
                                    type="switch"
                                    id={`custom-switch-${teamMember?.id}`}
                                    checked={values.status === 'active'}
                                    className={'members-status-switch mx-4'}
                                    role={'button'}
                                    onChange={() => handleChange({ target: { name: 'status', value: values.status === 'active' ? 'deactive' : 'active' }})}
                                />
                            </div>): null}
                        <Row className={'align-content-center justify-content-center mt-5 mb-2 text-white'}>
                            <Col lg={3}>
                                <FaPhone className={'mainColor'} size={25}/>
                                {" "}
                                {editMode ? (
                                    <Form.Control
                                        type="tel"
                                        name="phone"
                                        value={values.phone}
                                        onChange={handleChange}
                                        isValid={touched.phone && !errors.phone}
                                        isInvalid={touched.phone && errors.phone}
                                    />
                                ) : (
                                    `Phone: ${teamMember.phone}`
                                )}
                            </Col>
                            <Col lg={3}>
                                <MdOutlineMailOutline className={'mainColor'} size={25}/>
                                {" "}
                                {editMode ? (
                                    <Form.Control
                                        type="email"
                                        name="email"
                                        value={values.email}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        isValid={touched.email && !errors.email}
                                        isInvalid={touched.email && errors.email}
                                    />
                                ) : (
                                    teamMember?.email
                                )}
                            </Col>
                            <Col lg={3}>
                                <CiLock className={'mainColor'} size={25}/>
                                {" "}
                                {editMode ? (
                                    <Form.Control
                                        type="password"
                                        name="password"
                                        placeholder={"**********"}
                                        value={values.password}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        isValid={touched.password && !errors.password}
                                        isInvalid={touched.password && errors.password}
                                    />
                                ) : (
                                    '***********'
                                )}
                            </Col>
                        </Row>
                        {
                            editMode ?
                                <center>
                                    <Button type="submit" className="submit-btn" disabled={isSubmitting}>
                                        Save
                                    </Button>
                                    <Button
                                        type="button"
                                        variant={"outline-danger"}
                                        className="rounded-pill ms-3"
                                        onClick={()=>setEditMode(false)}
                                    >
                                        Cancel
                                    </Button>
                                </center>
                                : null
                        }

                    </div>
                </Form>
            )}
        </Formik>
    );
};

export default AdminTeamMember;