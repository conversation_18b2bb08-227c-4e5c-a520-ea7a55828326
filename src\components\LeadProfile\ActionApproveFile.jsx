import { useState } from 'react';
import { useDropzone } from "react-dropzone";
import { Ratio } from "react-bootstrap";
import { FaTimes, FaFileAlt } from 'react-icons/fa';

const ActionApproveFile = ({ setActionProven }) => {
    const [fileError, setFileError] = useState('');
    const [isFilesAccepted, setIsFilesAccepted] = useState(false);
    const [filePreview, setFilePreview] = useState(null);
    const [fileType, setFileType] = useState('');
    const [file, setFile] = useState(null);

    const handleRemoveFile = (e) => {
        e.stopPropagation();
        setFile(null);
        setFilePreview(null);
        setIsFilesAccepted(false);
        setActionProven(null);
    };

    const {
        getRootProps,
        getInputProps
    } = useDropzone({
        accept: {
            'image/jpeg': ['.jpeg', '.jpg'],
            'image/png': ['.png'],
            'image/gif': ['.gif'],
            'image/bmp': ['.bmp'],
            'application/pdf': ['.pdf'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
        },
        onDropAccepted: acceptedFiles => {
            const uploadedFile = acceptedFiles[0];
            const fileExtension = uploadedFile.name.split('.').pop().toLowerCase();

            setFileError('');
            setActionProven(uploadedFile);
            setFile(uploadedFile);
            setIsFilesAccepted(true);
            setFileType(fileExtension);

            if (['jpeg', 'jpg', 'png', 'gif', 'bmp'].includes(fileExtension)) {
                setFilePreview(URL.createObjectURL(uploadedFile));
            } else if (fileExtension === 'pdf') {
                setFilePreview(URL.createObjectURL(uploadedFile));
            } else {
                setFilePreview(null);
            }
        },
        onDropRejected: fileRejections => {
            const fileRejection = fileRejections[0];
            if (fileRejection.errors[0].code === 'file-invalid-type') {
                setFileError('Please upload a valid file');
                setActionProven(null);
                setIsFilesAccepted(false);
                setFilePreview(null);
            }
        }
    });

    const dropzoneClassName = `dropzone-container ${fileError ? 'error' : ''} ${isFilesAccepted ? 'active' : ''}`;

    return (
        <div className="upload-container">
            <div {...getRootProps({ className: dropzoneClassName })}>
                <input {...getInputProps()} />
                {!file ? (
                    <div className="upload-placeholder">
                        <p>Drag 'n' drop a proof file here, or click to select</p>
                        <em>(Accepts .jpeg, .jpg, .png, .gif, .bmp, .pdf, .doc, .docx files)</em>
                    </div>
                ) : (
                    <div className="file-preview-container">
                        <div className="file-header">
                            <span className="file-name">{file.name}</span>
                            <button 
                                className="remove-file-btn"
                                onClick={handleRemoveFile}
                                type="button"
                            >
                                <FaTimes />
                            </button>
                        </div>
                        <div className="preview-content">
                            {filePreview && ['jpeg', 'jpg', 'png', 'gif', 'bmp'].includes(fileType) ? (
                                <img
                                    src={filePreview}
                                    alt="Preview"
                                    className="file-preview-image"
                                />
                            ) : filePreview && fileType === 'pdf' ? (
                                <div className="pdf-preview">
                                    <Ratio aspectRatio="1x1">
                                        <embed
                                            src={filePreview}
                                            type="application/pdf"
                                        />
                                    </Ratio>
                                </div>
                            ) : (
                                <div className="document-preview">
                                    <FaFileAlt size={40} />
                                    <p>Preview not available</p>
                                    <a 
                                        href={URL.createObjectURL(file)} 
                                        download={file.name}
                                        onClick={e => e.stopPropagation()}
                                    >
                                        Download file
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
            {fileError && <p className="error-message">{fileError}</p>}
        </div>
    );
};

export default ActionApproveFile;
