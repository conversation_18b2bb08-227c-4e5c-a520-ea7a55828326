import i18next from 'i18next';

export const getRolesData = () => {
    const { t } = i18next;
    
    return [
        {
            key: 'introduction',
            title: t('privacy.introduction'),
            content: [
                { 
                    subtitle: t('privacy.overview'), 
                    description: t('privacy.overviewDetails') 
                },
                { 
                    subtitle: t('privacy.lastUpdated')
                }
            ],
        },
        {
            key: 'information',
            title: t('privacy.informationWeCollect'),
            content: [
                { 
                    subtitle: t('privacy.personalData'), 
                    description: t('privacy.personalDataDetails') 
                },
                { 
                    subtitle: t('privacy.automaticData'), 
                    description: t('privacy.automaticDataDetails') 
                }
            ],
        },
        {
            key: 'use',
            title: t('privacy.howWeUseInfo'),
            content: [
                { 
                    subtitle: t('privacy.weUseCollectedInfo'), 
                    description: t('privacy.useDetails') 
                }
            ],
        },
        {
            key: 'share',
            title: t('privacy.howWeShareInfo'),
            content: [
                { 
                    subtitle: '', 
                    description: t('privacy.shareDetails') 
                }
            ],
        },
        {
            key: 'security',
            title: t('privacy.dataSecurity'),
            content: [
                { 
                    subtitle: '', 
                    description: t('privacy.securityDetails') 
                }
            ],
        },
        {
            key: 'rights',
            title: t('privacy.yourRights'),
            content: [
                { 
                    subtitle: '', 
                    description: t('privacy.rightsDetails') 
                },
                { 
                    subtitle: '', 
                    description: t('privacy.exerciseRights') 
                }
            ],
        },
        {
            key: 'changes',
            title: t('privacy.changes'),
            content: [
                { 
                    subtitle: '', 
                    description: t('privacy.changesDetails') 
                }
            ],
        },
        {
            key: 'contact',
            title: t('privacy.contactUs'),
            content: [
                { 
                    subtitle: '', 
                    description: t('privacy.contactDetails') 
                }
            ],
        }
    ];
};