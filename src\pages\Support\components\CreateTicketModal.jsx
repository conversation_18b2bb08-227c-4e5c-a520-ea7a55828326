import { useState, useCallback, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Row, Col, Spinner } from "react-bootstrap";
import { useDropzone } from "react-dropzone";
import { FaPaperclip, FaTimes } from "react-icons/fa";
import {
  showErrorToast,
  showSuccessToast,
} from "../../../utils/toast-success-error";
import apiRequest from "../../../utils/apiRequest";

const CreateTicketModal = ({
  show,
  onClose,
  onCreateTicket,
  isAdmin = false,
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [priority, setPriority] = useState("low");
  const [clientId, setClientId] = useState("");
  const [clientList, setClientList] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Fetch client list for admin mode
  useEffect(() => {
    if (isAdmin && show) {
      const fetchClients = async () => {
        try {
          // Replace with your actual API call to get client list
          const response = await apiRequest("admin/clients", "get");
          if (response && response.data) {
            setClientList(response.data);
          }
        } catch (error) {
          console.error("Error fetching client list:", error);
        }
      };

      fetchClients();
    }
  }, [isAdmin, show]);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles) => {
    setAttachments((prev) => [...prev, ...acceptedFiles]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
      "application/pdf": [],
      "text/plain": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [],
    },
  });

  // Remove attachment
  const removeAttachment = (index) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!description.trim()) {
      newErrors.description = "Description is required";
    }

    if (isAdmin && !clientId) {
      newErrors.clientId = "Client is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const payload = {
        title: title.trim(),
        description: description.trim(),
        priority,
      };

      if (isAdmin && clientId) {
        payload.client_id = clientId;
      }

      if (attachments.length > 0) {
        payload.attachments = attachments;
      }

      await onCreateTicket(payload);

      // Reset form
      setTitle("");
      setDescription("");
      setPriority("low");
      setClientId("");
      setAttachments([]);
      setErrors({});
    } catch (error) {
      console.error("Error creating ticket:", error);
      showErrorToast("Failed to create ticket");
    } finally {
      setIsLoading(false);
    }
  };

  // Reset form on close
  const handleClose = () => {
    setTitle("");
    setDescription("");
    setPriority("low");
    setClientId("");
    setAttachments([]);
    setErrors({});
    onClose();
  };

  return (
    <Modal show={show} onHide={handleClose} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>Create New Support Ticket</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter ticket title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              isInvalid={!!errors.title}
            />
            <Form.Control.Feedback type="invalid">
              {errors.title}
            </Form.Control.Feedback>
          </Form.Group>

          {isAdmin && (
            <Form.Group className="mb-3">
              <Form.Label>Client</Form.Label>
              <Form.Select
                value={clientId}
                onChange={(e) => setClientId(e.target.value)}
                isInvalid={!!errors.clientId}
              >
                <option value="">Select Client</option>
                {clientList.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.name} (#{client.id})
                  </option>
                ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                {errors.clientId}
              </Form.Control.Feedback>
            </Form.Group>
          )}

          <Form.Group className="mb-3">
            <Form.Label>Priority</Form.Label>
            <Form.Select
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={5}
              placeholder="Describe your issue in detail"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              isInvalid={!!errors.description}
            />
            <Form.Control.Feedback type="invalid">
              {errors.description}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Attachments</Form.Label>
            <div
              {...getRootProps({
                className: "dropzone p-3 border rounded mb-3 text-center",
              })}
            >
              <input {...getInputProps()} />
              <p className="mb-0">
                <FaPaperclip className="me-2" />
                Drag & drop files here, or click to select files
              </p>
            </div>

            {attachments.length > 0 && (
              <div className="selected-files">
                <p className="mb-2">Selected Files:</p>
                <Row>
                  {attachments.map((file, index) => (
                    <Col key={index} md={6} className="mb-2">
                      <div className="d-flex align-items-center p-2 border rounded">
                        <div className="me-auto text-truncate">{file.name}</div>
                        <Button
                          variant="link"
                          className="text-danger p-0 ms-2"
                          onClick={() => removeAttachment(index)}
                        >
                          <FaTimes />
                        </Button>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? "Creating..." : "Create Ticket"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateTicketModal;
