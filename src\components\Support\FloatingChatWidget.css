.floating-chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #0D6EFD;
  color: white;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-chat-widget.rtl {
  right: auto;
  left: 20px;
}

.floating-chat-widget:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.floating-chat-widget.active {
  transform: scale(0.9);
  opacity: 0.8;
}

.floating-chat-widget.has-new-messages {
  animation: pulse 2s infinite;
}

.widget-icon {
  position: relative;
}

.unread-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
  }
}

