import { HiMiniXMark } from "react-icons/hi2";
import Form from "react-bootstrap/Form";
import * as Yup from "yup";
import { Formik } from "formik";
import { But<PERSON>, Col, Row } from "react-bootstrap";
import { handleAddTeamMemberThunk } from "../../redux/features/clientSlice";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { useTranslation } from "react-i18next";

const InviteMemberModal = ({ handleClose }) => {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const MemberValidationSchema = Yup.object().shape({
    name: Yup.string().required(t("inviteMember.validation.nameRequired")),
    phone: Yup.string().test({
      name: "at-least-one-contact",
      exclusive: true,
      message: t("inviteMember.validation.contactRequired"),
      test: function (value) {
        const email = this.parent.email;
        return (value && value.length > 0) || (email && email.length > 0);
      },
    }),
    email: Yup.string()
      .email(t("inviteMember.validation.emailInvalid"))
      .test({
        name: "at-least-one-contact",
        exclusive: true,
        message: t("inviteMember.validation.contactRequired"),
        test: function (value) {
          const phone = this.parent.phone;
          return (value && value.length > 0) || (phone && phone.length > 0);
        },
      }),
    password: Yup.string()
      .required(t("inviteMember.validation.passwordRequired"))
      .min(6, t("inviteMember.validation.passwordMin")),
    confirmPassword: Yup.string()
      .required(t("inviteMember.validation.confirmPasswordRequired"))
      .oneOf(
        [Yup.ref("password"), null],
        t("inviteMember.validation.passwordsMustMatch")
      ),
    role: Yup.number().required(t("inviteMember.validation.roleRequired")),
  });
  const dispatch = useDispatch();
  const handleAddTeamMember = (values, handleClose, resetForm) => {
    dispatch(handleAddTeamMemberThunk({ values, handleClose, resetForm }));
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
  const { roles } = useSelector((state) => state.role);
  // const filteredRoles = roles?.filter(role => role.show_name !== "admin");
  return (
    <Formik
      initialValues={{
        name: "",
        email: "",
        phone: "",
        password: "",
        role: "", // Empty string means no default selection
        confirmPassword: "",
      }}
      validationSchema={MemberValidationSchema}
      validateOnChange={false} // Add this to prevent validation on every change
      validateOnBlur={true} // Only validate when field loses focus
      onSubmit={(values, { resetForm }) => {
        handleAddTeamMember(values, handleClose, resetForm);
      }}
    >
      {({
        handleSubmit,
        handleChange,
        handleBlur,
        values,
        touched,
        errors,
      }) => (
        <Form className={"p-5"} noValidate onSubmit={handleSubmit}>
          <div className={"position-relative w-100 text-center mb-5"}>
            <h5 className={"fw-bold"}>{t("inviteMember.title")}</h5>
            <div
              className={"member-modal-close-icon"}
              onClick={() => handleClose()}
            >
              <HiMiniXMark size={25} color={"#E35757"} />
            </div>
          </div>
          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>{t("inviteMember.labels.name")}</Form.Label>
                <Form.Control
                  name={"name"}
                  type="text"
                  placeholder={t("inviteMember.placeholders.name")}
                  value={values.name}
                  onChange={handleChange}
                  isInvalid={touched.name && errors.name}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.name}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>{t("inviteMember.labels.email")}</Form.Label>
                <Form.Control
                  name={"email"}
                  type="email"
                  placeholder={t("inviteMember.placeholders.email")}
                  value={values.email}
                  onChange={handleChange}
                  isInvalid={touched.email && errors.email}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.email}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label className={"text-black"}>
                  {t("inviteMember.labels.phone")}
                </Form.Label>
                <Form.Control
                  name={"phone"}
                  type="tel"
                  placeholder={t("inviteMember.placeholders.phone")}
                  value={values.phone}
                  onChange={handleChange}
                  isInvalid={touched.phone && errors.phone}
                />
                {touched.phone && errors.phone && (
                  <small className="text-danger">{errors.phone}</small>
                )}
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field">
                <Form.Label>{t("inviteMember.labels.role")}</Form.Label>
                <Form.Select
                  name={"role"}
                  aria-label={t("inviteMember.labels.role")}
                  onChange={handleChange}
                  isInvalid={touched.role && errors.role}
                  value={values.role}
                  className={"px-3"}
                >
                  <option value="">{t("teamMembers.selectRole")}</option>
                  {roles?.map((role) => (
                    <option key={role?.id} value={Number(role?.id)}>
                      {role?.show_name}
                    </option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.role}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field position-relative password-input-container">
                <Form.Label>{t("inviteMember.labels.password")}</Form.Label>
                <Form.Control
                  name={"password"}
                  type={showPassword ? "text" : "password"}
                  placeholder={t("inviteMember.placeholders.password")}
                  value={values.password}
                  onChange={handleChange}
                  isInvalid={touched.password && errors.password}
                />
                <div
                  className={"password-input-icon-TM"}
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <AiOutlineEye size={25} />
                  ) : (
                    <AiOutlineEyeInvisible size={25} />
                  )}
                </div>
                <Form.Control.Feedback type="invalid">
                  {errors.password}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col lg={6}>
              <Form.Group className="mb-4 new-member-field position-relative password-input-container">
                <Form.Label>
                  {t("inviteMember.labels.confirmPassword")}
                </Form.Label>
                <Form.Control
                  name={"confirmPassword"}
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder={t("inviteMember.placeholders.confirmPassword")}
                  value={values.confirmPassword}
                  onChange={handleChange}
                  isInvalid={touched.confirmPassword && errors.confirmPassword}
                />
                <div
                  className={"password-input-icon-TM"}
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <AiOutlineEye size={25} />
                  ) : (
                    <AiOutlineEyeInvisible size={25} />
                  )}
                </div>
                <Form.Control.Feedback type="invalid">
                  {errors.confirmPassword}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>
          {/*<div className={"fs-6 fw-bold"}>BASIC PERMISSIONS</div>*/}
          {/*<div className={"member-modal-permission"}>*/}
          {/*    <MdOutlineModeEditOutline size={54} className={"edit-content-icon"}/>*/}
          {/*    <div className={"ms-3"}>*/}
          {/*        <h5 className={"fw-bold"}>*/}
          {/*            Add & Edit Content*/}
          {/*        </h5>*/}
          {/*        <p>*/}
          {/*            How to design your site footer like we did*/}
          {/*        </p>*/}
          {/*    </div>*/}
          {/*</div>*/}
          {/*<div className={"member-modal-permission"}>*/}
          {/*    <FaUsersGear size={54} className={"green-label"}/>*/}
          {/*    <div className={"ms-3"}>*/}
          {/*        <h5 className={"fw-bold"}>*/}
          {/*            Add & Edit Groups*/}
          {/*        </h5>*/}
          {/*        <p>*/}
          {/*            Lessons and insights from 8 years of Pixelgrade*/}
          {/*        </p>*/}
          {/*    </div>*/}
          {/*</div>*/}
          <Button
            className={"submit-btn d-flex justify-content-center mx-auto"}
            style={{ width: "fit-content" }}
            type="submit"
          >
            {t("inviteMember.buttons.add")}
          </Button>
        </Form>
      )}
    </Formik>
  );
};

export default InviteMemberModal;
