import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import authenticationTikTokAPI from "../../services/integrations/tiktok";
import useIntegration from "../../hooks/useIntegration";

const TikTokRedirect = () => {
  const location = useLocation();
  const { setTiktokIntegrated } = useIntegration();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(location.search);
  const code = searchParams.get("code");
  useEffect(() => {
    const getAccessToken = async () => {
      try {
        const response = await authenticationTikTokAPI(code);
        console.log(response);
        // const response =
        // const response = await getAccessTokenTikTok();
        // await updateAccessToken(response?.result);
        setTiktokIntegrated(true);
        navigate("./tiktok/connected");
      } catch (error) {
        console.error("Error fetching or updating access token:", error);
      }
    };

    if (code) {
      getAccessToken();
    }
  }, [location.search]);

  return <div>Integrated successfully</div>;
};

export default TikTokRedirect;
