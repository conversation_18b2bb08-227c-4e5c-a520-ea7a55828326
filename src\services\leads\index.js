import apiRequest from "../../utils/apiRequest";
import { cleanData } from "../../utils/clean-data";
import { api } from "../config";
import downloadFile from "../../utils/download-file";

const autoAssignLeadsApi = async () => {
    await apiRequest("auto_assign", "post");
};

// Reusable function to fetch lead data
const fetchLeadData = async (endpoint) => {
    return await apiRequest(endpoint, "get");
};

const getAllLeadsChartApi = async () => {
    return fetchLeadData("leadsFilterDate");
};

const getAllLeadsStatusApi = async () => {
    return fetchLeadData("leadsFilterStatus");
};

const getAllLeadsSourcesApi = async () => {
    return fetchLeadData("leadsFilterSource");
};

const getAllCompletedLeadsApi = async () => {
    return fetchLeadData("leadsFilterCompleted");
};

// Fetch leads that have no communication history
// Supports optional source & status filters while re-using the same endpoint
const getNoCommunicationLeadsApi = async (
    controllerOrClientId,
    records = 10,
    currentPage = 1,
    source = null,
    status = null,
    keySearch = null
) => {
    // The first parameter can be an AbortController (for cancellation) or clientId
    const isController = controllerOrClientId instanceof AbortController;

    const clientId = isController ? null : controllerOrClientId;
    const signal = isController ? controllerOrClientId.signal : null;

    // Build query params
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    if (source !== null && source !== undefined && source !== "") {
        params.append("source", source.toString());
    }
    if (keySearch !== null && keySearch !== undefined && keySearch !== "") {
        params.append("search", keySearch.toString());
    }
    if (status !== null && status !== undefined && status !== "") {
        params.append("status", status.toString());
    }

    const basePath = clientId ? `admin/leads/nocommunication?client_id=${clientId}` : `leads/nocommunication`;

    return await apiRequest(`${basePath}?${params.toString()}`, "get", null, {}, signal);
};

const createLeadApi = async (leadData, adminFlag) => {
    const url = adminFlag ? 'admin/leads' : 'lead';
    return await apiRequest(url, 'post', leadData);
};

const createLeadAdminApi = async (leadData) => {
    return await apiRequest("admin/leads", "post", leadData);
};

const deleteSingleLeadApi = async (id) => {
    return await apiRequest(`lead/${id}`, "delete");
};

const exportLeadsApi = async (clientId) => {
    const endpoint = clientId ? "admin/leads/export" : "leads/export";
    try {
        const response = await (clientId
            ? api.post(endpoint, { client_id: clientId }, {
                responseType: 'blob',
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })
            : api.get(endpoint, {
                responseType: 'blob',
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })
        );

        // Trigger the download
        downloadFile(response.data, 'leads.xlsx');
    } catch (error) {
        console.error('Error exporting data:', error);
        throw error;
    }
};

const filterLeadsApi = async (filterData, controller = null, records = 10, currentPage = 1) => {
    const data = {
        ...filterData,
        current_page: currentPage,
        per_page: records,
    };

    try {
        // Pass empty object for headers instead of null
        return await apiRequest("leadsFilter", "post", data, {}, controller?.signal);
    } catch (error) {
        // Properly handle abort errors
        if (error.name === 'AbortError' || error.message === 'AbortError') {
            const abortError = new Error('AbortError');
            abortError.name = 'AbortError';
            throw abortError;
        }
        const errorMessage = error?.response?.data?.message || error.message || 'An error occurred while filtering leads.';
        console.error('Error sending data:', errorMessage);
        throw new Error(errorMessage);
    }
};

const filterLeadsByStatusApi = async ({
    status = null,
    source = null,
    clientId,
    records = 10,
    currentPage = 1,
    controller = null,
}) => {
    const data = {
        client_id: clientId,
        current_page: currentPage,
        per_page: records,
        ...(source && { source: source.toString() }),
        ...(status && { status }),
    };

    try {
        return await apiRequest("leadsstatusfilter", "post", data, { signal: controller?.signal });
    } catch (error) {
        const isAbortError = error.name === 'AbortError';
        const errorMessage = error?.response?.data?.message || error.message || 'An error occurred while filtering leads by status.';
        console.error(isAbortError ? 'Request was aborted' : `Error sending data: ${errorMessage}`);
        if (!isAbortError) throw new Error(errorMessage);
    }
};

const getAllLeadsApi = async (clientIdOrController, records = 10, currentPage = 1) => {
    // Check if the first parameter is an AbortController
    const isController = clientIdOrController instanceof AbortController;

    // Extract the appropriate values
    const clientId = isController ? null : clientIdOrController;
    const signal = isController ? clientIdOrController.signal : null;

    const url = clientId
        ? `admin/leads?client_id=${clientId}&per_page=${records}&current_page=${currentPage}`
        : `/lead?per_page=${records}&current_page=${currentPage}`;

    return await apiRequest(url, 'get', null, {}, signal);
};
const getLeadsCountDashboardApi = async (signal) => {
    const url = '/widget';
    return await apiRequest(url, 'get', null, {}, signal);
};

const getSingleLeadApi = async (id, flag) => {
    const url = flag === 'admin' ? `admin/leads/${id}` : `lead/${id}`;
    return await apiRequest(url, 'get');
};

const importLeads = async (data) => {
    try {
        return await apiRequest("leads/import", 'post', data, {
            'Content-Type': 'multipart/form-data',
        });
    } catch (error) {
        console.error('Error sending data:', error);
        throw error;
    }
};

const unassignedLeadsApi = async (leadId) => {
    return await apiRequest(`assign_lead/${leadId}`, 'post', { assigned_id: "" });
};

const updateQuotationApi = async (quotationData, id) => {
    const parsedData = cleanData(quotationData);
    return await apiRequest(`lead/quotation/${id}`, 'post', parsedData);
};

const updateSingleLeadApi = async (leadId, leadData) => {
    // Clean the leadData by removing null, undefined, or empty values
    const cleanedData = cleanData(leadData);
    let formData;

    // Check if there is a file in the leadData
    if (cleanedData.quotation_offer || cleanedData.action_proven) {
        formData = new FormData();

        // Append each field from cleanedData to the FormData object
        for (const key in cleanedData) {
            if (Object.prototype.hasOwnProperty.call(cleanedData, key)) {
                formData.append(key, cleanedData[key]);
            }
        }
    }

    // Use apiRequest and pass 'post' method with appropriate data
    return await apiRequest(`lead/${leadId}?_method=put`, 'post', formData || cleanedData, {
        'Content-Type': formData ? 'multipart/form-data' : 'application/json',
    });
};

const resetLeadApi = async (id) => {
    return await apiRequest(`reset-lead/${id}`, 'get');
};

export default {
    createLeadApi,
    exportLeadsApi,
    filterLeadsApi,
    filterLeadsByStatusApi,
    getAllLeadsApi,
    getNoCommunicationLeadsApi,
    getLeadsCountDashboardApi,
    getSingleLeadApi,
    importLeads,
    unassignedLeadsApi,
    updateQuotationApi,
    updateSingleLeadApi,
    createLeadAdminApi,
    deleteSingleLeadApi,
    getAllLeadsChartApi,
    getAllLeadsSourcesApi,
    getAllLeadsStatusApi,
    getAllCompletedLeadsApi,
    autoAssignLeadsApi,
    resetLeadApi
}
