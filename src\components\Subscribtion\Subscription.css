@media (max-width: 576px) {
    .mobile-packages-container {
        display: flex !important;
        flex-wrap: nowrap !important;
        overflow-x: auto !important;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        padding: 1rem !important;
        gap: 1rem;
        /* Center alignment modifications */
        justify-content: flex-start !important;
        padding-left: calc((100% - 85%) / 2) !important;
        padding-right: calc((100% - 85%) / 2) !important;
    }

    .mobile-packages-container::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
    }

    .mobile-packages-container::before,
    .mobile-packages-container::after {
        content: '';
        flex: 0 0 calc((100% - 85%) / 2);
    }

    .mobile-pricing-card {
        flex: 0 0 85% !important;
        scroll-snap-align: center;
        margin-right: 1rem;
        transform: scale(0.98);
        transition: transform 0.3s ease;
        /* Center the content inside the card */
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .mobile-pricing-card:last-child {
        margin-right: calc((100% - 85%) / 2);
    }

    .mobile-pricing-card:first-child {
        margin-left: calc((100% - 85%) / 2);
    }

    /* Add visual feedback for active card */
    .mobile-pricing-card:active {
        transform: scale(0.96);
    }
}

/* Add pagination dots for mobile */
.package-pagination {
    display: none;
    justify-content: center;
    margin-top: 1rem;
}

.pagination-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ccc;
    margin: 0 4px;
    transition: background-color 0.3s ease;
}

.pagination-dot.active {
    background-color: #007bff;
}

@media (max-width: 576px) {
    .package-pagination {
        display: flex;
    }
}
