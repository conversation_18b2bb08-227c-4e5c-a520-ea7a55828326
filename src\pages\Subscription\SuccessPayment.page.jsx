import Lottie from 'lottie-react';
import successAnimation from "../../assets/media/animations/signin.json";
import {Link, useNavigate} from 'react-router-dom';
import './subscription.css';
import {Button} from "react-bootstrap";
import {useEffect} from "react";

const PaymentSuccess = () => {
    const navigate = useNavigate();

    const handleRedirect = () => {
        setTimeout(() => {
            navigate('/');
        }, 5000);
    };

    useEffect(() => {
        handleRedirect();
    }, []);

    return (
        <div className="payment-success">
            <Lottie animationData={successAnimation} loop={false} className={"w-50 h-50"} />
            <h1>Payment Successful!</h1>
            <p>Thank you for your purchase. You’ll be redirected shortly.</p>
            <Link to={"/"} replace={true}>
                <Button className={"mainBgColor rounded-pill fs-5 fw-bold border-0 align-self-center mb-3"} id="reset-button">Go Home</Button>
            </Link>
        </div>
    );
};

export default PaymentSuccess;
