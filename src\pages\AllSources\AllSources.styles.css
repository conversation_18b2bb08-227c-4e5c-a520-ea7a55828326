/* Refresh button styling */
.refresh-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bs-primary, #0d6efd);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  z-index: 10;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.refresh-button:hover {
  transform: scale(1.1);
  background-color: var(--bs-primary-dark, #0b5ed7);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.refresh-button:active {
  transform: scale(0.95);
}

.refresh-button svg {
  transition: transform 0.3s ease;
}

.refresh-button:hover svg {
  transform: rotate(180deg);
}

/* Tooltip styling */
.refresh-tooltip {
  animation: fadeIn 0.2s ease-in-out !important;
  font-weight: 500;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
