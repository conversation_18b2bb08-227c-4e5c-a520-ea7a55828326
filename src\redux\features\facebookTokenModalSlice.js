import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  showTokenModal: false,
  hasShownModal: false
};

const facebookTokenModalSlice = createSlice({
  name: 'facebookTokenModal',
  initialState,
  reducers: {
    setShowTokenModal: (state, action) => { state.showTokenModal = action.payload; },
    setHasShownModal: (state, action) => { state.hasShownModal = action.payload; },
    handleModalClose: (state) => {
      state.showTokenModal = false;
      state.hasShownModal = true;
    },
    showModal: (state) => {
      if (!state.hasShownModal) {
        state.showTokenModal = true;
      }
    }
  }
});

export const {
  setShowTokenModal,
  setHasShownModal,
  handleModalClose,
  showModal
} = facebookTokenModalSlice.actions;

export default facebookTokenModalSlice.reducer;
