import { toast } from "react-toastify";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import InviteNewAdminTM from "../../components/AdminDashboard/InviteNewAdminTM";
import AdminTeamTable from "../../components/AdminDashboard/AdminTeamTable";
import React, { useEffect, useMemo, useState } from "react";
import Form from "react-bootstrap/Form";
import updateTeamMemberApi from "../../services/teams/update-team-member.api";
import { MdEdit } from "react-icons/md";
import { PiTrashFill } from "react-icons/pi";
import { useSelector, useDispatch } from "react-redux";
import {
  setTeamMembers,
  deleteAdminTeamMember,
} from "../../redux/features/clientSlice";
import adminService from "../../services/admin";
import DeleteTeamMemberModal from "../../components/Modals/DeleteTeamMemberModal";
import EditAdminMemberModal from "../../components/Modals/EditAdminMemberModal";

const Team = () => {
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const dispatch = useDispatch();
  const { teamMembers } = useSelector((state) => state.client);

  const [loading, setLoading] = useState(false);

  const { data, indexedData } = useMemo(() => {
    const filteredData =
      teamMembers?.filter((item) => item.role !== "Owner") || [];
    const indexed = filteredData.map((item, index) => ({
      ...item,
      clientId: index + 1,
    }));

    return { data: filteredData, indexedData: indexed };
  }, [teamMembers]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [modalData, setModalData] = useState({ id: null, values: null });
  const handleShowEditModal = (id, values) => {
    setModalData({ id, values });
    setShowEditModal(true);
  };

  useEffect(() => {
    setLoading(true);
    const fetchData = async () => {
      const teamsData = await adminService.getAllAdminMembersApi();
      dispatch(setTeamMembers(teamsData.data));
      setLoading(false);
    };
    fetchData();
    setLoading(false);
  }, [dispatch]);

  const columns = useMemo(
    () => [
      {
        Header: "ID",
        accessor: "clientId",
        Cell: ({ row }) => <span>{row.index + 1}</span>,
      },
      {
        Header: "Name",
        accessor: "name",
        Cell: ({ row }) => (
          <>
            <div className={"text-decoration-none"}>{row.original.name}</div>
          </>
        ),
      },
      {
        Header: "Status",
        accessor: "status",
        Cell: ({ row }) => (
          <Form.Check
            type="switch"
            id={`custom-switch-${row.original.id}`}
            checked={row.original.status === "active"}
            className={"members-status-switch"}
            onChange={async () => {
              const updatedData = teamMembers?.map(async (item) => {
                if (item.id === row.original.id) {
                  const updatedItem = {
                    ...item,
                    status: item.status === "active" ? "deactive" : "active",
                  };
                  try {
                    await updateTeamMemberApi(
                      { status: updatedItem.status },
                      row.original.id,
                      "admin"
                    );
                    toast.success("Team member updated successfully", {
                      position: "bottom-right",
                      autoClose: 2000,
                      theme: "dark",
                    });
                    const updatedTeamMembers = teamMembers.map((teamMember) => {
                      if (teamMember.id === row.original.id) {
                        return {
                          ...teamMember,
                          status: updatedItem.status,
                        };
                      }
                      return teamMember;
                    });
                    dispatch(setTeamMembers(updatedTeamMembers));
                  } catch (err) {
                    toast.error("Error updating team member", {
                      position: "bottom-right",
                      autoClose: 2000,
                      theme: "dark",
                    });
                    console.log("API error:", err);
                  }
                  return updatedItem;
                }
                return item;
              });
              await Promise.all(updatedData);
            }}
          />
        ),
      },
      {
        Header: "Role",
        accessor: "role",
        Cell: ({ row }) => {
          const Role = row.original.role;
          let roleLabel;
          switch (Role) {
            case "owner-Admin":
              roleLabel = "Admin";
              break;
            case "owner-moderator":
              roleLabel = "Moderator";
              break;
            case "owner-accountant":
              roleLabel = "Accountant";
              break;
            case "owner-sales":
              roleLabel = "Sales";
              break;
            default:
              roleLabel = "Team Member";
          }
          return (
            <div className={"d-flex justify-content-center align-items-center"}>
              <div className={"shadow-sm rounded-2 p-1"}>{roleLabel}</div>
            </div>
          );
        },
      },
      {
        Header: "Actions",
        Cell: ({ row }) => (
          <div className={"d-flex justify-content-center align-items-center"}>
            <div
              className="me-3 shadow-sm rounded-2 p-1"
              style={{ width: "fit-content" }}
              onClick={() => handleShowEditModal(row.original.id, row.original)}
            >
              <MdEdit size={20} color="#92C020" />
            </div>
            <div className={"me-3 shadow-sm rounded-2 p-1"}>
              <PiTrashFill
                onClick={() => {
                  setIdToDelete(row.original.id);
                  setShowDeleteModal(true);
                }}
                size={20}
                className={"text-danger"}
              />
            </div>
          </div>
        ),
      },
    ],
    [teamMembers, dispatch]
  );

  return (
    <>
      <AdminTeamTable
        columns={columns}
        loading={loading}
        data={indexedData}
        setShowCenteredModal={setShowCenteredModal}
      />
      <CenteredModal
        size={"lg"}
        show={showCenteredModal}
        className={"dark-modal"}
        children={
          <InviteNewAdminTM
            teamMembers={data}
            handleClose={() => setShowCenteredModal(false)}
          />
        }
        onHide={() => setShowCenteredModal(false)}
      />
      <CenteredModal
        show={showDeleteModal}
        children={
          <DeleteTeamMemberModal
            title={"Are you sure you want to delete this Team Member?"}
            deleteMemberFunction={(params) =>
              dispatch(deleteAdminTeamMember(params))
            }
            id={idToDelete}
            handleClose={() => setShowDeleteModal(false)}
          />
        }
        onHide={() => setShowDeleteModal(false)}
      />
      {showEditModal && (
        <EditAdminMemberModal
          showEditModal={showEditModal}
          setShowEditModal={setShowEditModal}
          modalData={modalData}
          onHide={() => setShowEditModal(false)}
        />
      )}
    </>
  );
};

export default Team;
