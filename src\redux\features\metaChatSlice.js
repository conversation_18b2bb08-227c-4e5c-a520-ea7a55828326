import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { collection, onSnapshot, orderBy, query, limit, where, doc, setDoc } from 'firebase/firestore';
import metaService from '../../services/integrations/meta';
import { db } from '../../utils/firebase.config';
import { BigIntUtil } from '../../utils/BigIntUtil';

// Function to check for 24-hour window errors
const check24HourWindowError = (error) => {
  if (!error) return false;
  if (error.code === 10 || error.error_subcode === 2018278) return true;
  if (error.message && (
    error.message.includes("(#10)") ||
    error.message.toLowerCase().includes("outside the allowed window") ||
    error.message.toLowerCase().includes("24-hour")
  )) return true;
  return false;
};

// Completely revamp the fetchLatestMessages thunk to ensure it works for all chats
export const fetchLatestMessages = createAsyncThunk(
  'metaChat/fetchLatestMessages',
  async (_, { dispatch, getState }) => {
    try {
      // Use a different approach - instead of a single query with a limit,
      // we'll use a more comprehensive strategy

      // First, get all chats from the current state
      const state = getState();
      const messengerChats = state.metaChat.messengerChats || [];
      const instagramChats = state.metaChat.instagramChats || [];
      const allChats = [...messengerChats, ...instagramChats];

      // If we don't have any chats yet, use a general query
      if (allChats.length === 0) {
        const generalQuery = query(
          collection(db, "chats"),
          orderBy("created_time", "desc"),
          limit(1000) // Use a very high limit to ensure we get all messages
        );

        const unsubscribe = onSnapshot(generalQuery, (snapshot) => {
          if (!snapshot.empty) {
            const allMessages = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            dispatch(metaChatSlice.actions.setLatestMessages(allMessages));
          }
        });

        return unsubscribe;
      }

      // If we have chats, create a more targeted approach
      // Extract all participant IDs from the chats
      const participantIds = new Set();

      allChats.forEach(chat => {
        if (chat.participants && chat.participants.data) {
          chat.participants.data.forEach(participant => {
            if (participant.id) {
              participantIds.add(participant.id.toString());
            }
          });
        }
      });

      console.log(`Found ${participantIds.size} unique participant IDs`);

      // Convert to array for query
      const participantIdsArray = Array.from(participantIds);

      // Due to Firestore limitations, we might need to split this into multiple queries
      // if we have a lot of participants
      const batchSize = 10; // Firestore allows up to 10 items in an 'in' query
      const batches = [];

      for (let i = 0; i < participantIdsArray.length; i += batchSize) {
        const batch = participantIdsArray.slice(i, i + batchSize);
        batches.push(batch);
      }

      console.log(`Split into ${batches.length} batches for querying`);

      // Create a query for each batch
      const unsubscribes = [];

      batches.forEach((batch, index) => {
        // Query for messages where sender is in this batch
        const senderQuery = query(
          collection(db, "chats"),
          where("sender", "in", batch),
          orderBy("created_time", "desc")
        );

        const senderUnsubscribe = onSnapshot(senderQuery, (snapshot) => {
          if (!snapshot.empty) {
            const messages = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            // Merge with existing messages
            const currentState = getState();
            const currentMessages = currentState.metaChat.latestMessages || [];

            // Create a map for faster lookups
            const messagesMap = new Map();

            // Add existing messages to map
            currentMessages.forEach(msg => {
              const key = `${msg.sender || ''}-${msg.recipient || ''}`;
              if (!messagesMap.has(key) ||
                new Date(msg.created_time) < new Date(messagesMap.get(key).created_time)) {
                messagesMap.set(key, msg);
              }
            });

            // Add new messages to map
            messages.forEach(msg => {
              const key = `${msg.sender || ''}-${msg.recipient || ''}`;
              if (!messagesMap.has(key) ||
                new Date(msg.created_time) > new Date(messagesMap.get(key).created_time)) {
                messagesMap.set(key, msg);
              }
            });

            // Convert map back to array
            const mergedMessages = Array.from(messagesMap.values());

            // Update state
            dispatch(metaChatSlice.actions.setLatestMessages(mergedMessages));
          }
        });

        unsubscribes.push(senderUnsubscribe);

        // Query for messages where recipient is in this batch
        const recipientQuery = query(
          collection(db, "chats"),
          where("recipient", "in", batch),
          orderBy("created_time", "desc")
        );

        const recipientUnsubscribe = onSnapshot(recipientQuery, (snapshot) => {
          if (!snapshot.empty) {
            const messages = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));

            // Merge with existing messages
            const currentState = getState();
            const currentMessages = currentState.metaChat.latestMessages || [];

            // Create a map for faster lookups
            const messagesMap = new Map();

            // Add existing messages to map
            currentMessages.forEach(msg => {
              const key = `${msg.sender || ''}-${msg.recipient || ''}`;
              if (!messagesMap.has(key) ||
                new Date(msg.created_time) < new Date(messagesMap.get(key).created_time)) {
                messagesMap.set(key, msg);
              }
            });

            // Add new messages to map
            messages.forEach(msg => {
              const key = `${msg.sender || ''}-${msg.recipient || ''}`;
              if (!messagesMap.has(key) ||
                new Date(msg.created_time) > new Date(messagesMap.get(key).created_time)) {
                messagesMap.set(key, msg);
              }
            });

            // Convert map back to array
            const mergedMessages = Array.from(messagesMap.values());

            // Update state
            dispatch(metaChatSlice.actions.setLatestMessages(mergedMessages));
          }
        });

        unsubscribes.push(recipientUnsubscribe);
      });

      // Return a function that unsubscribes from all listeners
      return () => {
        unsubscribes.forEach(unsubscribe => unsubscribe());
      };
    } catch (error) {
      console.error("Error fetching latest messages:", error);
      throw error;
    }
  }
);

// Optimize the extractLatestMessage function
export const extractLatestMessage = (latestMessages, chatId) => {
  if (!latestMessages || !Array.isArray(latestMessages) || !chatId) return null;

  // Convert chatId to string for consistent comparison
  const chatIdStr = chatId.toString();

  // Find messages where this chat is either sender or recipient
  const relevantMessages = latestMessages.filter(
    msg => (msg.sender && msg.sender.toString() === chatIdStr) ||
      (msg.recipient && msg.recipient.toString() === chatIdStr)
  );

  if (relevantMessages.length === 0) return null;

  // Find the latest message
  const latestMessage = relevantMessages.reduce((latest, current) => {
    const latestTime = new Date(latest.created_time || latest.updated_time || 0);
    const currentTime = new Date(current.created_time || current.updated_time || 0);
    return currentTime > latestTime ? current : latest;
  }, relevantMessages[0]);

  // Format and return the message
  return {
    ...latestMessage,
    message: latestMessage?.message,
    message_id: latestMessage?.message_id,
    sender_name: latestMessage?.sender_name,
    created_time: latestMessage?.created_time || latestMessage?.updated_time,
  };
};

// Async thunk for fetching messages for a specific chat (Meta API and Firestore)
export const fetchMessagesForChat = createAsyncThunk(
  'metaChat/fetchMessagesForChat',
  async ({ thread, selectedPage }, { dispatch, getState }) => {
    // Clear existing messages first
    dispatch(metaChatSlice.actions.setMessages([]));
    dispatch(metaChatSlice.actions.setNestedMessages([]));

    if (!thread) {
      return;
    }

    try {
      const result = await metaService.getMessagesForChatApi({
        ...selectedPage,
        access_token: selectedPage?.page_token,
        user_id: thread.id,
      });

      // Sort messages by creation time
      result.sort(
        (a, b) => new Date(a.created_time) - new Date(b.created_time),
      );

      // Set nested messages
      dispatch(metaChatSlice.actions.setNestedMessages(result));

      // Also set these messages to the main messages array to ensure they appear
      dispatch(metaChatSlice.actions.setMessages(result));

      const latestMessages = getState().metaChat.latestMessages;
      const senderId =
        thread?.flage === "instagram"
          ? thread?.participants?.data[1]?.id
          : thread?.participants?.data[0]?.id;
      const messageFromFirestore = latestMessages
        ? extractLatestMessage(latestMessages, senderId)
        : null;

      if (messageFromFirestore && messageFromFirestore.id) {
        const nestedCollectionRef = collection(
          db,
          "chats",
          messageFromFirestore.id,
          "messages",
        );
        const nestedQuery = query(
          nestedCollectionRef,
          orderBy("created_time", "asc"), // Change to ascending order
        );

        const unsubscribe = onSnapshot(nestedQuery, (snapshot) => {
          const nestedMessages = snapshot.docs.map((doc) => {
            const data = doc.data();
            return {
              id: doc.id,
              created_time: data?.created_time,
              from: {
                id: new BigIntUtil(data?.sender)?.value,
                email: new BigIntUtil(data?.sender)?.value,
              },
              message: data?.message,
              type: data?.type
            };
          });

          // Use addMessages to merge with existing messages
          dispatch(metaChatSlice.actions.addMessages(nestedMessages));
        });

        // Store unsubscribe function to be called later
        dispatch(metaChatSlice.actions.setMessagesSnapshotUnsubscribe(unsubscribe));
      }

      return result;
    } catch (error) {
      console.error("Error fetching messages for chat:", error);
      return null;
    }
  }
);

export const fetchMessages = createAsyncThunk(
  'metaChat/fetchMessages',
  async ({ chatId, pageToken, signal }, { dispatch, getState }) => {
    try {
      // Clear existing messages first
      dispatch(setMessages([]));
      dispatch(setNestedMessages([]));

      if (!chatId) {
        return null;
      }

      const state = getState();
      const selectedPage = state.metaChat.selectedPage;
      const pageTokenToUse = pageToken || selectedPage?.page_token;

      if (!pageTokenToUse) {
        console.error("No page token available");
        return null;
      }

      // Get messages from API
      const response = await metaService.getMessagesForChatApi({
        page_id: selectedPage?.page_id,
        access_token: pageTokenToUse,
        user_id: chatId,
        signal
      });

      if (response) {
        // Sort messages by creation time
        const sortedMessages = response.sort(
          (a, b) => new Date(a.created_time) - new Date(b.created_time)
        );

        // Set messages from API to both nestedMessages and messages arrays
        dispatch(setNestedMessages(sortedMessages));
        dispatch(setMessages(sortedMessages)); // Add this line to ensure messages appear

        // Set up Firestore listener for real-time updates
        const state = getState();
        const latestMessages = state.metaChat.latestMessages;

        if (latestMessages && latestMessages.length > 0) {
          // Find the latest message for this chat
          const senderId = chatId;
          const messageFromFirestore = latestMessages.find(
            msg => msg.sender?.toString() === senderId?.toString() ||
              msg.recipient?.toString() === senderId?.toString()
          );

          if (messageFromFirestore && messageFromFirestore.id) {
            const nestedCollectionRef = collection(
              db,
              "chats",
              messageFromFirestore.id,
              "messages"
            );
            const nestedQuery = query(
              nestedCollectionRef,
              orderBy("created_time", "asc")
            );

            const unsubscribe = onSnapshot(nestedQuery, (snapshot) => {
              const nestedMessages = snapshot.docs.map((doc) => {
                const data = doc.data();
                return {
                  id: doc.id,
                  created_time: data?.created_time,
                  from: {
                    id: data?.sender,
                    email: data?.sender,
                  },
                  message: data?.message,
                  type: data?.type
                };
              });

              // Use addMessages instead of setMessages to merge with existing messages
              dispatch(addMessages(nestedMessages));
            });

            // Store the unsubscribe function
            dispatch(setMessagesSnapshotUnsubscribe(unsubscribe));
          }
        }

        return sortedMessages;
      }

      return null;
    } catch (error) {
      console.error("Error fetching messages:", error);
      return null;
    }
  }
);


// Async thunk for sending messages (Meta API)
export const sendMessage = createAsyncThunk(
  'metaChat/sendMessage',
  async ({ data, selectedPage, selectedChat, addSendingMessage, removeSendingMessage }, { dispatch, getState }) => {
    const tempMessageId = `temp_${Date.now()}`;

    try {
      // Log the FormData entries to debug attachment issues
      console.log("FormData entries in sendMessage:");
      for (let pair of data.entries()) {
        console.log(pair[0], pair[1]);
      }

      // Create and add temporary message for UI feedback
      const tempMessage = {
        id: tempMessageId,
        type: data.get("type") || "text",
        message: data.get("message") || data.get("text"),
        created_time: new Date().toISOString(),
        from: { id: selectedPage?.page_id }, // Format for UI compatibility
        sender: selectedPage?.page_id,
        recipient: selectedChat?.id,
      };

      // Only add a temporary message if addSendingMessage is provided and we're not already showing one
      if (addSendingMessage && !removeSendingMessage(tempMessageId, true)) {
        addSendingMessage(tempMessage);
      }

      // Send message via API
      const response = await metaService.sendMessageApi(data);

      // Check for upload attachment failure error that still provides a file_url
      const fileUrl = response?.file_url || null;
      const isUploadFailureWithUrl =
        response?.error?.code === 100 &&
        response?.error?.error_subcode === 2018047 &&
        fileUrl;

      // If we have an upload failure but still got a URL, log it but continue
      if (isUploadFailureWithUrl) {
        console.warn("Meta API reported upload failure but returned a URL. Proceeding with message.", {
          error: response.error,
          fileUrl
        });
      }

      // Handle other errors
      if (response?.error && !isUploadFailureWithUrl) {
        console.error("Error in Meta API response:", response.error);

        // Remove temporary message
        if (removeSendingMessage) {
          removeSendingMessage(tempMessageId);
        }

        return { error: response?.error?.message || "Error sending message" };
      }

      // Determine message type and content
      const messageType = data.get("type") || "text";
      const messageText = data.get("message") || data.get("text") || "";
      const isMediaMessage = messageType === "image" ||
        messageType === "video" ||
        messageType === "audio" ||
        messageType === "file";

      // Create a new message object based on the response
      const newMessage = {
        id: response?.message_id || `meta_${Date.now()}`,
        type: messageType,
        message: isMediaMessage ? (response?.file_url || fileUrl) : messageText,
        url: isMediaMessage ? (response?.file_url || fileUrl) : null,
        created_time: new Date().toISOString(),
        from: { id: selectedPage?.page_id },
        // Add any other properties needed for the message
      };

      // Remove temporary message
      if (removeSendingMessage) {
        removeSendingMessage(tempMessageId);
      }

      return { success: true, message: newMessage };
    } catch (error) {
      console.error("Error in sendMessage:", error);

      // Check for 24-hour window error in the caught error
      if (error?.response?.data?.error) {
        const apiError = error.response.data.error;
        if (check24HourWindowError(apiError)) {
          dispatch(setDisabledChat(true));
          if (removeSendingMessage) {
            removeSendingMessage(tempMessageId);
          }
          return { error: "Cannot send message outside the 24-hour window" };
        }
      }

      if (removeSendingMessage) {
        removeSendingMessage(tempMessageId);
      }

      return { error: "Error sending message" };
    }
  }
);

// Async thunk for fetching more chats (pagination)
export const fetchMoreChats = createAsyncThunk(
  'metaChat/fetchMoreChats',
  async ({ selectedPage, paginationMeta }, { dispatch, getState }) => {
    dispatch(metaChatSlice.actions.setLoadingPagination(true));
    try {
      const response = await metaService.getMoreChatsAPI({
        page_id: selectedPage?.page_id,
        access_token: selectedPage?.page_token,
        after: paginationMeta[0],
      });

      if (response?.data) {
        const newChats = response.data;
        dispatch(metaChatSlice.actions.addChats(newChats));
        dispatch(metaChatSlice.actions.setPaginationMeta([response.paging.cursors.after, response.paging.cursors.before]));
        dispatch(metaChatSlice.actions.setHasMore(!!response.paging.next));
      } else {
        dispatch(metaChatSlice.actions.setHasMore(false));
      }
    } catch (error) {
      console.error("Error fetching more chats:", error);
      dispatch(metaChatSlice.actions.setHasMore(false));
    } finally {
      dispatch(metaChatSlice.actions.setLoadingPagination(false));
    }
  }
);

// Add a new action to clear messages when selecting a new chat
export const selectChatAndFetchMessages = createAsyncThunk(
  'metaChat/selectChatAndFetchMessages',
  async (chat, { dispatch, getState }) => {
    // Reset disabled chat state when switching to a new chat
    dispatch(setDisabledChat(false));

    // First, clear all existing messages and unsubscribe from any listeners
    dispatch(clearMessages());

    // Set the selected chat
    dispatch(setSelectedChat(chat));

    // If there's a chat selected, fetch its messages
    if (chat) {
      const state = getState();
      const selectedPage = state.metaChat.selectedPage;

      // Fetch messages for the selected chat
      await dispatch(fetchMessages({
        chatId: chat.id,
        pageToken: selectedPage?.page_token
      }));
    }

    return chat;
  }
);

const metaChatSlice = createSlice({
  name: 'metaChat',
  initialState: {
    chats: [],
    selectedChat: null,
    messages: [],
    selectedPage: null,
    latestMessages: [],
    nestedMessages: [],
    messagesSnapshotUnsubscribe: null,
    activeFilter: "messenger",
    messengerChats: [],
    instagramChats: [],
    disabledChat: false,
    disabledChatLimit: false,
    pageImg: null,
    paginationMeta: [null, null],
    hasMore: true,
    loadingPagination: false,
    loadingChats: false,
    conversations: [], // Assuming this is Meta related
    businessPhoneNumbers: [], // Assuming this is Meta related
    startDate: "", // Assuming this is Meta related
    endDate: "", // Assuming this is Meta related
    filteredData: [], // Assuming this is Meta related
    // No whatsapp related states here
  },
  reducers: {
    setChats: (state, action) => {
      state.chats = action.payload;
    },
    addChats: (state, action) => {
      state.chats = [...state.chats, ...action.payload];
    },
    setSelectedChat: (state, action) => {
      state.selectedChat = action.payload;
    },
    setMessages: (state, action) => {
      state.messages = action.payload;
    },
    setSelectedPage: (state, action) => {
      state.selectedPage = action.payload;
    },
    setLatestMessages: (state, action) => {
      state.latestMessages = action.payload;
    },
    setNestedMessages: (state, action) => {
      state.nestedMessages = action.payload;
    },
    setMessagesSnapshotUnsubscribe: (state, action) => {
      // If there's an existing unsubscribe function, call it first
      if (state.messagesSnapshotUnsubscribe) {
        try {
          state.messagesSnapshotUnsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from messages snapshot:", error);
        }
      }

      // Set the new unsubscribe function
      state.messagesSnapshotUnsubscribe = action.payload;
    },
    setActiveFilter: (state, action) => {
      state.activeFilter = action.payload;
    },
    setMessengerChats: (state, action) => {
      state.messengerChats = action.payload;
    },
    setInstagramChats: (state, action) => {
      state.instagramChats = action.payload;
    },
    setDisabledChat: (state, action) => {
      state.disabledChat = action.payload;
    },
    setDisabledChatLimit: (state, action) => {
      state.disabledChatLimit = action.payload;
    },
    setPageImg: (state, action) => {
      state.pageImg = action.payload;
    },
    setPaginationMeta: (state, action) => {
      state.paginationMeta = action.payload;
    },
    setHasMore: (state, action) => {
      state.hasMore = action.payload;
    },
    setLoadingPagination: (state, action) => {
      state.loadingPagination = action.payload;
    },
    setLoadingChats: (state, action) => {
      state.loadingChats = action.payload;
    },
    setConversations: (state, action) => {
      state.conversations = action.payload;
    },
    setBusinessPhoneNumbers: (state, action) => {
      state.businessPhoneNumbers = action.payload;
    },
    setStartDate: (state, action) => {
      state.startDate = action.payload;
    },
    setEndDate: (state, action) => {
      state.endDate = action.payload;
    },
    setFilteredData: (state, action) => {
      state.filteredData = action.payload;
    },
    addMessages: (state, action) => {
      const newMessages = action.payload;

      // If there are no new messages, return early
      if (!newMessages || newMessages.length === 0) return;

      // Create a map of existing messages for faster lookup
      const existingMessagesMap = new Map();
      state.messages.forEach(msg => {
        existingMessagesMap.set(msg.id, msg);
      });

      // Add new messages if they don't exist
      newMessages.forEach(newMsg => {
        if (!existingMessagesMap.has(newMsg.id)) {
          existingMessagesMap.set(newMsg.id, newMsg);
        }
      });

      // Convert back to array and sort
      const uniqueMessages = Array.from(existingMessagesMap.values());
      uniqueMessages.sort(
        (a, b) => new Date(a.created_time) - new Date(b.created_time)
      );

      state.messages = uniqueMessages;
    },
    clearMessages: (state) => {
      state.messages = [];
      state.nestedMessages = [];

      // If there's an active listener, we need to unsubscribe
      if (state.messagesSnapshotUnsubscribe) {
        try {
          state.messagesSnapshotUnsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from messages snapshot:", error);
        }
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    resetFilteredChatsPages: (state) => {
      state.chats = [];
      state.paginationMeta = [null, null];
      state.hasMore = true;
    },
    addMessage: (state, action) => {
      // Check if the message already exists to prevent duplicates
      const messageExists = state.messages.some(
        msg => msg.id === action.payload.id ||
          (msg.message_id && msg.message_id === action.payload.message_id)
      );

      if (!messageExists) {
        state.messages.push(action.payload);

        // Sort messages by creation time
        state.messages.sort((a, b) => {
          const dateA = new Date(a.created_time);
          const dateB = new Date(b.created_time);
          return dateA - dateB;
        });
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLatestMessages.pending, (state) => {
        state.loadingChats = true;
      })
      .addCase(fetchLatestMessages.fulfilled, (state) => {
        state.loadingChats = false;
      })
      .addCase(fetchLatestMessages.rejected, (state) => {
        state.loadingChats = false;
        // Handle error if needed
      })
      .addCase(fetchMessagesForChat.pending, (state) => {
        state.loadingChats = true;
      })
      .addCase(fetchMessagesForChat.fulfilled, (state) => {
        state.loadingChats = false;
      })
      .addCase(fetchMessagesForChat.rejected, (state) => {
        state.loadingChats = false;
        // Handle error if needed
      })
      .addCase(sendMessage.pending, (state) => {
        // Handle pending state for sendMessage if needed
      })
      .addCase(sendMessage.fulfilled, (state) => {
        // Handle fulfilled state for sendMessage if needed
      })
      .addCase(sendMessage.rejected, (state, action) => {
        // Handle rejected state for sendMessage if needed
        console.error("Error sending message:", action.error.message);
      })
      .addCase(fetchMoreChats.pending, (state) => {
        state.loadingPagination = true;
      })
      .addCase(fetchMoreChats.fulfilled, (state) => {
        state.loadingPagination = false;
      })
      .addCase(fetchMoreChats.rejected, (state) => {
        state.loadingPagination = false;
        state.hasMore = false;
      });
  },
});

export const {
  setChats,
  addChats,
  setSelectedChat,
  setMessages,
  setSelectedPage,
  setLatestMessages,
  setNestedMessages,
  setMessagesSnapshotUnsubscribe,
  setActiveFilter,
  setMessengerChats,
  setInstagramChats,
  setDisabledChat,
  setDisabledChatLimit,
  setPageImg,
  setPaginationMeta,
  setHasMore,
  setLoadingPagination,
  setLoadingChats,
  setConversations,
  setBusinessPhoneNumbers,
  setStartDate,
  setEndDate,
  setFilteredData,
  addMessages,
  addMessage,
  clearMessages,
  resetFilteredChatsPages,
} = metaChatSlice.actions;

// Selectors
export const selectMessages = (state) => state.metaChat.messages;
export const selectSelectedChat = (state) => state.metaChat.selectedChat;
export const selectActiveFilter = (state) => state.metaChat.activeFilter;
export const selectSelectedPage = (state) => state.metaChat.selectedPage;
export const selectChats = (state) => state.metaChat.chats;
export const selectMessengerChats = (state) => state.metaChat.messengerChats;
export const selectInstagramChats = (state) => state.metaChat.instagramChats;
export const selectPaginationMeta = (state) => state.metaChat.paginationMeta;
export const selectHasMore = (state) => state.metaChat.hasMore;
export const selectLoadingPagination = (state) => state.metaChat.loadingPagination;
export const selectLoadingChats = (state) => state.metaChat.loadingChats;
export const selectDisabledChat = (state) => state.metaChat.disabledChat;
export const selectDisabledChatLimit = (state) => state.metaChat.disabledChatLimit;
export const selectLatestMessages = (state) => state.metaChat.latestMessages;

export default metaChatSlice.reducer;






















