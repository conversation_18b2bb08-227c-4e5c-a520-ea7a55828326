.statistics-card-container {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0 4px 40px -5px rgba(0, 0, 0, 0.15);
}

.statistics-card {
    border-radius: 5px;
    background: rgba(237, 237, 237, 0.19);
}

.statistic-title {
    color: #949EAB;
    font-weight: 500;
}

.statistic-number {
    font-size: 1.1rem;
    font-weight: 700;
}

.statistics-card-container {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0 4px 40px -5px rgba(0, 0, 0, 0.15);
    padding: 20px;
    margin-bottom: 20px;
    @media only screen and (min-width: 768px) {
        max-width: 280px;
    }
    max-width: 90%;
}

.pieChart-container, .pieChart-arcs-container {
    position: relative;
}

.pie-chart-svg {
    position: absolute;
    top: 0;
    left: 0;
}

.pie-chart-svg circle {
    stroke: none;
}

.rbc-event:has(div[style*="rgb(146, 192, 32)"]) {
    background-color: #92C0201A;
    border: 1px solid #92C020;
}

.rbc-event:has(div[style*="rgb(146, 192, 32)"]) .rbc-event-label:not(:last-child) {
    background-color: #92C020;
    padding: 5px;
    border-radius: 6px;
}

.rbc-event:has(div[style*="rgb(160, 6, 6)"]) {
    background-color: rgba(160, 6, 6, 0.10);
    border: 1px solid #A00606;
}

.rbc-event:has(div[style*="rgb(160, 6, 6)"]) .rbc-event-label:not(:last-child) {
    background-color: #A00606;
    padding: 5px;
    border-radius: 6px;
}

.rbc-event:has(div[style*="rgb(186, 189, 6)"]) {
    background-color: rgba(186, 189, 6, 0.10);
    border: 1px solid #BABD06;
}

.rbc-event:has(div[style*="rgb(186, 189, 6)"]) .rbc-event-label:not(:last-child) {
    background-color: #BABD06;
    padding: 5px;
    border-radius: 6px;
}

.rbc-event:has(div[style*="rgb(249, 119, 0)"]) {
    background-color: rgba(186, 189, 6, 0.10);
    border: 1px solid #F97700;
}

.rbc-event:has(div[style*="rgb(249, 119, 0)"]) .rbc-event-label:not(:last-child) {
    background-color: #F97700;
    padding: 5px;
    border-radius: 6px;
}

.rbc-event:has(div[style*="rgb(200, 77, 231)"]) {
    background-color: rgba(186, 189, 6, 0.10);
    border: 1px solid #C84DE7;
}

.rbc-event:has(div[style*="rgb(200, 77, 231)"]) .rbc-event-label:not(:last-child) {
    background-color: #C84DE7;
    padding: 5px;
    border-radius: 6px;
}

.rbc-day-slot .rbc-event {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {
    flex-flow: row wrap !important;
    min-height: 40px !important;
}

.rbc-event {
    height: fit-content !important;
}

.TM-chart-container {
    max-width: 450px;
}

.team-actions-button:after {
    content: unset;
}

.content-container {
    display: flex;
    flex-direction: column;
  }

  @media (max-width: 768px) {
    .content-container div[style*="height: 350px"] {
      height: 300px !important;
    }
  }

  @media (max-width: 576px) {
    .content-container div[style*="height: 350px"] {
      height: 250px !important;
    }
  }
