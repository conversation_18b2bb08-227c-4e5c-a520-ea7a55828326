import { useEffect } from "react";
import { But<PERSON> } from "react-bootstrap";
import { FaArrowLeft } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import AdminSupportChatContent from "../../components/AdminDashboard/AdminSupportChatContent";
import { setupGlobalAdminChatListener } from "../../redux/features/adminSupportChatSlice";
import "./AdminSupportChat.css";

const AdminSupportChatPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";

  // Ensure the global listener is set up
  useEffect(() => {
    const setupListener = async () => {
      await dispatch(setupGlobalAdminChatListener());
    };

    setupListener();

    return () => {
      // Use the global unsubscribe function
      if (typeof window.__adminChatUnsubscribe === "function") {
        window.__adminChatUnsubscribe();
      }
    };
  }, [dispatch]);

  return (
    <div className="admin-support-chat-page mb-5 border-success card bg-dark text-light rounded-4">
      <div className="chat-header">
        <Button
          variant="link"
          className="back-button"
          onClick={() => navigate("/admin/support")}
        >
          <FaArrowLeft className={isRTL ? "flip-rtl" : ""} />
          <span className="ms-2">Back to Support</span>
        </Button>
        <h2>Client Support Chats</h2>
      </div>

      <div className="chat-content-container">
        <AdminSupportChatContent />
      </div>
    </div>
  );
};

export default AdminSupportChatPage;
