import {useEffect, useState} from "react";
import getSingleTeamMemberApi from "../../services/teams/get-single-team-member.api";
import {useNavigate, useParams} from "react-router-dom";
import TeamMemberInfoComponent from "../../components/TeamMemberProfile/TeamMemberInfo.component";
import TeamMemberChartsComponent from "../../components/TeamMemberProfile/TeamMemberCharts.component";
import {IoCaretBack} from "react-icons/io5";
import i18n from "../../utils/i18n";

const TeamMemberProfilePage = () => {
    const [teamMember, setTeamMember] = useState({});
    const params = useParams();
    const Rtl = i18n.dir() === 'rtl';

    useEffect(() => {
        const getData = async () => {
            try {
                const response = await getSingleTeamMemberApi(params.id);
                setTeamMember(response.data);
            } catch (error) {
                console.error("Error fetching team member data:", error);
            }
        };
        getData();
    }, [params.id]);

    const navigate = useNavigate();
    return (
        <>
            <div role={"button"} onClick={() => navigate(-1)} title={"Back To Teams"} style={{left: Rtl ? "auto" : "20px", right: Rtl ? "20px" : "auto"}}>
                <IoCaretBack color={"#000"} className={"bg-white rounded-circle p-1"} style={{rotate: Rtl ? "180deg" : "0"}} size={35}/>
            </div>
            <TeamMemberInfoComponent teamMember={teamMember} setTeamMember={setTeamMember} params={params}/>
            <div className={"content-container"}>
                <TeamMemberChartsComponent teamMember={teamMember}/>
                {/*<TeamMemberCalendarComponent />*/}
            </div>
        </>
    );
};

export default TeamMemberProfilePage;
