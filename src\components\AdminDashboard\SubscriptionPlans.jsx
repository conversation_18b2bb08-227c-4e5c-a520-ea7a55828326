import React, {useMemo, useState} from "react";
import { useSelector } from "react-redux";
import {IoSparklesSharp} from "react-icons/io5";
import {MdEdit} from "react-icons/md";
import {PiTrashFill} from "react-icons/pi";
import SubscriptionPlansDataTable from "./SubscriptionPlansDataTable";

const SubscriptionPlans = () => {
    const [loading, setLoading] = useState(false);
    const [totalPages, setTotalPages] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const columns = useMemo(() => [{
        Header: ' ', Cell: ({row}) => {
            if (row.original.assignedTo === null || row.original.assignedTo === undefined) {
                return <IoSparklesSharp size={20} color={"#92C020"}/>;
            } else {
                return null;
            }
        },
    }, {
        Header: "Plan name", accessor: "plan_name"
    },
        {
            Header: "Monthly Price",
            accessor: "monthly_price",
            Cell: ({row}) => {
                const monthlyPrice = row.original.monthly_price;
                return (
                    <div>
                        {monthlyPrice} $
                    </div>
                );
            }
        },
        {
            Header: "Annually Price",
            accessor: "annually_price",
            Cell: ({row}) => {
                const annuallyPrice = row.original.annually_price;
                return (
                    <div>
                        {annuallyPrice} $
                    </div>
                );
            }
        },
        {
            Header: "Subscriptions Count", accessor: "subscriptions_count",
        },
        {
            Header: "Earnings",
            accessor: "earnings",
            Cell: ({row}) => {
                const Earnings = row.original.earnings;
                return (
                    <div>
                        {Earnings} $
                    </div>
                );
            }
        },
        {
            Header: "Actions", Cell: ({row}) => (<div className="d-flex justify-content-center">
                <div className="me-3 shadow-sm rounded-2 p-1" onClick={()=>{
                    // handleShowEditModal(row.original.id, row.original)
                    }}>
                    <MdEdit size={20} color="#92C020"/>
                </div>
                <div className={"me-3 shadow-sm rounded-2 p-1"}>
                    <PiTrashFill onClick={() => {
                        // setShowDeleteModal(true)
                        // setIdToDelete(row.original.id)
                    }} size={20}
                                 className={"text-danger"}/>
                </div>
            </div>)
        }
    ], []);
    const data = [
        {
            id: 1,
            plan_name: "Free",
            monthly_price: "00",
            annually_price: "00",
            subscriptions_count: "125,689",
            earnings: "00.00"
        },
        {
            id: 2,
            plan_name: "Basic",
            monthly_price: "99",
            annually_price: "99",
            subscriptions_count: "125,689",
            earnings: "300.00"
        },
        {
            id: 3,
            plan_name: "Premium",
            monthly_price: "199",
            annually_price: "199",
            subscriptions_count: "125,689",
            earnings: "300.00"
        },
        {
            id:4,
            plan_name: "Enterprise",
            monthly_price: "399",
            annually_price: "399",
            subscriptions_count: "125,689",
            earnings: "300.00"
        }
    ]
    return (
      <SubscriptionPlansDataTable
          setCurrentPage={setCurrentPage}
          currentPage={currentPage}
          recordsPerPage={recordsPerPage}
          setRecordsPerPage={setRecordsPerPage}
          loading={loading} classNames={"admin-theme px-3 py-2"} data={data} columns={columns} totalPages={totalPages} setTotalPages={setTotalPages}
          />
    );
};

export default SubscriptionPlans;
