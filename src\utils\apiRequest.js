import { api } from "../services/config";
import { showErrorToast, showSuccessToast } from "./toast-success-error";
import i18n from "./i18n";
import { cleanData } from "./clean-data";

const apiRequest = async (url, method = "get", data = null, headers = {}, signal = null, additionalConfig = {}, successMessage = null, errorMessage = null) => {
    // Get current language from i18n
    const currentLanguage = i18n.language;

    const config = {
        url,
        method,
        headers: {
            "Content-Type": "application/json",
            "Accept-Language": currentLanguage, // Add language header
            language: currentLanguage, // Some APIs may use this format
            ...headers
        },
        signal,
        ...additionalConfig, // Allow overriding or adding Axios configurations dynamically
    };

    // Handle query params for GET requests
    if (method === "get" && data) {
        config.params = data;
    } else if (data) {
        // For POST/PUT/PATCH requests, clean the data first unless it's FormData
        if (headers["Content-Type"] === "multipart/form-data") {
            config.data = data;
        } else if (method !== "get") {
            config.data = cleanData(data);
        }
    }

    try {
        const response = await api(config);
        // Show success toast if a message is provided or if the response has a message
        const messageToShow = successMessage;
        if (messageToShow) {
            showSuccessToast(messageToShow);
        }
        return response.data;
    } catch (error) {
        if (signal?.aborted) {
            // Silently handle aborted requests - this is expected behavior
            // when components unmount or new requests supersede old ones
        } else {
            // Use provided errorMessage, fallback to error response message, then default
            const messageToShow = errorMessage || error?.response?.data?.message || error.message || "An unexpected error occurred";
            showErrorToast(messageToShow); // Show toast only for non-canceled errors
            console.error(`Error in API Request [${method.toUpperCase()} ${url}]:`, error);
        }
        throw error;
    }
};

export default apiRequest;
