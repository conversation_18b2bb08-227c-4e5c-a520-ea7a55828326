import React, { useEffect, useMemo, useState } from "react";
import { MdEdit } from "react-icons/md";
import { Link } from "react-router-dom";
import { format, parseISO } from "date-fns";
import { PiTrashFill } from "react-icons/pi";
import { useSelector } from "react-redux";
import clientService from "../../services/clients";
import ClientsDataTable from "../../components/AdminDashboard/ClientsDataTable";
import "../../components/CustomDataTable/datatable.css";
import Form from "react-bootstrap/Form";
import EditClientModal from "../../components/AdminDashboard/EditClientModal";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import DeleteClientModal from "../../components/Modals/DeleteClientModal";
import handleUpgradeSubscriptionApi from "../../services/payment/admin-upgrade-subscription";
import {
  showErrorToast,
  showSuccessToast,
} from "../../utils/toast-success-error";
import useAdmin from "../../redux/hooks/useAdmin";

const Clients = () => {
  const sourceToIcon = useSelector((state) => state.client.sourceToIcon);
  const leadStatusCounts = useSelector(
    (state) => state.client.leadStatusCounts
  );
  const { clients, setClients, handleDeleteClient, handleUpdateClientStatus } =
    useAdmin();
  const [showCenteredModal, setShowCenteredModal] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState(null);
  const [loading, setLoading] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [modalData, setModalData] = useState({ id: null, values: null });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [idToDelete, setIdToDelete] = useState(null);
  useEffect(() => {
    if (!user) {
      return;
    }
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const clientsData = await clientService.getAllClientsApi(
          currentPage,
          recordsPerPage
        );
        setTotalPages(clientsData?.data["Number Of Pages"]);
        setCurrentPage(clientsData?.data["Page Number"]);
        setClients(clientsData?.data?.Clients);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching initial data:", error);
        showErrorToast(error.response?.data?.message || "An error occurred");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [user, currentPage, recordsPerPage]);

  useEffect(() => {
    setData(
      clients?.map((client, index) => ({
        id: client?.id,
        name: client?.name,
        email: client?.email,
        source: client?.source,
        phone: client?.phone,
        leadsCount: client["Number Of Leads"] || 0,
        logs: client?.latestActivity || client?.activities,
        assignedTo: client?.assignedTo || client?.assigned_to?.name,
        createdAt: client?.created_at,
        status: client?.status || "active",
        clientId: index + 1,
        packageId: client?.package_id || 1,
        isPlus: client?.isPlus,
      }))
    );
  }, [clients]);

  const handleShowEditModal = (id, values) => {
    setModalData({ id, values });
    setShowEditModal(true);
  };

  const handleUpgrade = async (email) => {
    const response = await handleUpgradeSubscriptionApi(email);
    if (response?.status === 200) {
      if (response && response?.data) {
        const apiUser = response?.data?.user;
        const updatedClients = Array.isArray(clients)
          ? clients.map((client) =>
              client.id === apiUser?.id
                ? {
                    ...client,
                    isPlus: true,
                    packageId: apiUser?.package_id ?? client.packageId,
                    packageName: apiUser?.package_name ?? client.packageName,
                  }
                : client
            )
          : [];
        setClients(updatedClients);
      }
      showSuccessToast(response.message);
    } else {
      showErrorToast(response.message);
    }
  };

  const columns = useMemo(
    () => [
      {
        Header: "ID",
        accessor: "clientId",
        Cell: ({ row }) => <span>{row.index + 1}</span>,
      },
      {
        Header: "Client Name",
        accessor: "name",
        Cell: ({ row }) => {
          const name = row.original.name;
          return (
            <Link
              to={`/admin/clients/leads/${row.original.id}`}
              className={"text-nowrap overflow-hidden text-center"}
            >
              {name}
            </Link>
          );
        },
      },
      {
        Header: "Email",
        accessor: "email",
        Cell: ({ row }) => {
          const email = row.original.email;
          return (
            <Link
              to={`/admin/clients/leads/${row.original.id}`}
              className={"text-nowrap overflow-hidden text-center"}
            >
              {email}
            </Link>
          );
        },
      },
      {
        Header: "Phone",
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <Link
              to={`/admin/clients/leads/${row.original.id}`}
              className={"text-nowrap overflow-hidden text-center"}
            >
              {phone}
            </Link>
          );
        },
      },
      {
        Header: "Leads Count",
        accessor: "leadsCount",
      },
      {
        Header: "Client Status",
        accessor: "clientStatus",
        Cell: ({ row }) => {
          const status = row.original.status;
          const id = row.original.id;
          return (
            <Form.Group>
              <Form.Check
                type="switch"
                id={`status-${status}`}
                className={"members-status-switch ms-2"}
                checked={status === "active"}
                onChange={() =>
                  handleUpdateClientStatus({
                    clientId: id,
                    status: status === "active" ? "deactive" : "active",
                  })
                }
              />
            </Form.Group>
          );
        },
      },
      {
        Header: "Created At",
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          if (value) {
            const parsedDate = parseISO(value);
            const formattedDate = format(parsedDate, "yyyy-MM-dd");
            return (
              <Link to={`/admin/clients/leads/${row.original.id}`}>
                {formattedDate}
              </Link>
            );
          }
        },
      },
      {
        Header: "Package",
        accessor: "packageId",
        Cell: ({ row }) => {
          const packageId = row.original.packageId;
          return packageId ? (
            <Link to={`/admin/clients/leads/${row.original.id}`}>
              <span className="text-success fw-bold">
                {packageId === 2 ? "Plus" : "Free"}
              </span>
            </Link>
          ) : null;
        },
      },
      {
        Header: "Subscription",
        accessor: "isPlus",
        Cell: ({ row }) => {
          return row.original.isPlus ? (
            <span className="text-success fw-bold">-</span>
          ) : (
            <button
              className="btn btn-sm btn-outline-success"
              onClick={() => handleUpgrade(row.original.email)}
            >
              Upgrade
            </button>
          );
        },
      },
      {
        Header: "Actions",
        Cell: ({ row }) => (
          <div className="d-flex justify-content-center">
            <div
              className="me-3 shadow-sm rounded-2 p-1"
              onClick={() => handleShowEditModal(row.original.id, row.original)}
            >
              <MdEdit size={20} color="#92C020" />
            </div>
            <div className={"me-3 shadow-sm rounded-2 p-1"}>
              <PiTrashFill
                onClick={() => {
                  setShowDeleteModal(true);
                  setIdToDelete(row.original.id);
                }}
                size={20}
                className={"text-danger"}
              />
            </div>
          </div>
        ),
      },
    ],
    [handleUpdateClientStatus]
  );
  return (
    <>
      <ClientsDataTable
        showCenteredModal={showCenteredModal}
        setShowCenteredModal={setShowCenteredModal}
        selectedLeadId={selectedLeadId}
        setSelectedLeadId={setSelectedLeadId}
        setCurrentPage={setCurrentPage}
        currentPage={currentPage}
        recordsPerPage={recordsPerPage}
        setRecordsPerPage={setRecordsPerPage}
        sourceToIcon={sourceToIcon}
        leadStatusCounts={leadStatusCounts}
        loading={loading}
        classNames={"admin-theme"}
        data={data}
        columns={columns}
        totalPages={totalPages}
        setTotalPages={setTotalPages}
      />
      {showEditModal && (
        <EditClientModal
          showEditModal={showEditModal}
          setShowEditModal={setShowEditModal}
          modalData={modalData}
          onHide={() => setShowEditModal(false)}
        />
      )}
      <CenteredModal
        show={showDeleteModal}
        children={
          <DeleteClientModal
            title={"Are you sure you want to delete this Client?"}
            deleteClientFunction={handleDeleteClient}
            id={idToDelete}
            handleClose={() => setShowDeleteModal(false)}
          />
        }
        onHide={() => setShowDeleteModal(false)}
      />
    </>
  );
};

export default Clients;
