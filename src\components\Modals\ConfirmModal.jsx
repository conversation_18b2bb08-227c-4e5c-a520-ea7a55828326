import {Button} from "react-bootstrap";
const ConfirmModal = (props) => {
    return (
        <div className={"d-flex flex-column justify-content-around align-items-center"}>
            <h3 className={"fw-bold text-center my-5"}>
                {props.title}
            </h3>
            <div className={"d-flex justify-content-center"}>
                <Button variant={"secondary"} className={"me-3 rounded-pill"} onClick={()=> props.handleClose()}>
                    Cancel
                </Button>
                <Button className={"apply-btn"} onClick={() => props.confirmFunction()}>
                    Confirm
                </Button>
            </div>
        </div>
    );
};

export default ConfirmModal;