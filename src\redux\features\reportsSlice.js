import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    teamMembersFilters: {
        startDate: null,
        endDate: null,
        selectedTM: null,
        role: null,
    },
    salesFilters: {
        startDate: null,
        endDate: null,
        selectedTM: null,
        status: null,
    },
    leadAssignmentFilters: {
        startDate: null,
        endDate: null,
        selectedTM: null,
        source: null,
    },
};

const reportsSlice = createSlice({
    name: 'reports',
    initialState,
    reducers: {
        setTeamMembersFilters(state, action) {
            state.teamMembersFilters = { ...state.teamMembersFilters, ...action.payload };
        },
        clearTeamMembersFilters(state) {
            state.teamMembersFilters = { ...initialState.teamMembersFilters };
        },
        setSalesFilters(state, action) {
            state.salesFilters = { ...state.salesFilters, ...action.payload };
        },
        clearSalesFilters(state) {
            state.salesFilters = { ...initialState.salesFilters };
        },
        setLeadAssignmentFilters(state, action) {
            state.leadAssignmentFilters = { ...state.leadAssignmentFilters, ...action.payload };
        },
        clearLeadAssignmentFilters(state) {
            state.leadAssignmentFilters = { ...initialState.leadAssignmentFilters };
        },
    },
});

export const {
    setTeamMembersFilters,
    clearTeamMembersFilters,
    setSalesFilters,
    clearSalesFilters,
    setLeadAssignmentFilters,
    clearLeadAssignmentFilters,
} = reportsSlice.actions;

export default reportsSlice.reducer;
