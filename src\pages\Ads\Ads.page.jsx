import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import {
  setCurrentPage,
  setRecordsPerPage,
  setTotalPages,
} from "../../redux/features/leadsPaginationSlice";
import { Card, Form, Row, Col, Button } from "react-bootstrap";
import { FaMagnifyingGlass, FaArrowLeft } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationComponent from "../../components/CustomDataTable/PaginationComponent";
import { Tooltip } from "react-tooltip";
import { ReactSVG } from "react-svg";
import { sourceToIcon } from "../../constants/sourceIcons";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import adsService from "../../services/ads";
import "./Ads.page.css";

export default function AdsPage() {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { adsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [ads, setAds] = useState([]);
  const [leads, setLeads] = useState([]);
  const [currentPage, setCurrentPageLocal] = useState(1);
  const [recordsPerPage, setRecordsPerPageLocal] = useState(10);
  const [totalPages, setTotalPagesLocal] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [showLeadsDetail, setShowLeadsDetail] = useState(false);
  const [selectedAd, setSelectedAd] = useState(null);
  const [leadsCurrentPage, setLeadsCurrentPage] = useState(1);
  const [leadsTotalPages, setLeadsTotalPages] = useState(0);

  useEffect(() => {
    fetchAds();
  }, [currentPage, recordsPerPage, searchTerm]);

  const fetchAds = async () => {
    setLoading(true);
    try {
      const response = await adsService.getAllAdsApi(
        recordsPerPage,
        currentPage,
        searchTerm
      );

      if (
        (response?.success || response?.message === "Success") &&
        response?.data
      ) {
        setAds(response.data || []);
        const totalPagesCount = response.data.last_page || 1;
        setTotalPagesLocal(totalPagesCount);

        // Update Redux state
        dispatch(setCurrentPage(currentPage));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotalPages(totalPagesCount));
      } else {
        setAds([]);
        setTotalPagesLocal(0);
      }
    } catch (error) {
      console.error("Error fetching ads:", error);
      setAds([]);
      setTotalPagesLocal(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchLeadsForAd = async (adId) => {
    setLoading(true);
    try {
      const response = await adsService.getAdLeadsApi(adId);

      if (
        (response?.status === 200 ||
          response?.success ||
          response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - leads are in response.data.data
        setLeads(response.data.data || []);

        // Set pagination info for leads if needed
        if (response.data.last_page) {
          setLeadsTotalPages(response.data.last_page);
        }
      } else {
        setLeads([]);
        setLeadsTotalPages(0);
      }
    } catch (error) {
      console.error("Error fetching leads for ad:", error);
      setLeads([]);
      setLeadsTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    setCurrentPageLocal(1);
    dispatch(setCurrentPage(1));
  };

  const handleViewLeads = (ad) => {
    setSelectedAd(ad);
    setShowLeadsDetail(true);
    fetchLeadsForAd(ad.ad_id);
  };

  const handleBackToAds = () => {
    setShowLeadsDetail(false);
    setSelectedAd(null);
    setLeads([]);
  };

  // Get ads columns from the module
  const adsColumnsConfig = useMemo(
    () => adsColumns(handleViewLeads),
    [adsColumns, handleViewLeads]
  );

  // Leads table columns
  const leadsColumns = useMemo(
    () => [
      {
        Header: t("leadsTable.columns.id"),
        accessor: "id",
        Cell: ({ row }) => (
          <div className="text-center">
            <span>{row.index + 1}</span>
          </div>
        ),
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "name",
        Cell: ({ row }) => {
          const name = row.original.name;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-name-${row.original.id} mx-auto text-decoration-none`}
                style={{ maxWidth: "200px" }}
              >
                {name}
              </Link>
              <Tooltip
                anchorSelect={`.lead-name-${row.original.id}`}
                content={name}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-phone-${row.original.id} mx-auto text-decoration-none`}
                style={{ maxWidth: "150px" }}
              >
                {phone}
              </Link>
              <Tooltip
                anchorSelect={`.lead-phone-${row.original.id}`}
                content={phone}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("tables.headers.email"),
        accessor: "email",
        Cell: ({ row }) => {
          const email = row.original.email;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-email-${row.original.id} mx-auto text-decoration-none`}
                style={{ maxWidth: "200px" }}
              >
                {email}
              </Link>
              <Tooltip
                anchorSelect={`.lead-email-${row.original.id}`}
                content={email}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const source = row.original.source;
          const IconComponent = sourceToIcon[source] || null;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className="mx-auto social-icon-container"
              >
                {IconComponent && <ReactSVG src={IconComponent} />}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.status"),
        accessor: "status",
        Cell: ({ row }) => {
          const status = row.original.status;
          const statusMapping = {
            0: {
              label: t("status.pending"),
              className: "status-badge--pending",
            },
            1: {
              label: t("status.inProgress"),
              className: "status-badge--in-progress",
            },
            2: {
              label: t("status.completed"),
              className: "status-badge--completed",
            },
            3: {
              label: t("status.rejected"),
              className: "status-badge--rejected",
            },
            4: {
              label: t("status.wrongLead"),
              className: "status-badge--wrong-lead",
            },
            5: {
              label: t("status.notQualified"),
              className: "status-badge--not-qualified",
            },
            6: {
              label: t("status.noCommunication"),
              className: "status-badge--no-communication",
            },
            7: { label: t("status.booked"), className: "status-badge--booked" },
            8: {
              label: t("status.bookedReserved"),
              className: "status-badge--booked-reserved",
            },
            9: {
              label: t("status.canceled"),
              className: "status-badge--canceled",
            },
            10: {
              label: t("status.quotation"),
              className: "status-badge--quotation-sent",
            },
            11: {
              label: t("status.assigned"),
              className: "status-badge--in-progress",
            },
          };

          const { label, className } = statusMapping[status] || {
            label: "Unknown",
            className: "status-badge--unknown",
          };

          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`status-badge ${className} rounded-pill p-1`}
                style={{ fontSize: "0.8rem", fontWeight: 600 }}
              >
                {label}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          let display = value;
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              display = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
            }
          }
          const classKey = `lead-created-${row.original.id}`;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line ${classKey} mx-auto text-decoration-none`}
                style={{ maxWidth: "180px" }}
              >
                {display || "-"}
              </Link>
              {display && (
                <Tooltip
                  anchorSelect={`.${classKey}`}
                  content={display}
                  className={"bg-dark text-white"}
                />
              )}
            </div>
          );
        },
      },
    ],
    [t]
  );

  // Since pagination is handled by the API, we don't need to slice the data
  const data = useMemo(() => ads, [ads]);

  return (
    <div className="content-container">
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">
              {showLeadsDetail
                ? t("adsTable.leadsForAd", { adName: selectedAd?.ad_name })
                : t("adsTable.title")}
            </h4>
            {showLeadsDetail && (
              <Button
                variant="outline-secondary"
                onClick={handleBackToAds}
                className="d-flex align-items-center"
              >
                <FaArrowLeft className="me-2" />
                {t("adsTable.columns.backToAds")}
              </Button>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              {!showLeadsDetail && (
                <div
                  className="mb-3"
                  style={{
                    opacity: loading ? 0.5 : 1,
                  }}
                >
                  <Row className="justify-content-end align-items-center">
                    <Col lg={4} md={4} sm={12}>
                      <Form.Group className="position-relative">
                        <Form.Control
                          placeholder={`${t(
                            "tableControls.placeholders.searchTable"
                          )} ${ads.length} ${t(
                            "tableControls.placeholders.records"
                          )}...`}
                          value={searchTerm}
                          onChange={handleSearchChange}
                          className="rounded-pill"
                        />
                        <FaMagnifyingGlass
                          className="text-muted position-absolute"
                          style={{
                            right: "10px",
                            top: "50%",
                            transform: "translateY(-50%)",
                          }}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              )}

              <DataTableComponent
                columns={showLeadsDetail ? leadsColumns : adsColumnsConfig}
                data={showLeadsDetail ? leads : data}
                loading={loading}
                initialSortBy={[]}
                hiddenColumns={[]}
                noDataFound={
                  showLeadsDetail
                    ? t("leadsTable.noLeadsFound") || "No Leads Found"
                    : t("adsTable.noAdsFound") || "No Ads Found"
                }
              />

              {!showLeadsDetail && <PaginationComponent />}
            </>
          )}
        </Card.Body>
      </Card>
    </div>
  );
}
