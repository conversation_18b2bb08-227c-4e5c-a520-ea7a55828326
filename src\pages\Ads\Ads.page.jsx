import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  setCurrentPage,
  setRecordsPerPage,
  setTotalPages,
} from "../../redux/features/leadsPaginationSlice";
import { Card, Form, Row, Col, Button } from "react-bootstrap";
import { FaMagnifyingGlass, FaArrowLeft } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationComponent from "../../components/CustomDataTable/PaginationComponent";
import { Tooltip } from "react-tooltip";
import { ReactSVG } from "react-svg";
import { sourceToIcon } from "../../constants/sourceIcons";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import adsService from "../../services/ads";
import "./Ads.page.css";

export default function AdsPage() {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { currentPage: reduxCurrentPage, recordsPerPage: reduxRecordsPerPage } =
    useSelector((state) => state.leadsPagination);
  const { adsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [ads, setAds] = useState([]);
  const [leads, setLeads] = useState([]);
  const [currentPage, setCurrentPageLocal] = useState(1);
  const [recordsPerPage, setRecordsPerPageLocal] = useState(10);
  const [totalPages, setTotalPagesLocal] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [leadsSearchTerm, setLeadsSearchTerm] = useState("");
  const [showLeadsDetail, setShowLeadsDetail] = useState(false);
  const [selectedAd, setSelectedAd] = useState(null);

  useEffect(() => {
    fetchAds();
  }, [currentPage, recordsPerPage, searchTerm]);

  // Effect to handle Redux pagination changes for leads
  useEffect(() => {
    if (showLeadsDetail && selectedAd && reduxCurrentPage > 0) {
      fetchLeadsForAd(selectedAd.ad_id, reduxCurrentPage, reduxRecordsPerPage);
    }
  }, [reduxCurrentPage, reduxRecordsPerPage, showLeadsDetail, selectedAd]);

  const fetchAds = async () => {
    setLoading(true);
    try {
      const response = await adsService.getAllAdsApi(
        recordsPerPage,
        currentPage,
        searchTerm
      );

      if (
        (response?.success || response?.message === "Success") &&
        response?.data
      ) {
        setAds(response.data || []);
        const totalPagesCount = response.data.last_page || 1;
        setTotalPagesLocal(totalPagesCount);

        // Update Redux state
        dispatch(setCurrentPage(currentPage));
        dispatch(setRecordsPerPage(recordsPerPage));
        dispatch(setTotalPages(totalPagesCount));
      } else {
        setAds([]);
        setTotalPagesLocal(0);
      }
    } catch (error) {
      console.error("Error fetching ads:", error);
      setAds([]);
      setTotalPagesLocal(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchLeadsForAd = async (adId, page = 1, recordsPerPage = 10) => {
    setLoading(true);
    try {
      const response = await adsService.getAdLeadsApi(
        adId,
        recordsPerPage,
        page
      );

      if (
        (response?.status === 200 ||
          response?.success ||
          response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - leads are in response.data.data
        setLeads(response.data.data || []);

        // Update Redux state for PaginationComponent when showing leads
        if (showLeadsDetail) {
          dispatch(setCurrentPage(response.data.current_page || 1));
          dispatch(setTotalPages(response.data.last_page || 1));
          dispatch(setRecordsPerPage(recordsPerPage));
        }
      } else {
        setLeads([]);
      }
    } catch (error) {
      console.error("Error fetching leads for ad:", error);
      setLeads([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    setCurrentPageLocal(1);
    dispatch(setCurrentPage(1));
  };

  const handleLeadsSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setLeadsSearchTerm(newSearchTerm);
    // Reset to first page when searching
    dispatch(setCurrentPage(1));
  };

  const handleViewLeads = (ad) => {
    setSelectedAd(ad);
    setShowLeadsDetail(true);

    // Reset Redux pagination state for leads
    dispatch(setCurrentPage(1));
    dispatch(setRecordsPerPage(10));
    dispatch(setTotalPages(1));

    fetchLeadsForAd(ad.ad_id, 1, 10);
  };

  const handleBackToAds = () => {
    setShowLeadsDetail(false);
    setSelectedAd(null);
    setLeads([]);
    setLeadsSearchTerm(""); // Reset leads search

    // Restore ads pagination state in Redux
    dispatch(setCurrentPage(currentPage));
    dispatch(setRecordsPerPage(recordsPerPage));
    dispatch(setTotalPages(totalPages));
  };

  // Get ads columns from the module
  const adsColumnsConfig = useMemo(
    () => adsColumns(handleViewLeads),
    [adsColumns, handleViewLeads]
  );

  // Leads table columns
  const leadsColumns = useMemo(
    () => [
      {
        Header: t("leadsTable.columns.id"),
        accessor: "id",
        Cell: ({ row }) => (
          <div className="text-center">
            <span>{row.index + 1}</span>
          </div>
        ),
      },
      {
        Header: t("leadsTable.columns.contactName"),
        accessor: "name",
        Cell: ({ row }) => {
          const name = row.original.name;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-name-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "200px", color: "inherit" }}
              >
                {name}
              </Link>
              <Tooltip
                anchorSelect={`.lead-name-${row.original.id}`}
                content={name}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.phone"),
        accessor: "phone",
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-phone-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "150px", color: "inherit" }}
              >
                {phone}
              </Link>
              <Tooltip
                anchorSelect={`.lead-phone-${row.original.id}`}
                content={phone}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("tables.headers.email"),
        accessor: "email",
        Cell: ({ row }) => {
          const email = row.original.email;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line lead-email-${row.original.id} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "200px", color: "inherit" }}
              >
                {email}
              </Link>
              <Tooltip
                anchorSelect={`.lead-email-${row.original.id}`}
                content={email}
                className={"bg-dark text-white"}
              />
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.source"),
        accessor: "source",
        Cell: ({ row }) => {
          const source = row.original.source;
          const IconComponent = sourceToIcon[source] || null;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className="mx-auto social-icon-container text-decoration-none"
                style={{ color: "inherit" }}
              >
                {IconComponent && <ReactSVG src={IconComponent} />}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.status"),
        accessor: "status",
        Cell: ({ row }) => {
          const status = row.original.status;
          const statusMapping = {
            0: {
              label: t("status.pending"),
              className: "status-badge--pending",
            },
            1: {
              label: t("status.inProgress"),
              className: "status-badge--in-progress",
            },
            2: {
              label: t("status.completed"),
              className: "status-badge--completed",
            },
            3: {
              label: t("status.rejected"),
              className: "status-badge--rejected",
            },
            4: {
              label: t("status.wrongLead"),
              className: "status-badge--wrong-lead",
            },
            5: {
              label: t("status.notQualified"),
              className: "status-badge--not-qualified",
            },
            6: {
              label: t("status.noCommunication"),
              className: "status-badge--no-communication",
            },
            7: { label: t("status.booked"), className: "status-badge--booked" },
            8: {
              label: t("status.bookedReserved"),
              className: "status-badge--booked-reserved",
            },
            9: {
              label: t("status.canceled"),
              className: "status-badge--canceled",
            },
            10: {
              label: t("status.quotation"),
              className: "status-badge--quotation-sent",
            },
            11: {
              label: t("status.assigned"),
              className: "status-badge--in-progress",
            },
          };

          const { label, className } = statusMapping[status] || {
            label: "Unknown",
            className: "status-badge--unknown",
          };

          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`status-badge ${className} rounded-pill p-1 text-decoration-none`}
                style={{
                  fontSize: "0.8rem",
                  fontWeight: 600,
                  color: "inherit",
                }}
              >
                {label}
              </Link>
            </div>
          );
        },
      },
      {
        Header: t("leadsTable.columns.createdAt"),
        accessor: "createdAt",
        Cell: ({ value, row }) => {
          let display = value;
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              display = format(parsedDate, "yyyy-MM-dd HH:mm:ss");
            }
          }
          const classKey = `lead-created-${row.original.id}`;
          return (
            <div className="text-center">
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line ${classKey} mx-auto text-decoration-none text-dark`}
                style={{ maxWidth: "180px", color: "inherit" }}
              >
                {display || "-"}
              </Link>
              {display && (
                <Tooltip
                  anchorSelect={`.${classKey}`}
                  content={display}
                  className={"bg-dark text-white"}
                />
              )}
            </div>
          );
        },
      },
    ],
    [t]
  );

  // Since pagination is handled by the API, we don't need to slice the data
  const data = useMemo(() => ads, [ads]);

  // Filter leads based on search term (frontend filtering)
  const filteredLeads = useMemo(() => {
    if (!leadsSearchTerm.trim()) {
      return leads;
    }

    const searchLower = leadsSearchTerm.toLowerCase();
    return leads.filter((lead) => {
      return (
        lead.name?.toLowerCase().includes(searchLower) ||
        lead.email?.toLowerCase().includes(searchLower) ||
        lead.phone?.toLowerCase().includes(searchLower)
      );
    });
  }, [leads, leadsSearchTerm]);

  // Paginate filtered leads (frontend pagination for leads)
  const paginatedLeads = useMemo(() => {
    const startIndex = (reduxCurrentPage - 1) * reduxRecordsPerPage;
    const endIndex = startIndex + reduxRecordsPerPage;
    return filteredLeads.slice(startIndex, endIndex);
  }, [filteredLeads, reduxCurrentPage, reduxRecordsPerPage]);

  // Update pagination when filtered leads change
  useEffect(() => {
    if (showLeadsDetail) {
      const totalFilteredPages = Math.ceil(
        filteredLeads.length / reduxRecordsPerPage
      );
      dispatch(setTotalPages(totalFilteredPages || 1));

      // If current page is beyond available pages, reset to page 1
      if (reduxCurrentPage > totalFilteredPages && totalFilteredPages > 0) {
        dispatch(setCurrentPage(1));
      }
    }
  }, [
    filteredLeads.length,
    reduxRecordsPerPage,
    showLeadsDetail,
    reduxCurrentPage,
    dispatch,
  ]);

  return (
    <div className="content-container">
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4 className="mb-0">
              {showLeadsDetail
                ? t("adsTable.leadsForAd", { adName: selectedAd?.ad_name })
                : t("adsTable.title")}
            </h4>
            {showLeadsDetail && (
              <Button
                variant="outline-secondary"
                onClick={handleBackToAds}
                className="d-flex align-items-center"
              >
                <FaArrowLeft className="me-2" />
                {t("adsTable.columns.backToAds")}
              </Button>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <div
                className="mb-3"
                style={{
                  opacity: loading ? 0.5 : 1,
                }}
              >
                <Row className="justify-content-end align-items-center">
                  <Col lg={4} md={4} sm={12}>
                    <Form.Group className="position-relative">
                      <Form.Control
                        placeholder={
                          showLeadsDetail
                            ? `${t("tableControls.placeholders.searchTable")} ${
                                filteredLeads.length
                              } leads...`
                            : `${t("tableControls.placeholders.searchTable")} ${
                                ads.length
                              } ${t("tableControls.placeholders.records")}...`
                        }
                        value={showLeadsDetail ? leadsSearchTerm : searchTerm}
                        onChange={
                          showLeadsDetail
                            ? handleLeadsSearchChange
                            : handleSearchChange
                        }
                        className="rounded-pill"
                      />
                      <FaMagnifyingGlass
                        className="text-muted position-absolute"
                        style={{
                          right: "10px",
                          top: "50%",
                          transform: "translateY(-50%)",
                        }}
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </div>

              <DataTableComponent
                columns={showLeadsDetail ? leadsColumns : adsColumnsConfig}
                data={showLeadsDetail ? paginatedLeads : data}
                loading={loading}
                initialSortBy={[]}
                hiddenColumns={[]}
                noDataFound={
                  showLeadsDetail
                    ? t("leadsTable.noLeadsFound") || "No Leads Found"
                    : t("adsTable.noAdsFound") || "No Ads Found"
                }
              />

              <PaginationComponent />
            </>
          )}
        </Card.Body>
      </Card>
    </div>
  );
}
