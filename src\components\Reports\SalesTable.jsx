import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { Button, Col, Form, Row, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { FaCalendarAlt } from "react-icons/fa";
import { TiArrowForward } from "react-icons/ti";
import DatePicker from "react-datepicker";
import { toast } from "react-toastify";
import { getSalesFilterTableAPI } from "../../services/reports/get-leads-reports.api";
import { useTranslatedColumns } from "./ColumnsForTables.module";
import PaginationRecordsForReports from "./PaginationRecordsForReports";
import { useTranslation } from "react-i18next";
import { ExportSalesPerformanceReportsApi } from "../../services/reports/export-reports.api";
import {
  setSalesFilters,
  clearSalesFilters,
} from "../../redux/features/reportsSlice";

const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split(".")[0];
};

function GlobalFilter({
  setGlobalFilter,
  fetchInitialData,
  recordsPerPage,
  currentPage,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  selectedSalesTM,
  setSelectedSalesTM,
  salesStatus,
  setSalesStatus,
}) {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { teamMembers } = useSelector((state) => state.client);

  const activeFiltersRef = useRef({
    activeStatusSales: null,
    activeTMSales: null,
    activeFromDateSales: null,
    activeToDateSales: null,
  });

  const setActiveStatesForExport = () => {
    activeFiltersRef.current = {
      activeStatusSales: salesStatus,
      activeTMSales: selectedSalesTM?.id,
      activeFromDateSales: startDate,
      activeToDateSales: endDate,
    };
  };

  const handleExport = async () => {
    setActiveStatesForExport();
    const params = {
      from: activeFiltersRef.current.activeFromDateSales,
      to: activeFiltersRef.current.activeToDateSales,
      member: activeFiltersRef.current.activeTMSales,
      status: activeFiltersRef.current.activeStatusSales,
      page: currentPage,
      number_of_records: recordsPerPage,
    };
    await ExportSalesPerformanceReportsApi(params);
  };

  const applyFilters = async () => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedSalesTM?.id,
      recordsPerPage,
      currentPage,
      status: salesStatus,
    };
    await fetchInitialData(params);

    dispatch(
      setSalesFilters({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        selectedTM: selectedSalesTM,
        status: salesStatus,
      })
    );
  };

  const clearFilters = () => {
    setGlobalFilter("");
    setStartDate(null);
    setEndDate(null);
    setSelectedSalesTM(null);
    setSalesStatus(null);
    fetchInitialData({ currentPage, recordsPerPage });

    dispatch(clearSalesFilters());
  };

  return (
    <Row className={"align-items-center justify-content-evenly"}>
      <Col lg={2} sm={6} className={"my-2"}>
        <Form.Select
          value={selectedSalesTM?.id || ""}
          className={"rounded-pill"}
          onChange={(e) => setSelectedSalesTM({ id: e.target.value })}
        >
          <option value="">{t("filters.selectTeamMember")}</option>
          {teamMembers?.length > 0
            ? teamMembers?.map((teamMember) => (
                <option key={teamMember?.id} value={teamMember?.id}>
                  {teamMember?.name}
                </option>
              ))
            : null}
        </Form.Select>
      </Col>
      <Col lg={2} sm={6} className={"my-2"}>
        <Form.Select
          value={
            salesStatus !== null && salesStatus !== undefined ? salesStatus : ""
          }
          className={"rounded-pill"}
          onChange={(e) =>
            setSalesStatus(
              e.target.value === "" ? null : Number(e.target.value)
            )
          }
        >
          <option value="">{t("filters.selectStatus")}</option>
          <option value="0">{t("status.pending")}</option>
          <option value="1">{t("status.inProgress")}</option>
          <option value="2">{t("status.completed")}</option>
          <option value="3">{t("status.rejected")}</option>
        </Form.Select>
      </Col>

      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={startDate}
          selected={startDate || null}
          onChange={(date) => setStartDate(date)}
          selectsStart
          startDate={startDate || null}
          endDate={endDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("filters.from")}
                defaultValue={
                  startDate ? new Date(startDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={endDate}
          selected={endDate}
          onChange={(date) => setEndDate(date)}
          selectsEnd
          startDate={startDate || null}
          endDate={endDate || null}
          minDate={startDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("filters.to")}
                defaultValue={
                  endDate ? new Date(endDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col
        lg={4}
        className={"d-flex justify-content-center justify-content-lg-end my-2"}
      >
        <Button onClick={applyFilters} className="px-3 py-2 apply-btn">
          {t("filters.apply")}
        </Button>
        <Button
          onClick={clearFilters}
          className="rounded-pill clear-btn px-3 mx-2"
        >
          {t("filters.clear")}
        </Button>
        <div
          className={
            "d-flex justify-content-between align-items-center rounded-pill bg-dark text-white px-3 py-1 fs-6"
          }
          role={"button"}
          onClick={handleExport}
        >
          <TiArrowForward />
          <div>{t("filters.export")}</div>
        </div>
      </Col>
    </Row>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const SalesTable = ({
  activeTab,
  setSalesStatus,
  salesStatus,
  setSelectedSalesTM,
  selectedSalesTM,
}) => {
  const dispatch = useDispatch();
  const savedSalesFilters = useSelector((state) => state.reports.salesFilters);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsToDisplay, setRecordsToDisplay] = useState(10); // Default value
  const [startDate, setStartDate] = useState(
    savedSalesFilters.startDate ? new Date(savedSalesFilters.startDate) : null
  );
  const [endDate, setEndDate] = useState(
    savedSalesFilters.endDate ? new Date(savedSalesFilters.endDate) : null
  );
  const [salesStatusLocal, setSalesStatusLocal] = useState(salesStatus);
  const fetchMembersAbortController = useRef(null);
  const initializedRef = useRef(false);
  const restoredDone = useRef(false);
  const hasFetched = useRef(false);

  // Restore filters on mount
  useEffect(() => {
    const navEntry = performance.getEntriesByType("navigation")[0];
    const isReload = navEntry && navEntry.type === "reload";
    if (isReload) {
      localStorage.removeItem("salesFilters");
    }

    const saved = isReload ? null : localStorage.getItem("salesFilters");
    if (saved) {
      try {
        const {
          startDate: sd,
          endDate: ed,
          selectedSalesTM: selTM,
          salesStatus: savedStatus,
        } = JSON.parse(saved);
        if (sd) setStartDate(new Date(sd));
        if (ed) setEndDate(new Date(ed));
        if (selTM) setSelectedSalesTM(selTM);
        if (savedStatus !== undefined && savedStatus !== null)
          setSalesStatusLocal(savedStatus);

        // Fetch data using restored filters
        if (activeTab === "sales") {
          const params = {
            from: formatDate(sd ? new Date(sd) : null),
            to: formatDate(ed ? new Date(ed) : null),
            member: selTM?.id,
            status: savedStatus,
            currentPage,
            recordsPerPage,
          };
          fetchInitialData(params);
        }
      } catch (e) {
        console.error("Failed to parse saved sales filters", e);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchInitialData = async (params) => {
    try {
      setLoading(true);

      // Abort previous fetch if any
      if (fetchMembersAbortController.current) {
        fetchMembersAbortController.current.abort();
      }
      fetchMembersAbortController.current = new AbortController();
      const signal = fetchMembersAbortController.current.signal;
      // Fetch data
      const response = await getSalesFilterTableAPI({ signal, params });

      // Handle response
      if (response?.success) {
        if (
          response?.result === "there is no data" ||
          (Array.isArray(response?.result) && response?.result.length === 0)
        ) {
          // Handle no data case
          setData([]);
          setTotal(0);
          setRecordsToDisplay(0);
          setPaginationLinks([]);
          toast.info("No data available for the selected filters.", {
            position: "bottom-right",
            theme: "dark",
          });
        } else {
          const { data, current_page, per_page, links, total, to } =
            response.result;
          setData(data);
          setCurrentPage(current_page);
          setRecordsPerPage(per_page);
          setPaginationLinks(links);
          setTotal(total);
          setRecordsToDisplay(to);
        }
      }
      setLoading(false);
      restoredDone.current = true;
    } catch (error) {
      console.error("Error fetching initial data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab !== "sales" || !restoredDone.current) return;

    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedSalesTM?.id,
      status: salesStatusLocal,
      currentPage,
      recordsPerPage,
    };
    fetchInitialData(params);
  }, [activeTab, currentPage, recordsPerPage]);

  // when no saved filters
  useEffect(() => {
    if (restoredDone.current) return;
    if (!localStorage.getItem("salesFilters")) {
      restoredDone.current = true;
      if (activeTab === "sales") {
        fetchInitialData({ currentPage, recordsPerPage });
      }
    }
  }, [activeTab]);

  // Ensure first-time fetch when tab becomes active
  useEffect(() => {
    if (activeTab !== "sales" || hasFetched.current) return;

    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedSalesTM?.id,
      status: salesStatusLocal,
      currentPage,
      recordsPerPage,
    };
    fetchInitialData(params);
    hasFetched.current = true;
  }, [activeTab]);

  const defaultColumn = useMemo(
    () => ({
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const { salesColumns } = useTranslatedColumns();
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setPageSize,
    rows,
    state: { globalFilter },
  } = useTable(
    {
      columns: salesColumns,
      data: data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handlePageChange = async (url) => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedSalesTM?.id,
      url,
      recordsPerPage,
    };
    await fetchInitialData(params);
  };

  const handlePageSizeChange = async (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedSalesTM?.id,
      currentPage,
      recordsPerPage: size,
    };
    await fetchInitialData(params);
  };

  return loading ? (
    <FetchingDataLoading className={"content-container"} />
  ) : (
    <>
      <div className={"content-container"}>
        <GlobalFilter
          preGlobalFilteredRows={preGlobalFilteredRows}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          // handleFilterByDuration={handleFilterByDuration}
          fetchInitialData={fetchInitialData}
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          selectedSalesTM={selectedSalesTM}
          setSelectedSalesTM={setSelectedSalesTM}
          recordsPerPage={recordsPerPage}
          currentPage={currentPage}
          salesStatus={salesStatusLocal}
          setSalesStatus={setSalesStatusLocal}
        />
      </div>
      <div className={"all-leads-table px-2"}>
        <Table
          responsive={"xl"}
          className="table text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <thead>
                {headerGroups?.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers?.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows?.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      style={{ cursor: "default" }}
                      key={row.original.id}
                    >
                      {row?.cells?.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
        />
      </div>
    </>
  );
};

export default SalesTable;
