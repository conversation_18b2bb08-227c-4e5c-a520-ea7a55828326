import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { collection, onSnapshot, orderBy, query, doc, limit } from 'firebase/firestore';
import metaService from '../../services/integrations/meta'; // Assuming metaService handles both Meta and WhatsApp APIs
import { showErrorToast } from '../../utils/toast-success-error';
import { db } from '../../utils/firebase.config';

// Async thunk for fetching WhatsApp messages
export const fetchWhatsAppMessages = createAsyncThunk(
  'whatsappChat/fetchWhatsAppMessages',
  async ({ thread, selectedPhone }, { dispatch, getState }) => {
    try {
      // Abort controller logic should ideally be managed within the component or a higher-level effect
      // For Redux Thunk, we'll assume the API call handles its own cancellation or we'll refactor it.
      // For now, we'll just call the service.

      const result = await metaService.getMessagesForWhatsappChatApi({
        id: thread?.id,
      });

      if (result?.data) {
        const apiMessages = result.data;

        if (apiMessages.length > 0) {
          const latestMessage = apiMessages.sort((a, b) =>
            new Date(b.created_at || b.timestamp) - new Date(a.created_at || a.timestamp)
          )[0];
          dispatch(whatsappChatSlice.actions.setSelectedWhatsappChatUpdatedTime(latestMessage.created_at || latestMessage.timestamp || new Date().toISOString()));
        }

        apiMessages.sort((a, b) => new Date(a.created_at || a.timestamp) - new Date(b.created_at || b.timestamp));

        const formattedApiMessages = apiMessages.map(msg => {
          const formattedMsg = {
            id: msg.id || `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message_id: msg.id || msg.message_id,
            created_time: msg.created_at || msg.timestamp,
            message: msg.message,
            url: msg.type === "image" ? msg.url : msg.message,
            type: msg.type || "text",
            sender: msg.from === selectedPhone?.id ? selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '') : thread?.sender_phone_number?.trim().replace(/^\+|\s+/g, ''),
            recipient: msg.from === selectedPhone?.id ? thread?.sender_phone_number?.trim().replace(/^\+|\s+/g, '') : selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '')
          };
          return formattedMsg;
        });

        dispatch(whatsappChatSlice.actions.setWhatsappChatMessages(formattedApiMessages));

        const senderPhoneId = thread?.sender_phone_number.trim().replace(/^\+|\s+/g, '');

        if (senderPhoneId) {
          const nestedCollectionRef = collection(
            db,
            "whatsApp",
            senderPhoneId,
            "messages"
          );
          const nestedQuery = query(
            nestedCollectionRef,
            orderBy("created_time", "desc")
          );

          // This part needs to be handled carefully with Redux Toolkit.
          // onSnapshot returns an unsubscribe function. We need to store it and call it when the component unmounts or chat changes.
          // For now, we'll just return the snapshot data.
          // A better approach for real-time listeners in Redux is to use a middleware or a custom hook.
          onSnapshot(nestedQuery, (snapshot) => {
            const nestedMessages = snapshot.docs.map((doc) => {
              const data = doc.data();
              return {
                id: doc.id,
                message_id: data?.message_id || doc.id,
                created_time: data?.created_time,
                message: data?.message,
                type: data?.type || "text",
                sender: data?.sender,
                sender_name: data?.sender_name,
                recipient: data?.recipient
              };
            });

            if (nestedMessages.length > 0) {
              const latestMessage = nestedMessages.sort((a, b) =>
                new Date(b.created_time) - new Date(a.created_time)
              )[0];
              dispatch(whatsappChatSlice.actions.setSelectedWhatsappChatUpdatedTime(latestMessage.created_time));
            }

            dispatch(whatsappChatSlice.actions.addFirestoreWhatsappMessages(nestedMessages));
          });
        }
      }
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Fetch WhatsApp messages request aborted");
      } else {
        console.error("Error fetching WhatsApp messages:", error);
        showErrorToast("Error loading WhatsApp messages");
      }
      throw error; // Re-throw to allow error handling in component
    }
  }
);

// Async thunk for sending WhatsApp messages
export const sendWhatsMessage = createAsyncThunk(
  'whatsappChat/sendWhatsMessage',
  async ({ data, selectedPhone, selectedChat, selectedWhatsAccount, addSendingMessage, removeSendingMessage }, { dispatch }) => {
    const tempMessageId = `temp_${Date.now()}`;

    try {
      // Log the FormData entries to debug attachment issues
      console.log("FormData entries in sendWhatsMessage:");
      for (let pair of data.entries()) {
        console.log(pair[0], pair[1]);
      }

      // Create temporary message for UI feedback if needed
      if (addSendingMessage) {
        const messageType = data.get("type") || "text";
        const messageText = data.get("text") || "";
        const isMediaMessage = messageType === "image" ||
          messageType === "video" ||
          messageType === "audio" ||
          messageType === "sticker" ||
          messageType === "file";

        const tempMessage = {
          id: tempMessageId,
          type: messageType,
          message: messageText,
          created_time: new Date().toISOString(),
          sender: selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, ''),
          recipient: selectedChat?.sender_phone_number?.trim().replace(/^\+|\s+/g, '')
        };

        addSendingMessage(tempMessage);
      }

      const response = await metaService.sendWhatsMessageApi(data);
      const fileUrl = response?.file_url || null;

      // Special handling for error code 100 with subcode 2018047 (Upload attachment failure)
      // If we have a file_url despite the error, we can still proceed with the message
      const isUploadFailureWithUrl =
        response?.error?.code === 100 &&
        response?.error?.error_subcode === 2018047 &&
        fileUrl;

      if (response?.error && !isUploadFailureWithUrl) {
        console.error("Error in WhatsApp API response:", response.error);

        // Remove temporary message
        if (removeSendingMessage) {
          removeSendingMessage(tempMessageId);
        }

        showErrorToast(response?.error?.message || "Error sending WhatsApp message");
        throw new Error(response?.error?.message || "Error sending WhatsApp message");
      }

      // If we have an upload failure but still got a URL, log it but continue
      if (isUploadFailureWithUrl) {
        console.warn("WhatsApp API reported upload failure but returned a URL. Proceeding with message.", {
          error: response.error,
          fileUrl
        });
      }

      const currentDate = new Date();
      const formattedDate = currentDate.toISOString();

      const senderPhone = selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, '');
      const recipientPhone = selectedChat?.sender_phone_number?.trim().replace(/^\+|\s+/g, '');
      const senderName = selectedWhatsAccount?.name || "Business";
      const messageText = data.get("text") || "";
      const messageType = data.get("type") || "text";

      // Check if this is a media message
      const isMediaMessage = messageType === "image" ||
        messageType === "video" ||
        messageType === "audio" ||
        messageType === "sticker" ||
        messageType === "file";

      // Create the message object with the URL field for media messages
      const newMessage = {
        id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        message_id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        type: messageType,
        sender: senderPhone,
        sender_name: senderName,
        recipient: recipientPhone,
        message: isMediaMessage ? (response?.file_url || fileUrl) : messageText,
        url: isMediaMessage ? (response?.file_url || fileUrl) : null,
        created_time: formattedDate,
        mime_type: messageType === "audio" ? "audio/mp3" : null
      };

      // Remove temporary message
      if (removeSendingMessage) {
        removeSendingMessage(tempMessageId);
      }

      // Add the sent message to the state
      dispatch(addSentWhatsappMessage(newMessage));

      // Update the selected chat's timestamp
      dispatch(setSelectedWhatsappChatUpdatedTime(formattedDate));

      return { success: true, message: newMessage };
    } catch (error) {
      console.error("Error in sendWhatsMessage:", error);

      // Remove temporary message
      if (removeSendingMessage) {
        removeSendingMessage(tempMessageId);
      }

      showErrorToast("Error sending WhatsApp message");
      return { error: "Error sending WhatsApp message" };
    }
  }
);

// Async thunk for filtering WhatsApp conversations
export const filterWhatsAppConversations = createAsyncThunk(
  'whatsappChat/filterWhatsAppConversations',
  async ({ accessToken, id, startDate, endDate }, { dispatch }) => {
    try {
      const response = await metaService.getWhatsAppFilterConversations({
        accessToken,
        id,
        startDate,
        endDate,
      });
      dispatch(whatsappChatSlice.actions.setConversations(response?.conversation_analytics?.data[0]?.data_points));
      return response?.conversation_analytics?.data[0]?.data_points;
    } catch (error) {
      console.error("Error filtering WhatsApp conversations:", error);
      showErrorToast("Error filtering WhatsApp conversations");
      throw error;
    }
  }
);

// Async thunk for fetching latest WhatsApp messages (Firestore listener)
export const fetchWhatsAppLatestMessages = createAsyncThunk(
  'whatsappChat/fetchWhatsAppLatestMessages',
  async (_, { dispatch, getState }) => {
    try {
      const state = getState();
      const selectedPhone = state.whatsAppChat.selectedPhone;

      if (!selectedPhone) {
        return null;
      }

      // Get the business phone number in a consistent format
      const businessPhoneNumber = selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '');

      // Use a more efficient query with limit
      const whatsAppQuery = query(
        collection(db, "whatsApp"),
        orderBy("created_time", "desc"),
        limit(50) // Limit to a reasonable number of documents
      );

      const unsubscribe = onSnapshot(whatsAppQuery, (snapshot) => {
        // Only process if we have changes
        if (!snapshot.empty) {
          const changes = snapshot.docChanges();

          if (changes.length > 0) {
            // Get current state directly
            const currentState = getState().whatsAppChat;
            const currentChats = currentState.whatsappChats || [];

            // Create a map for faster lookups
            const chatsByPhone = new Map();

            // First, add existing chats to the map
            currentChats.forEach(chat => {
              const phoneNumber = chat.sender_phone_number?.trim().replace(/^\+|\s+/g, '');
              if (phoneNumber) {
                chatsByPhone.set(phoneNumber, chat);
              }
            });

            // Process changes
            changes.forEach(change => {
              const doc = change.doc;
              const data = doc.data();

              // Only include chats where this business phone is the sender or recipient
              if (data.sender !== businessPhoneNumber && data.recipient !== businessPhoneNumber) {
                return;
              }

              // Determine the customer's phone number
              const customerPhone = data.sender === businessPhoneNumber
                ? data.recipient
                : data.sender;

              if (!customerPhone) return;

              if (change.type === 'added' || change.type === 'modified') {
                // Update or add to our map
                const existingChat = chatsByPhone.get(customerPhone);

                if (existingChat) {
                  // Update if the new message is more recent
                  const existingDate = new Date(
                    existingChat.last_message?.created_at ||
                    existingChat.updated_at ||
                    existingChat.created_at
                  );

                  const newDate = new Date(data.created_time);

                  if (newDate > existingDate) {
                    chatsByPhone.set(customerPhone, {
                      ...existingChat,
                      last_message: {
                        message: data.message,
                        created_at: data.created_time
                      },
                      updated_at: data.created_time
                    });
                  }
                } else {
                  // Add new chat
                  chatsByPhone.set(customerPhone, {
                    id: doc.id,
                    sender_phone_number: data.sender === businessPhoneNumber ? data.recipient : data.sender,
                    sender_name: data.sender_name || data.sender,
                    last_message: {
                      message: data.message,
                      created_at: data.created_time
                    },
                    created_at: data.created_time,
                    updated_at: data.created_time
                  });
                }
              } else if (change.type === 'removed') {
                // Remove from our map if it exists
                chatsByPhone.delete(customerPhone);
              }
            });

            // Convert the map back to an array
            const mergedChats = Array.from(chatsByPhone.values());

            // Sort by latest message time
            const sortedChats = mergedChats.sort((a, b) => {
              const timeA = a.last_message?.created_at ? new Date(a.last_message.created_at) :
                a.updated_at ? new Date(a.updated_at) :
                  new Date(a.created_at);

              const timeB = b.last_message?.created_at ? new Date(b.last_message.created_at) :
                b.updated_at ? new Date(b.updated_at) :
                  new Date(b.created_at);

              return timeB - timeA; // Sort in descending order (newest first)
            });

            // Only dispatch if there are actual changes
            if (JSON.stringify(sortedChats) !== JSON.stringify(currentChats)) {
              dispatch(whatsappChatSlice.actions.setWhatsappChats(sortedChats));
            }
          }
        }
      });

      return unsubscribe;
    } catch (error) {
      console.error("Error fetching WhatsApp latest messages:", error);
      return null;
    }
  }
);

// Add a new action to select a WhatsApp chat
export const selectWhatsappChatAndFetchMessages = createAsyncThunk(
  'whatsAppChat/selectWhatsappChatAndFetchMessages',
  async (chat, { dispatch, getState }) => {
    // First, clear all existing messages and unsubscribe from any listeners
    dispatch(clearWhatsappChatMessages());

    // Set the selected chat
    dispatch(setSelectedWhatsappChat(chat));

    // If there's a chat selected, fetch its messages
    if (chat) {
      const state = getState();
      const selectedPhone = state.whatsAppChat.selectedPhone;

      // Fetch messages for the selected chat
      await dispatch(fetchWhatsAppMessages(chat));
    }

    return chat;
  }
);

const whatsappChatSlice = createSlice({
  name: 'whatsappChat',
  initialState: {
    whatsappChats: [],
    whatsappChatMessages: [],
    whatsAppAccounts: [],
    selectedWhatsAccount: null,
    selectedWhatsappChat: null,
    whatsAppAccountDetails: [],
    selectedPhone: null,
    loading: false,
    error: null,
    startDate: "", // Added for filtering
    endDate: "",   // Added for filtering
    conversations: [], // Added for filtering
    latestMessages: [], // Added for fetchWhatsAppLatestMessages
    messagesSnapshotUnsubscribe: null, // New state for unsubscribe function
  },
  reducers: {
    setWhatsappChats: (state, action) => {
      state.whatsappChats = action.payload;
    },
    setWhatsappChatMessages: (state, action) => {
      state.whatsappChatMessages = action.payload;
    },
    addFirestoreWhatsappMessages: (state, action) => {
      action.payload.forEach(newMsg => {
        const exists = state.whatsappChatMessages.some(msg =>
          msg.id === newMsg.id ||
          (msg.message_id && msg.message_id === newMsg.message_id) ||
          (msg.message === newMsg.message &&
            msg.sender === newMsg.sender &&
            msg.created_time === newMsg.created_time)
        );
        if (!exists) {
          state.whatsappChatMessages.push(newMsg);
        }
      });
      state.whatsappChatMessages.sort((a, b) => {
        const dateA = new Date(a.created_time.replace(' ', 'T'));
        const dateB = new Date(b.created_time.replace(' ', 'T'));
        return dateA - dateB;
      });
    },
    addSentWhatsappMessage: (state, action) => {
      state.whatsappChatMessages.push(action.payload);
      state.whatsappChatMessages.sort((a, b) => {
        const dateA = new Date(a.created_time.replace(' ', 'T'));
        const dateB = new Date(b.created_time.replace(' ', 'T'));
        return dateA - dateB;
      });
    },
    setWhatsAppAccounts: (state, action) => {
      state.whatsAppAccounts = action.payload;
    },
    setSelectedWhatsAccount: (state, action) => {
      state.selectedWhatsAccount = action.payload;
    },
    setSelectedWhatsappChat: (state, action) => {
      state.selectedWhatsappChat = action.payload;
    },
    setSelectedWhatsappChatUpdatedTime: (state, action) => {
      if (state.selectedWhatsappChat) {
        state.selectedWhatsappChat.updated_at = action.payload;
      }
    },
    setWhatsAppAccountDetails: (state, action) => {
      state.whatsAppAccountDetails = action.payload;
    },
    setSelectedPhone: (state, action) => {
      state.selectedPhone = action.payload;
    },
    clearWhatsappChatMessages: (state) => {
      state.whatsappChatMessages = [];

      // If there's an active listener, we need to unsubscribe
      if (state.messagesSnapshotUnsubscribe) {
        try {
          state.messagesSnapshotUnsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from WhatsApp messages snapshot:", error);
        }
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    setMessagesSnapshotUnsubscribe: (state, action) => {
      // If there's an existing unsubscribe function, call it first
      if (state.messagesSnapshotUnsubscribe) {
        try {
          state.messagesSnapshotUnsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from WhatsApp messages snapshot:", error);
        }
      }

      // Set the new unsubscribe function
      state.messagesSnapshotUnsubscribe = action.payload;
    },
    setStartDate: (state, action) => { // New reducer
      state.startDate = action.payload;
    },
    setEndDate: (state, action) => {   // New reducer
      state.endDate = action.payload;
    },
    setConversations: (state, action) => { // New reducer
      state.conversations = action.payload;
    },
    setLatestMessages: (state, action) => { // New reducer for latest messages
      state.latestMessages = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWhatsAppMessages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWhatsAppMessages.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(fetchWhatsAppMessages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(sendWhatsMessage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendWhatsMessage.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(sendWhatsMessage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(filterWhatsAppConversations.pending, (state) => { // New extra reducer
        state.loading = true;
        state.error = null;
      })
      .addCase(filterWhatsAppConversations.fulfilled, (state) => { // New extra reducer
        state.loading = false;
      })
      .addCase(filterWhatsAppConversations.rejected, (state, action) => { // New extra reducer
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchWhatsAppLatestMessages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWhatsAppLatestMessages.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(fetchWhatsAppLatestMessages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  },
});

export const {
  setWhatsappChats,
  setWhatsappChatMessages,
  addFirestoreWhatsappMessages,
  addSentWhatsappMessage,
  setWhatsAppAccounts,
  setSelectedWhatsAccount,
  setSelectedWhatsappChat,
  setSelectedWhatsappChatUpdatedTime,
  setWhatsAppAccountDetails,
  setSelectedPhone,
  clearWhatsappChatMessages,
  setMessagesSnapshotUnsubscribe,
  setStartDate,
  setEndDate,
  setConversations,
  setLatestMessages,
} = whatsappChatSlice.actions;

// Selectors
export const selectWhatsappChatMessages = (state) => state.whatsAppChat.whatsappChatMessages;
export const selectSelectedWhatsappChat = (state) => state.whatsAppChat.selectedWhatsappChat;
export const selectWhatsappChats = (state) => state.whatsAppChat.whatsappChats;
export const selectSelectedWhatsAccount = (state) => state.whatsAppChat.selectedWhatsAccount;
export const selectWhatsAppAccounts = (state) => state.whatsAppChat.whatsAppAccounts;
export const selectSelectedPhone = (state) => state.whatsAppChat.selectedPhone;
export const selectLoading = (state) => state.whatsAppChat.loading;
export const selectLatestMessages = (state) => state.whatsAppChat.latestMessages; // Export the new selector

export default whatsappChatSlice.reducer;








