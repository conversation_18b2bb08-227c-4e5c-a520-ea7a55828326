.filter-table-rows a{
    text-decoration: none;
    color: black;
}

.data-table-pagination .page-link {
    margin: 10px 5px;
    color: rgba(0, 0, 0, 0.50);
    font-size: 1.25rem;
    font-weight: 400;
    border: unset;
    border-radius: 7px;
}

.data-table-pagination.no-margin .page-link {
    margin: 0;
}

.data-table-pagination .active>.page-link {
    background: linear-gradient(220deg, #92C020 -9.71%, #CAD511 117.08%);
    color: #FFF;
}

.page-number-input {
    width: fit-content;
    max-width: 50px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #9DC41D;
    background: #FFF;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
    padding: 10px 0 10px 10px;
    margin: 0 5px;
}

.records-buttons-container {
    border: 1px solid #9DC41D;
    border-radius: 5px;
    padding: 4px;
}

.record-button-selected {
    border: 1px solid #9DC41D;
    background: #9DC41D;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.24);
    color: #FFFFFF;
    padding: 6px 10px;
    border-radius: 4px;
}

.record-button {
    border-radius: 4px;
    color: #000;
    padding: 6px 10px;
}

.green-checkbox input {
    border: 1px solid #92BF20;
}

.filter-table-rows a:hover:not(.activity-button) {
    color: #92C020;
}

.all-leads-table {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    margin-top: 1rem;
    margin-bottom: 1rem;
}


.leads-table-navs .nav-link.active, .btn-link.active {
    color: #92C020;
    font-family: Kumbh Sans, sans-serif;
    font-weight: 400;
}

.leads-table-navs .nav-link, .btn-link {
    color: #000;
    font-size: 0.8rem;
    font-weight: 300;
}

.all-leads-table .search-input {
    border-radius: 39px;
    border: 1px solid #DFDFDF;
    background: #FAFAFA;
}

.all-leads-table .search-icon {
    position: absolute;
    right: 2%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.leads-tabs-navs {
    border-radius: 50px;
    background: #FFF;
    box-shadow: 0 4px 22px 0 rgba(0, 0, 0, 0.10);
    width: fit-content;
}

.leads-tabs-navs.nav-pills .nav-link {
    border-radius: 50px;
    color: #000;
    font-size: 16px;
    font-weight: 400;
    padding: 17px 32px;
}

.leads-tabs-navs.nav-pills .nav-link.active {
    background: #000;
    color: #FFF;
    font-weight: 900;
    transition: ease-in-out all 0.25s;
}

.pieChart-container, .pieChart-arcs-container {
    position: relative;
}

.pie-chart-svg {
    position: absolute;
    top: 0;
    left: 0;
}

.pie-chart-svg circle {
    stroke: none;
}

.labels-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    /*margin-top: 20px;*/
}

.label-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.react-datepicker-wrapper {
    width: 100%;
}

.logs-tooltip-container {
    width: 250px !important;
    max-width: 300px !important;
    z-index: 999;
}

.log-container, .log-container-profile {
    position: relative;
    border-radius: 6px;
    background: #FFF;
    box-shadow: 0 0 32px -5.435px rgba(0, 0, 0, 0.14);
    color: #000;
    text-align: left;
    padding: 2px 8px;
    margin-bottom: 10px;
}

.log-container::after {
    content: url("../../assets/media/li-marker.svg");
    position: absolute;
    top: 50%;
    right: 101%;
    transform: translate(-50%, -50%);
}

.note-dot {
    content: url("../../assets/media/li-marker.svg");
    width: 20px;
    height: 20px;
    position: absolute;
    top: 100%;
    bottom: 0;
    right: 45%;
    transform: translate(-50%, -50%);
}

.logs-list-container {
    padding-left: 15px;
    border-left: 2px solid white;
}

.log-list-container-profile {
    padding-bottom: 20px;
    margin-bottom: 0;
    position: relative;
}

/*.assignment-tab.border-right, .members-tab.border-right, .sales-tab.border-right {*/
/*    border-right: 1px solid black;*/
/*}*/

/*.assignment-tab.border-left, .members-tab.border-left, .sales-tab.border-left {*/
/*    border-left: 1px solid black;*/
/*}*/

.social-icon-container svg{
    max-width: 1.5rem;
    max-height: 1.5rem;
}

.go-back-btn {
    position: absolute;
    left: 5%;
    top: 50%;
    transform: translate(-50%, -50%);
}