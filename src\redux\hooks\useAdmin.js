import { useDispatch, useSelector } from 'react-redux';
import { useMemo } from 'react';
// Import action creators, selectors, and async thunks from the admin slice
import {
    // Action creators
    setClients,
    setTodayClients,
    setTodayLeads,
    setTodayCompletedLeads,
    setIsViewedAs,
    setSelectedClient,
    // Selectors
    selectClients,
    selectTodayClients,
    selectTodayLeads,
    selectTodayCompletedLeads,
    selectIsViewedAs,
    selectSelectedClient,
    // Async thunks
    addClientThunk,
    deleteClientThunk,
    updateClientThunk,
    updateClientStatusThunk,
} from '../features/adminSlice';

/**
 * Custom hook that provides easy access to the admin slice state and actions.
 * This mirrors the API style found in useIntegration for consistency across the app.
 */
const useAdmin = () => {
    const dispatch = useDispatch();

    // ----------- State selectors ----------- //
    const clients = useSelector(selectClients);
    const todayClients = useSelector(selectTodayClients);
    const todayLeads = useSelector(selectTodayLeads);
    const todayCompletedLeads = useSelector(selectTodayCompletedLeads);
    const isViewedAs = useSelector(selectIsViewedAs);
    const selectedClient = useSelector(selectSelectedClient);

    // ----------- Sync action dispatchers ----------- //
    const actions = useMemo(
        () => ({
            setClients: (value) => dispatch(setClients(value)),
            setTodayClients: (value) => dispatch(setTodayClients(value)),
            setTodayLeads: (value) => dispatch(setTodayLeads(value)),
            setTodayCompletedLeads: (value) => dispatch(setTodayCompletedLeads(value)),
            setIsViewedAs: (value) => dispatch(setIsViewedAs(value)),
            setSelectedClient: (value) => dispatch(setSelectedClient(value)),
        }),
        [dispatch]
    );

    // ----------- Async action dispatchers ----------- //
    const asyncActions = useMemo(
        () => ({
            addClient: (values, onHide, resetForm) =>
                dispatch(addClientThunk({ values, onHide, resetForm })),

            deleteClient: (clientId, handleClose) =>
                dispatch(deleteClientThunk({ clientId, handleClose })),

            updateClient: (values, clientData, handleClose) =>
                dispatch(updateClientThunk({ values, clientData, handleClose })),

            updateClientStatus: (clientId, status) =>
                dispatch(updateClientStatusThunk({ clientId, status })),
        }),
        [dispatch]
    );

    // Return API (state + actions)
    return {
        // State
        clients,
        todayClients,
        todayLeads,
        todayCompletedLeads,
        isViewedAs,
        selectedClient,

        // Sync actions
        ...actions,

        // Async actions
        ...asyncActions,

        // Legacy aliases for backward-compatibility (if any old components use them)
        handleAddClient: asyncActions.addClient,
        handleDeleteClient: asyncActions.deleteClient,
        handleUpdateClient: asyncActions.updateClient,
        handleUpdateClientStatus: asyncActions.updateClientStatus,
    };
};

export default useAdmin;

// Also export thunks directly for granular usage if needed
export {
    addClientThunk,
    deleteClientThunk,
    updateClientThunk,
    updateClientStatusThunk,
};
