import { Button } from "react-bootstrap";
import leadService from "../../services/leads";
import { useDispatch, useSelector } from "react-redux";
import {
  setLeads,
  setLeadStatusCounts,
} from "../../redux/features/clientSlice";
import {
  showSuccessToast,
  showErrorToast,
} from "../../utils/toast-success-error";

const DeleteLeadFromTable = ({ leadId, setShowCenteredModal }) => {
  const dispatch = useDispatch();
  const { leads, leadStatusCounts } = useSelector((state) => state.client);

  return (
    <div
      className={"d-flex flex-column justify-content-around align-items-center"}
    >
      <h3 className={"fw-bold"}>Are you sure to delete this Lead?</h3>
      <div className={"d-flex justify-content-center my-5"}>
        <Button
          variant={"secondary"}
          className={"me-2"}
          onClick={() => setShowCenteredModal(false)}
        >
          Cancel
        </Button>
        <Button
          variant={"danger"}
          onClick={() => {
            leadService
              .deleteSingleLeadApi(leadId)
              .then((response) => {
                showSuccessToast(
                  response?.message || "Lead deleted successfully",
                  { position: "bottom-right", theme: "dark" }
                );
                setShowCenteredModal(false);

                // Find the lead to be deleted to get its status
                const leadToDelete = leads.find((lead) => lead.id === leadId);

                // Update leads list
                const updatedLeads = leads.filter((lead) => lead.id !== leadId);
                dispatch(setLeads(updatedLeads));

                // Update lead status counts based on the deleted lead's status
                if (leadToDelete) {
                  const updatedCounts = { ...leadStatusCounts };

                  // Decrement the appropriate counter based on status
                  switch (leadToDelete.status) {
                    case 0:
                      updatedCounts.pendding = Math.max(
                        0,
                        updatedCounts.pendding - 1
                      );
                      break;
                    case 1:
                      updatedCounts.inprogress = Math.max(
                        0,
                        updatedCounts.inprogress - 1
                      );
                      break;
                    case 2:
                      updatedCounts.completed = Math.max(
                        0,
                        updatedCounts.completed - 1
                      );
                      break;
                    case 3:
                      updatedCounts.rejected = Math.max(
                        0,
                        updatedCounts.rejected - 1
                      );
                      break;
                    case 11:
                      updatedCounts.assigned = Math.max(
                        0,
                        updatedCounts.assigned - 1
                      );
                      break;
                    default:
                      break;
                  }

                  dispatch(setLeadStatusCounts(updatedCounts));
                }
              })
              .catch((err) => {
                showErrorToast(
                  err?.response?.data?.message || "Error Deleting lead"
                );
              });
          }}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};

export default DeleteLeadFromTable;
