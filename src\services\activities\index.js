import {cleanData} from "../../utils/clean-data";
import apiRequest from "../../utils/apiRequest";

const createActivityApi = async (activityData) => {
    // Clean the activityData by removing null, undefined, or empty values
    const cleanedData = cleanData(activityData);
    let formData;

    // Check if there are any file fields
    if (cleanedData.quotation_offer || cleanedData.action_proven) {
        formData = new FormData();
        // Append each field from cleanedData to the FormData object
        for (const key in cleanedData) {
            if (Object.prototype.hasOwnProperty.call(cleanedData, key)) {
                formData.append(key, cleanedData[key]);
            }
        }
    }

    // Determine headers and data format
    const dataToSend = formData || cleanedData;
    const headers = {
        "Content-Type": formData ? "multipart/form-data" : "application/json",
    };

    // Send the API request using the reusable helper, passing messages for toast notifications
    return apiRequest(
        "activities",
        "post",
        dataToSend,
        headers,
        null,
        {},
        "Activity created successfully", // Custom success message
        "Failed to create activity" // Custom error message
    );
};

const getAllActivitiesApi = async () => {
    return apiRequest("activities", "get", null);
};

const getLastActivitiesApi = async (data) => {
    return apiRequest("lastActivity", "post", data);
};

const updateActivityApi = async (id, activityData) => {
    // Pass success message directly to apiRequest
    return apiRequest(
        `activity/${id}`,
        "put",
        activityData,
        {},
        null,
        {},
        "Activity updated successfully" // Custom success message
    );
};

export default {createActivityApi, getAllActivitiesApi, getLastActivitiesApi, updateActivityApi};
