import {useFilters, useGlobalFilter, usePagination, useSortBy, useTable} from "react-table";
import {Button, Pagination, Row} from "react-bootstrap";
import {BsFillCaretDownFill} from "react-icons/bs";
import {useMemo, useState} from "react";

const DataTableFollowUpsComponent = ({columns, data}) => {

    const [timeFilter, setTimeFilter] = useState('all');

    const filteredData = useMemo(() => {
        if (timeFilter === 'all') {
            return data;
        } else {
            const currentTime = new Date().getTime();
            return data.filter((item) => {
                const itemTime = new Date(item.time).getTime();
                if (timeFilter === 'due_today') {
                    return itemTime >= currentTime && itemTime < currentTime + 24 * 60 * 60 * 1000 + 1;
                } else if (timeFilter === 'upcoming') {
                    return itemTime > currentTime + 24 * 60 * 60 * 1000;
                } else if (timeFilter === 'overdue') {
                    return itemTime < currentTime;
                } else if (timeFilter === 'someday') {
                    // Customize this condition based on your definition of "someday"
                    return itemTime > currentTime + 7 * 24 * 60 * 60 * 1000;
                }
                return true;
            });
        }
    }, [data, timeFilter]);

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        prepareRow,
        page,
        canPreviousPage,
        canNextPage,
        pageOptions,
        gotoPage,
        nextPage,
        previousPage,
        setPageSize,
        state: { pageIndex, pageSize },
    } = useTable({
        columns, data: filteredData, initialState: { pageIndex: 0, pageSize: 5 },
    }, useFilters, useGlobalFilter, useSortBy, usePagination)

        return (<div className={"all-leads-table"}>
            <Row className={'m-4'}>
                <div className="btn-group follow-up-filter">
                    <Button
                        variant={timeFilter === 'all' ? 'primary' : 'secondary'}
                        onClick={() => setTimeFilter('all')}
                    >
                        All
                    </Button>
                    <Button
                        variant={timeFilter === 'due_today' ? 'primary' : 'secondary'}
                        onClick={() => setTimeFilter('due_today')}
                    >
                        Due Today
                    </Button>
                    <Button
                        variant={timeFilter === 'upcoming' ? 'primary' : 'secondary'}
                        onClick={() => setTimeFilter('upcoming')}
                    >
                        Upcoming
                    </Button>
                    <Button
                        variant={timeFilter === 'overdue' ? 'primary' : 'secondary'}
                        onClick={() => setTimeFilter('overdue')}
                    >
                        Overdue
                    </Button>
                    <Button
                        variant={timeFilter === 'someday' ? 'primary' : 'secondary'}
                        onClick={() => setTimeFilter('someday')}
                    >
                        Due Someday
                    </Button>
                </div>
            </Row>
                <table className="table text-center" {...getTableProps()}>
                    <thead>
                    {headerGroups.map((headerGroup) => (
                        <tr {...headerGroup.getHeaderGroupProps()}>
                            {headerGroup.headers.map((column) => (
                                <th {...column.getHeaderProps(column.getSortByToggleProps())}>
                                    {column.render('Header')}
                                    <span>
                    {column.isSorted ? (
                        column.isSortedDesc ? (
                            ' 🔽'
                        ) : (
                            ' 🔼'
                        )
                    ) : (
                        <>
                            {' '}
                            {column.accessor && <BsFillCaretDownFill />}
                        </>
                    )}
                  </span>
                                </th>
                            ))}
                        </tr>
                    ))}
                    </thead>
                    <tbody {...getTableBodyProps()}>
                    {page.map((row, i) => {
                        prepareRow(row);
                        return (
                            <tr {...row.getRowProps()} key={i} className={'client-table-row'}>
                                {row.cells.map((cell) => (
                                    <td {...cell.getCellProps()}>{cell.render('Cell')}</td>
                                ))}
                            </tr>
                        );
                    })}
                    </tbody>
                </table>
                <div className={'d-flex justify-content-between align-items-center my-4 mx-3'}>
                    <div className={'d-flex justify-content-between align-items-center'}>
                        <div className="me-2">
                            Page{' '}
                            <strong>
                                {pageIndex + 1} of {pageOptions.length}
                            </strong>
                        </div>
                        <div className="page-size-select">
                            <input
                                className="form-control"
                                type="number"
                                defaultValue={pageIndex + 1}
                                onChange={(e) => {
                                    const page = e.target.value ? Number(e.target.value) - 1 : 0;
                                    gotoPage(page);
                                }}
                                style={{ width: '100px', height: '20px' }}
                            />
                        </div>
                    </div>
                    <Pagination className={'data-table-pagination'}>
                        <Pagination.Prev onClick={() => previousPage()} disabled={!canPreviousPage} />
                        {Array.from({ length: pageOptions.length }).map((_, index) => {
                            if (
                                pageOptions.length <= 5 ||
                                index === 0 ||
                                index === pageOptions.length - 1 ||
                                (index >= pageIndex - 2 && index <= pageIndex + 2)
                            ) {
                                return (
                                    <Pagination.Item
                                        key={index}
                                        onClick={() => gotoPage(index)}
                                        active={pageIndex === index}
                                    >
                                        {index + 1}
                                    </Pagination.Item>
                                );
                            } else if (index === 1 || index === pageOptions.length - 2) {
                                return <Pagination.Ellipsis key={index} />;
                            }
                            return null;
                        })}
                        <Pagination.Next onClick={() => nextPage()} disabled={!canNextPage} />
                    </Pagination>
                    <div className={'d-flex justify-content-between align-items-center'}>
                        <div className={'me-2'}>Records Per Page:</div>
                        <select
                            className="form-control"
                            value={pageSize}
                            onChange={(e) => {
                                setPageSize(Number(e.target.value));
                            }}
                            style={{ width: '120px', height: '38px' }}
                        >
                            {[1, 5, 10, 20, 30, 40, 50].map((pageSize) => (
                                <option key={pageSize} value={pageSize}>
                                    Show {pageSize}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>
        );
};

export default DataTableFollowUpsComponent;
