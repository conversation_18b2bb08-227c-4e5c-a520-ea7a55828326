import React from "react";
import { Form } from "react-bootstrap";

const PriorityDropdown = ({
  ticketId,
  currentPriority,
  onPriorityChange,
  isUpdating,
}) => {
  return (
    <Form.Group>
      <div className="position-relative">
        <Form.Select
          size="sm"
          value={currentPriority}
          onChange={(e) => {
            onPriorityChange(ticketId, e.target.value);
          }}
          disabled={isUpdating}
        >
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </Form.Select>

        {isUpdating && (
          <div className="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-light bg-opacity-75">
            <div
              className="spinner-border spinner-border-sm text-dark"
              role="status"
            >
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        )}
      </div>
    </Form.Group>
  );
};

export default React.memo(PriorityDropdown);
