import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import clientService from '../../services/clients';
import { showSuccessToast } from "../../utils/toast-success-error";
import { toast } from "react-toastify";

// Async Thunks
export const addClientThunk = createAsyncThunk(
  'admin/addClient',
  async ({ values, onHide, resetForm }) => {
    try {
      const { confirmPassword, ...clientData } = values;
      const result = await clientService.addClientApi(clientData);
      showSuccessToast('Client Created Successfully!');
      resetForm();
      onHide();
      return result.data.user;
    } catch (error) {
      toast.error(error?.response?.data?.message, { position: "bottom-right", theme: "dark" });
      throw error;
    }
  }
);

export const deleteClientThunk = createAsyncThunk(
  'admin/deleteClient',
  async ({ clientId, handleClose }) => {
    try {
      await clientService.deleteClientApi(clientId);
      // Make sure handleClose is called here if it exists
      if (handleClose) {
        handleClose();
      }
      return clientId;
    } catch (error) {
      throw error;
    }
  }
);

export const updateClientThunk = createAsyncThunk(
  'admin/updateClient',
  async ({ values, clientData, handleClose }, { rejectWithValue }) => {
    if (!clientData) {
      handleClose && handleClose();
      return null;
    }

    // Check for changes in name, email, and phone
    const nameChanged = values.name !== clientData.name;
    const emailChanged = values.email !== clientData.email;
    const phoneChanged = values.phone !== clientData.phone;

    // Check if any of the fields have changed
    if (!nameChanged && !emailChanged && !phoneChanged) {
      toast.info("No changes detected.", { position: "bottom-right", theme: "dark" });
      return null;
    }

    try {
      await clientService.editClientApi(clientData.id, values);
      handleClose && handleClose();
      return { id: clientData.id, values };
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateClientStatusThunk = createAsyncThunk(
  'admin/updateClientStatus',
  async ({ clientId, status }) => {
    try {
      await clientService.editClientApi(clientId, { status });
      return { clientId, status };
    } catch (error) {
      throw error;
    }
  }
);

// Slice
const adminSlice = createSlice({
  name: 'admin',
  initialState: {
    clients: [],
    todayClients: 0,
    todayLeads: 0,
    todayCompletedLeads: 0,
    isViewedAs: false,
    selectedClient: null,
  },
  reducers: {
    setClients: (state, action) => {
      state.clients = action.payload;
    },
    setTodayClients: (state, action) => {
      state.todayClients = action.payload;
    },
    setTodayLeads: (state, action) => {
      state.todayLeads = action.payload;
    },
    setTodayCompletedLeads: (state, action) => {
      state.todayCompletedLeads = action.payload;
    },
    setIsViewedAs: (state, action) => {
      state.isViewedAs = action.payload;
    },
    setSelectedClient: (state, action) => {
      state.selectedClient = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addClientThunk.fulfilled, (state, action) => {
        state.clients = [action.payload, ...state.clients];
        state.todayClients += 1;
      })
      .addCase(deleteClientThunk.fulfilled, (state, action) => {
        state.clients = state.clients.map(client =>
          client.id === action.payload.clientId ? { ...client, status: 'deactivated' } : client
        );
      })
      .addCase(updateClientThunk.fulfilled, (state, action) => {
        if (action.payload) {
          state.clients = state.clients.map(client => {
            if (client.id === action.payload.id) {
              return {
                ...client,
                name: action.payload.values.name,
                email: action.payload.values.email,
                phone: action.payload.values.phone
              };
            }
            return client;
          });
        }
      })
      .addCase(updateClientStatusThunk.fulfilled, (state, action) => {
        state.clients = state.clients.map(client => {
          if (client.id === action.payload.clientId) {
            return {
              ...client,
              status: action.payload.status
            };
          }
          return client;
        });
      });
  }
});

// Action creators
export const {
  setClients,
  setTodayClients,
  setTodayLeads,
  setTodayCompletedLeads,
  setIsViewedAs,
  setSelectedClient,
} = adminSlice.actions;

// Selector functions to replace useAdmin hook
export const selectClients = (state) => state.admin.clients;
export const selectTodayClients = (state) => state.admin.todayClients;
export const selectTodayLeads = (state) => state.admin.todayLeads;
export const selectTodayCompletedLeads = (state) => state.admin.todayCompletedLeads;
export const selectIsViewedAs = (state) => state.admin.isViewedAs;
export const selectSelectedClient = (state) => state.admin.selectedClient;

// Helper functions to replace the context methods
export const handleAddClient = (values, onHide, resetForm) => {
  return addClientThunk({ values, onHide, resetForm });
};

export const handleDeleteClient = (clientId, handleClose) => {
  return deleteClientThunk({ clientId, handleClose });
};

export const handleUpdateClient = (values, clientData, handleClose) => {
  return updateClientThunk({ values, clientData, handleClose });
};

export const handleUpdateClientStatus = ({ clientId, status }) => {
  return updateClientStatusThunk({ clientId, status });
};

export default adminSlice.reducer;
