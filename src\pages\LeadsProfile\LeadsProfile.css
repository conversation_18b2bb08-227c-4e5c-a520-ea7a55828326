.profile-icon-container {
    background-color: #FFFFFF;
    border-radius: 10px;
    box-shadow: rgba(100, 100, 111, 0.2) 0 7px 29px 0;
    padding: 10px;
    margin: 10px 0;
}

.profile-icon-container:hover {
    background-color: #CAEC76;
    cursor: pointer;
    transition: ease-in-out background-color 0.25s;
}

.profile-more-icon svg {
    border-radius: 14px;
    background: linear-gradient(265deg, #92C020 -3.29%, #CAD511 137.18%);
    box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.10);
}

.accordion-container {
    border-radius: 14px;
    border: 1px solid #92C020;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
}

.accordion-container .accordion-body {
    background: transparent !important;
}

.accordion-container .accordion-item {
    background-color: unset;
}

.accordion-container .accordion-item .accordion-button{
    background-color: transparent;
    border-radius: 14px 14px 0 0;
    color: #92C020;
    justify-content: space-between;
}

.accordion-collapse.collapse {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
}

.accordion-item:last-of-type .accordion-button.collapsed {
    border-radius: 40px;
}

.accordion-container .accordion-item .accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2392C020'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    margin-left: 10px;
}

.accordion-container .accordion-collapse.collapse {
    border: unset;
    background: unset;
}

.accordion-container .accordion-item {
    border: unset;
}

.accordion-container .contact-accordion .accordion-collapse.collapse.show {
    border: 2px solid #FFF;
    background: #FFF;
}

.accordion-container .accordion-button {
    border-radius: 14px;
}

.edit-profile-icon {
    background-color: #92C020;
    border-radius: 50%;
    min-width: 20px;
}

.edit-profile-icon svg {
    padding: 1px;
}

.user-profile-tabs {
    border-radius: 40px;
    background: #FFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.10);
    width: fit-content;
}

.user-profile-tabs.nav-pills .nav-link.active {
    border-radius: 40px;
    background: linear-gradient(265deg, #92C020 -3.29%, #CAD511 137.18%);
    color: #FFF;
    font-weight: 700;
    transition: ease-in-out all 0.25s;
}

.user-profile-tabs.nav-pills .nav-link {
    border-radius: 40px;
    background: #FFF;
    color: #000;
    font-weight: 400;
}

.overview-details-row {
    justify-content: space-evenly;
    align-items: center;
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    padding: 40px 20px;
    margin: 0 10px 20px;
    text-align: center;
}

.communications-row {
    border-radius: 14px;
    border: 2px solid #FFF;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    padding: 20px;
    margin: 20px 10px;
}

.communications-row img{
    width: 120px;
    height: 90px;
}

.activities-nav-tabs.nav-underline .nav-link.active {
    border-bottom: 3px solid #92C020;
    font-weight: 700;
}

.activities-nav-tabs.nav-underline {
    width: fit-content;
    justify-content: center;
    margin: 0 auto;
    border-bottom: 1px solid rgba(175, 175, 175, 0.50);
    gap: 30px;
}

.activities-nav-tabs.nav-underline .nav-link {
    color: #000;
    font-weight: 400;
}

.profile-delete-icon {
    border-right: 1px solid rgba(0, 0, 0, 0.21);
}

.profile-delete-icon svg {
    fill: #E35757;
}

.accordion-divider .accordion-button {
    border-top: 1px solid #92C020;
    border-bottom: 1px solid #92C020;
}

.assign-client-icon {
    padding: 5px;
    margin: 0 auto 9px;
    border-radius: 10px;
    border: 1px solid #97C21F;
    background: #FFF;
    box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.10);
    cursor: pointer;
    width: fit-content;
}

.about-contact {
    border-radius: 14px;
    border: 1px solid #92C020;
    background: #FFF;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    padding: 1rem;
    font-size: 0.8rem;
}

.groups-collapse-btn {
    border-radius: 14px;
    border: 1px solid #92C020;
    background: #92C020;
    box-shadow: 0 4px 60px -7px rgba(0, 0, 0, 0.10);
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    padding: 10px;
}

.green-checkbox input {
    border: 1px solid #92BF20;
}

.filter-table-rows a:hover:not(.activity-button) {
    color: #92C020;
}

.admin-theme .overview-details-row, .admin-theme .about-contact {
    border: 1px solid #444;
    background: #242424;
    color: white;
}

.single-quotation:hover {
    color: #92C020;
}

.completed-status {
    border-radius: 3px;
    background: rgba(58, 201, 119, 0.10);
    color: #3AC977;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.on-hold-status {
    border-radius: 3px;
    background: rgba(191, 137, 0, 0.21);
    color: #E4AB15;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.not-started-status {
    border-radius: 3px;
    background: rgba(255, 94, 94, 0.15);
    color: #FF5E5E;
    text-align: center;
    font-family: Poppins, serif;
    font-size: 0.8rem;
    font-style: normal;
    font-weight: 500;
    padding: 5px;
}

.overlay-quotation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    border-radius: 14px;
}

.single-quotation:hover .overlay-quotation {
    opacity: 1;
    visibility: visible;
    backdrop-filter: blur(5px);
}

.single-quotation:hover li {
    list-style-type: none;
}

.eye-icon {
    color: #fff;
    font-size: 2rem;
}

.activity-container-profile {
    height: 60%;
    min-height: 130px;
    cursor: pointer;
}

.activity-container-profile:hover .overlay-activity {
    opacity: 1;
    visibility: visible;
    backdrop-filter: blur(5px);
}

.overlay-activity {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    border-radius: 14px;
}

.call-icons {
    position: absolute;
    top: 70%;
    right: -5%;
    transform: translate(-50%, -50%);
}

[dir = "rtl"] .call-icons {
    right: 0;
    left: 35%;
}

.mail-icon {
    position: absolute;
    top: 70%;
    right: 0;
    transform: translate(-50%, -50%);
}

[dir=rtl] .about-contact [type=email], [dir=rtl] .about-contact [type=number], [dir=rtl] .about-contact [type=tel], [dir=rtl] .about-contact [type=url] {
    direction: rtl;
}
