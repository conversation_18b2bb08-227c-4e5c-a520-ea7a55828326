import Lottie from "lottie-react";
import sendMail from "../../assets/media/animations/send-mail.json";
import {Button} from "react-bootstrap";
import {Link} from "react-router-dom";
const ResendEmailPage = () => {

    return (
        <div className={"user-auth-containers"}>
            <div className={"user-auth-form"}>
                <Lottie animationData={sendMail} style={{ width: 100, height: 100 }} />
                <div className={"form-title"}>
                    Reset email sent
                </div>
                <div className={"form-description my-5 text-center"}>
                    We have just sent an email with a password
                    reset link to <strong><EMAIL></strong>
                </div>
                <div className={"d-flex justify-content-between"}>
                    <Link to={"/"}>
                        <div className={"submit-btn me-4"}>
                            Got it
                        </div>
                    </Link>
                    <Button variant={"outline-success rounded-pill"}>
                        Send again
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ResendEmailPage;