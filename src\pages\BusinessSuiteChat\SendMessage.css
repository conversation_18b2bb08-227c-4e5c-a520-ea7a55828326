.send-message {
  display: flex;
  gap: 10px;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  align-items: center;
  position: relative;
}

.input-container {
  position: relative;
  background-color: #f8f9fa;
  border-radius: 24px;
  padding: 8px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.input-container:focus-within {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.form-input__input {
  border: none;
  background: transparent;
  outline: none;
  font-size: 15px;
}

/* Enhanced Audio Button Styles */
.audio-record-btn, .audio-stop-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: transparent;
  position: relative;
  z-index: 1;
}

.audio-record-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
  z-index: -1;
}

.audio-record-btn:hover::before {
  transform: scale(1);
}

.audio-record-btn:hover {
  transform: scale(1.05);
}

.audio-stop-btn {
  background-color: rgba(220, 53, 69, 0.15);
  animation: pulse 1.5s infinite;
}

.audio-stop-btn:hover {
  background-color: rgba(220, 53, 69, 0.25);
}

.attachment-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.attachment-label:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.send-btn {
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 0 22px;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 80px;
  box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
}

.send-btn:hover:not(:disabled) {
  background-color: #0b5ed7;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.4);
}

.send-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

.send-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

.sending-message {
  padding: 10px 20px;
  color: #6c757d;
  font-style: italic;
}

.attachment-preview-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.attachment-preview {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  max-height: 150px;
  object-fit: contain;
  border-radius: 8px;
}

.file-info {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  word-break: break-all;
}

.audio-preview {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 250px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
}

.audio-player {
  width: 100%;
  height: 32px;
}

.remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.remove-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Send icon button styles */
.send-icon-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: transparent;
  position: relative;
  z-index: 1;
  cursor: pointer;
}

.send-icon-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(13, 110, 253, 0.1);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
  z-index: -1;
}

.send-icon-btn:hover::before {
  transform: scale(1);
}

.send-icon-btn:hover {
  transform: scale(1.05);
}

.send-icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-icon-btn:disabled:hover {
  transform: none;
}

.send-icon-btn:disabled::before {
  transform: scale(0);
}

/* Add these styles to your existing CSS */

.emoji-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s;
  margin: 0 5px;
}

.emoji-btn:hover {
  color: #0d6efd;
}

.emoji-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

.emoji-picker-container {
  position: absolute;
  bottom: 70px;
  right: 70px;
  z-index: 1000;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .emoji-picker-container {
    right: 10px;
    width: 280px;
  }
}

/* Sticker styles */
.sticker-img {
  object-fit: contain !important;
  background-color: transparent !important;
  max-width: 150px;
  max-height: 150px;
  width: auto;
  height: auto;
  border-radius: 8px;
}

.sticker-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.2s;
}

.sticker-btn:hover {
  color: #0d6efd;
}

.sticker-picker-container {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  z-index: 1000;
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
}

.sticker-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.sticker-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sticker-item:hover {
  transform: scale(1.05);
  background-color: #f8f9fa;
}

.sticker-item img {
  width: 100%;
  height: auto;
  max-width: 80px;
  max-height: 80px;
  object-fit: contain;
}

/* Sticker preview styles */
.sticker-preview-container {
  width: 100%;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.sticker-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 5px;
  border-radius: 8px;
  background-color: white;
}

.sticker-preview-image {
  max-width: 120px;
  max-height: 120px;
  object-fit: contain;
  border-radius: 8px;
}

.sticker-preview .remove-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 5;
}
