.password-input-container {
    position: relative;
    width: 100%;
}

.password-input-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #00000033;
    /* Add z-index to ensure it's above the input but below validation icon */
    z-index: 2;
}

.password-input-icon-TM {
    position: absolute;
    right: 0;
    top: 75%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    color: #00000033;
}

.gray-label {
    color: rgba(0, 0, 0, 0.50);
    font-size: 1rem;
    font-weight: 400;
}

.green-label {
    color: #80AA17;
    font-size: 1rem;
    font-weight: 400;
}

.google-auth-btn {
    border-radius: 30px;
    border: 2px solid #BFBFBF;
    background-color: #FFF;
    padding: 0.5rem 1rem;
    margin-top: 1rem;
}

.social-submit-button {
    display: contents;
}


.social-icon-sign {
    border: 1px solid rgba(0, 0, 0, 0.30);
    border-radius: 50%;
    padding: 10px;
    cursor: pointer;
}

.user-auth-containers {
    width: 100vw;
    height: 100vh;
    background-color: #FFFFFF;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.user-auth-form {
    width: 50%;
    height: 100%;
    margin: 0 auto;
    animation: fadeIn 0.6s ease-in-out;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-content: center;
}

.form-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
}

.form-description {
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 500;
}

.mainBackGround {
    background: linear-gradient(263deg, #92C020 -9.91%, #CAD511 128.31%);
    color: white;
}

.auth-form-input {
    background-color: transparent;
    padding: 12px 15px;
    margin: 8px 0;
    width: 100%;
    border-radius: 7px;
    border: 2px solid rgba(0, 0, 0, 0.20);
}

.signUpForm, .signInForm {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: 70%;
    width: 70%;
    @media (max-width: 1280px) {
        width: 90%;
        height: 85%;
    }
}

.signInForm input.form-control, .signUpForm input.form-control {
    background-color: transparent;
    /*padding: 12px 15px;*/
    margin: 8px 0;
    width: 100%;
    border-radius: 7px;
    border: 2px solid rgba(0, 0, 0, 0.20);
}

.password-input-container:has(input.is-valid) .password-input-icon-TM {
    top: 75%;
}

.password-input-container:has(input.is-invalid) .password-input-icon-TM {
    top: 55%;
}

.password-input-container:has(input.is-invalid) .password-input-icon {
    top: 28%;
}

.password-input-container .form-control.is-valid, .was-validated .form-control:valid, .password-input-container .form-control.is-invalid, .was-validated .form-control:invalid {
    background-image: unset;
}

.password-input-container:has(input.is-valid) .password-input-icon .password-toggle-icon {
    color: #80AA17;
}

.password-input-container:has(input.is-invalid) .password-input-icon .password-toggle-icon {
    color: #F44336;
}

.mobile-title {
    background: linear-gradient(180.00deg, rgb(146, 192, 32),rgb(202, 213, 17));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 0;
}

.input-icon-container {
    position: relative;
}

.input-with-icon .form-control {
    padding-left: 2.5rem;
}

.input-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(0, 0, 0, 0.6);
}

.input-icon-container:has(.form-control.is-invalid) .input-icon {
    top: 30%;
}

.overlay-container,
.overlay-container *,
[dir="rtl"] .overlay-container,
[dir="rtl"] .overlay-container * {
    direction: ltr !important;
}

.input-icon-rtl {
  left: auto;
  right: 10px;
}

.password-icon-rtl {
  right: auto;
  left: 20px;
}

/* For RTL text alignment in all form elements */
[dir="rtl"] .form-control {
  text-align: right;
}

.PhoneInputInput::placeholder {
    opacity: 1 !important;
}

[dir="rtl"] .PhoneInputInput {
    padding-right: 3.5rem !important;
    color: black;
    text-align: right;
}

[dir="rtl"] .form-check-label {
  /* padding-right: 1.5em; */
  padding-left: 0;
}

[dir="rtl"] .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.5em;
}

/* Fix PhoneInput component for RTL */
[dir="rtl"] .PhoneInputCountry {
  margin-right: 0;
  margin-left: 0.5em;
  left: unset;
  right: -2%;
}

[dir="ltr"] .form-control.is-invalid, .was-validated .form-control:invalid {
    border-color: var(--bs-form-invalid-border-color);
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 12 12%27 width=%2712%27 height=%2712%27 fill=%27none%27 stroke=%27%23dc3545%27%3e%3ccircle cx=%276%27 cy=%276%27 r=%274.5%27/%3e%3cpath stroke-linejoin=%27round%27 d=%27M5.8 3.6h.4L6 6.5z%27/%3e%3ccircle cx=%276%27 cy=%278.2%27 r=%27.6%27 fill=%27%23dc3545%27 stroke=%27none%27/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(.375em + .1875rem) center;
    background-size: calc(.75em + .375rem) calc(.75em + .375rem);
  }


.PhoneInputCountrySelectArrow {
    margin: 0 6px;
    width: 0.5rem !important;
    height: 0.5rem !important;
opacity: 1 !important;
}

/* Update existing password input container styles */
.password-input-container {
    position: relative;
    width: 100%;
}

/* Update eye icon positioning */
.password-input-icon {
    position: absolute;
    right: 10px;
    /*top: 50%;*/
    transform: translateY(-50%);
    cursor: pointer;
    color: #00000033;
    /* Add z-index to ensure it's above the input but below validation icon */
    z-index: 2;
}

/* Position the validation icon */
.password-input-container .form-control.is-invalid {
    background-position: right calc(.375em + .1875rem + 35px) center !important; /* Move validation icon further right */
}

/* RTL support */
[dir="rtl"] .password-input-icon {
    right: auto;
    left: 10px;
}

[dir="rtl"] .password-input-container .form-control.is-invalid {
    background-position: left calc(.375em + .1875rem + 35px) center !important;
}

/* Ensure validation message doesn't overlap */
.password-input-container .invalid-feedback {
    margin-top: 0.25rem;
    display: block;
}

/* Update icon states for validation */
.password-input-container:has(input.is-invalid) .password-input-icon {
    /*top: 50%;*/
    transform: translateY(-50%);
}

/* Optional: Style adjustments for the eye icon */
.password-toggle-icon {
    opacity: 0.6;
    transition: opacity 0.2s;
}

.password-toggle-icon:hover {
    opacity: 1;
}
