import { toast } from "react-toastify";
import { useIsMobile } from "./useIsMobile";

// Create a function to get the toast position based on screen size
const getToastPosition = () => {
    return window.innerWidth <= 576 ? "top-center" : "bottom-right";
};

// Utility to handle success toast
const showSuccessToast = (message) => {
    toast.success(message, { 
        position: getToastPosition(),
        theme: "dark" 
    });
};

// Utility to handle error toast
const showErrorToast = (message) => {
    toast.error(message, { 
        position: getToastPosition(),
        theme: "dark" 
    });
};

export { showSuccessToast, showErrorToast };
