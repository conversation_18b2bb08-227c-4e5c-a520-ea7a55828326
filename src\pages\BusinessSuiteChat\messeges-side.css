
.arrow-container {
    display: flex;
    justify-content: center;
    align-items: start;
    height: 3rem;
    width: fit-content;
    margin: 0 auto;
}

.arrow-down {
    animation: bounce 2s infinite;
    background-color: #92C020;
    color: white;
    border-radius: 50%;
    padding: 5px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(10px);
    }
    60% {
        transform: translateY(5px);
    }
}

.tooltip-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.tooltip-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tooltip-content p {
    margin-bottom: 10px;
}

.tooltip-content button {
    padding: 5px 10px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

.tooltip-content button:hover {
    background: #0056b3;
}