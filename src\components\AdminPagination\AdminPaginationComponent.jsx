const AdminPaginationComponent = ({
  currentPage,
  totalPages,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 20, 30, 40, 50]
}) => {
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange(newPage);
    }
  };

  const handleItemsPerPageChange = (event) => {
    const newSize = Number(event.target.value);
    onItemsPerPageChange(newSize);
  };

  return (
    <div className="d-flex justify-content-between align-items-center mt-3 px-4 py-5">
      <div>
        <select
          className="form-select bg-dark text-light border-success"
          value={itemsPerPage}
          onChange={handleItemsPerPageChange}
        >
          {itemsPerPageOptions.map(size => (
            <option key={size} value={size}>
              {size} per page
            </option>
          ))}
        </select>
      </div>

      <div>
        <ul className="pagination mb-0">
          <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
            <button
              className="page-link bg-dark text-light border-success"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
          </li>

          {[...Array(totalPages)].map((_, index) => {
            const pageNumber = index + 1;
            if (
              totalPages <= 5 ||
              pageNumber === 1 ||
              pageNumber === totalPages ||
              (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
            ) {
              return (
                <li
                  key={pageNumber}
                  className={`page-item ${currentPage === pageNumber ? 'active' : ''}`}
                >
                  <button
                    className={`page-link ${
                      currentPage === pageNumber ? 'bg-success' : 'bg-dark'
                    } text-light border-success`}
                    onClick={() => handlePageChange(pageNumber)}
                  >
                    {pageNumber}
                  </button>
                </li>
              );
            } else if (
              (pageNumber === 2 && currentPage > 4) ||
              (pageNumber === totalPages - 1 && currentPage < totalPages - 3)
            ) {
              return (
                <li key={pageNumber} className="page-item">
                  <button className="page-link bg-dark text-light border-success">
                    ...
                  </button>
                </li>
              );
            }
            return null;
          })}

          <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
            <button
              className="page-link bg-dark text-light border-success"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </li>
        </ul>
      </div>

      <div className="text-light">
        Page {currentPage} of {totalPages}
      </div>
    </div>
  );
};

export default AdminPaginationComponent;
