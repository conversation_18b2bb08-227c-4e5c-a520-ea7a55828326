import HeaderComponent from "./Header/Header.component";
import SidebarComponent from "./Sidebar/Sidebar.component";
import { Outlet } from "react-router-dom";
import CenteredModal from "../Shared/modals/CenteredModal/CenteredModal";
import ImportModalContent from "../../pages/Integrations/IntegratedPlatform/ImportModalContent";
import { useState, useEffect } from "react";
import { useIsMobile } from "../../utils/useIsMobile";
import ExpiredSessionModal from "../Modals/ExpiredSessionModal";
import { useSelector, useDispatch } from "react-redux";
import useAuth from "../../redux/hooks/useAuth";
import {
  setChatModalVisibility,
  selectShowChatModal,
  setupGlobalChatListener,
} from "../../redux/features/supportChatSlice";
import { setupGlobalAdminChatListener } from "../../redux/features/adminSupportChatSlice";
import FloatingChatWidget from "../../pages/Support/components/FloatingChatWidget";
import SupportChatModal from "../../pages/Support/components/SupportChatModal";
import useIntegration from "../../hooks/useIntegration";

const Layout = () => {
  const dispatch = useDispatch();
  const {
    user,
    userFlag,
    setShowExpiredSessionModal,
    showExpiredSessionModal,
    logout,
  } = useAuth();

  // Set up global chat listeners when the app starts
  useEffect(() => {
    if (user?.user?.id) {
      if (userFlag === "admin") {
        // For admin users, set up listener for all client chats
        dispatch(setupGlobalAdminChatListener());
      } else {
        // For regular users, set up listener for their own chat
        dispatch(setupGlobalChatListener(user.user.id)).then((result) => {
          if (result.payload) {
            window.__userChatUnsubscribe = result.payload;
          }
        });
      }
    }

    // Clean up the listener when component unmounts
    return () => {
      if (typeof window.__adminChatUnsubscribe === "function") {
        window.__adminChatUnsubscribe();
      }
      if (typeof window.__userChatUnsubscribe === "function") {
        window.__userChatUnsubscribe();
      }
    };
  }, [dispatch, user?.user?.id, userFlag]);

  const { showIntegrationModal, setShowIntegrationModal } = useIntegration();
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = useState(!isMobile);
  const iconSize = isMobile ? 22 : 20;
  const showChatModal = useSelector(selectShowChatModal);

  return (
    <>
      <HeaderComponent />
      <div
        className={"d-flex justify-content-center justify-content-md-between"}
      >
        <SidebarComponent
          isExpanded={isExpanded}
          mobileSidebar={isMobile}
          setIsExpanded={setIsExpanded}
          iconSize={iconSize}
          onChatClick={() => dispatch(setChatModalVisibility(true))}
        />
        <main className={"mb-5 container"} style={{ marginTop: "1rem" }}>
          <Outlet />
        </main>
        <FloatingChatWidget />
      </div>

      {/* Support Chat Modal - now it's not wrapped in a Modal component */}
      <SupportChatModal
        show={showChatModal}
        onClose={() => dispatch(setChatModalVisibility(false))}
      />

      <CenteredModal
        show={showIntegrationModal}
        children={
          <ImportModalContent
            handleClose={() => setShowIntegrationModal(false)}
          />
        }
        onHide={() => setShowIntegrationModal(false)}
      />
      <CenteredModal
        show={showExpiredSessionModal}
        children={
          <ExpiredSessionModal
            handleClose={() => {
              setShowExpiredSessionModal(false);
              logout();
            }}
          />
        }
        onHide={() => {
          setShowExpiredSessionModal(false);
          logout();
        }}
      />
    </>
  );
};

export default Layout;
