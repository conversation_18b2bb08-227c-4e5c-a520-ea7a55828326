import { BigIntUtil } from "./BigIntUtil";

/**
 * normalizeId converts an id (number or string) into a canonical string form.
 * If the id is already a number and unsafe, a warning is printed—but the conversion still happens.
 */
export const normalizeId = (id) => {
    try {
        // Create a BigIntUtil instance and get its string value
        return new BigIntUtil(id).toString();
    } catch (error) {
        console.error("normalizeId error: ", error);
        return id?.toString();
    }
};