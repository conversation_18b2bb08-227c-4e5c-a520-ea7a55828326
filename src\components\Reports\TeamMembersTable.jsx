import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { Button, Col, Form, Row, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { FaCalendarAlt } from "react-icons/fa";
import { TiArrowForward } from "react-icons/ti";
import DatePicker from "react-datepicker";
import { toast } from "react-toastify";
import { getTeamMembersFilterTableAPI } from "../../services/reports/get-leads-reports.api";
import { useTranslatedColumns } from "./ColumnsForTables.module";
import PaginationRecordsForReports from "./PaginationRecordsForReports";
import { ExportTeamMembersReportsApi } from "../../services/reports/export-reports.api";
import { useTranslation } from "react-i18next";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
import { setRoles } from "../../redux/features/roleSlice";
import {
  setTeamMembersFilters,
  clearTeamMembersFilters,
} from "../../redux/features/reportsSlice";

const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split(".")[0];
};

function GlobalFilter({
  setGlobalFilter,
  fetchInitialData,
  recordsPerPage,
  currentPage,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  selectedTM,
  setSelectedTM,
  role,
  setRole,
  activeTab,
}) {
  const { teamMembers } = useSelector((state) => state.client);
  const dispatchRedux = useDispatch();
  const activeFiltersRef = useRef({
    activeRoleTeam: null,
    activeTMTeam: null,
    activeFromDateTeam: null,
    activeToDateTeam: null,
  });

  const setActiveStatesForExport = () => {
    activeFiltersRef.current = {
      activeRoleTeam: role,
      activeTMTeam: selectedTM?.id,
      activeFromDateTeam: startDate,
      activeToDateTeam: endDate,
    };
  };

  const handleExport = async () => {
    setActiveStatesForExport();
    const params = {
      from: activeFiltersRef.current.activeFromDateTeam,
      to: activeFiltersRef.current.activeToDateTeam,
      member: activeFiltersRef.current.activeTMTeam,
      role: activeFiltersRef.current.activeRoleTeam,
      page: currentPage,
      number_of_records: recordsPerPage,
    };
    await ExportTeamMembersReportsApi(params);
  };

  const applyFilters = async () => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedTM?.id,
      role,
      recordsPerPage,
      currentPage,
    };
    await fetchInitialData(params);

    dispatchRedux(
      setTeamMembersFilters({
        startDate: startDate ? startDate.toISOString() : null,
        endDate: endDate ? endDate.toISOString() : null,
        selectedTM,
        role,
      })
    );
  };

  const clearFilters = () => {
    setGlobalFilter("");
    setStartDate(null);
    setEndDate(null);
    setSelectedTM(null);
    setRole(null);
    fetchInitialData({ currentPage, recordsPerPage });
    dispatchRedux(clearTeamMembersFilters());
    activeFiltersRef.current = {
      activeRoleTeam: null,
      activeTMTeam: null,
      activeFromDateTeam: null,
      activeToDateTeam: null,
    };
  };
  const [filteredTeamMembers, setFilteredTeamMembers] = useState([]);
  useEffect(() => {
    if (teamMembers?.length === 0) {
      return;
    }
    if (role) {
      setFilteredTeamMembers(
        teamMembers.filter(
          (teamMember) => Number(teamMember.role) === Number(role)
        )
      );
    } else {
      setFilteredTeamMembers(teamMembers);
    }
  }, [teamMembers, role]);
  const { roles } = useSelector((state) => state.role);
  const { t } = useTranslation();
  return (
    <Row className={"align-items-center justify-content-evenly"}>
      <Col lg={2} sm={6} className={"my-2"}>
        <select
          value={role || ""}
          onChange={(e) => setRole(e.target.value)}
          className="form-select rounded-pill"
          aria-label="Default select example"
        >
          <option value="">{t("teamMembers.selectRole")}</option>
          {roles?.length > 0
            ? roles?.map((role) => (
                <option key={role?.id} value={role?.id}>
                  {role?.show_name}
                </option>
              ))
            : null}
        </select>
      </Col>
      <Col lg={2} sm={6} className={"my-2"}>
        <Form.Select
          value={selectedTM?.id || null}
          className={"rounded-pill"}
          onChange={(e) => setSelectedTM({ id: e.target.value })}
        >
          <option value="">{t("teamMembers.selectTeamMember")}</option>
          {filteredTeamMembers?.length > 0
            ? filteredTeamMembers?.map((teamMember) => (
                <option key={teamMember?.id} value={teamMember?.id}>
                  {teamMember?.name}
                </option>
              ))
            : null}
        </Form.Select>
      </Col>
      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={startDate}
          selected={startDate || null}
          onChange={(date) => setStartDate(date)}
          selectsStart
          startDate={startDate || null}
          endDate={endDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("teamMembers.filters.from")}
                defaultValue={
                  startDate ? new Date(startDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={endDate}
          selected={endDate}
          onChange={(date) => setEndDate(date)}
          selectsEnd
          startDate={startDate || null}
          endDate={endDate || null}
          minDate={startDate}
          placeholderText={t("teamMembers.filters.to")}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("teamMembers.filters.to")}
                defaultValue={
                  endDate ? new Date(endDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col
        lg={4}
        className={"d-flex justify-content-center justify-content-lg-end my-2"}
      >
        <Button onClick={applyFilters} className="px-3 py-2 apply-btn">
          {t("teamMembers.filters.apply")}
        </Button>
        <Button
          onClick={clearFilters}
          className="rounded-pill clear-btn px-3 mx-2"
        >
          {t("teamMembers.filters.clear")}
        </Button>
        <div
          className={
            "d-flex justify-content-between align-items-center rounded-pill bg-dark text-white px-3 py-1 fs-6"
          }
          role={"button"}
          onClick={handleExport}
        >
          <TiArrowForward />
          <div>{t("teamMembers.filters.export")}</div>
        </div>
      </Col>
    </Row>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const { t } = useTranslation();
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={t("teamMembers.searchRecords", { count })}
    />
  );
}

const TeamMembersTable = ({
  activeTab,
  selectedTM,
  startDate,
  endDate,
  setStartDate,
  setEndDate,
  setSelectedTM,
  setShowStatistics,
  role,
  setRole,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsToDisplay, setRecordsToDisplay] = useState(10); // Default value
  const fetchMembersAbortController = useRef(null);
  const restoredDone = useRef(false);
  const dispatch = useDispatch();

  const fetchInitialData = async (params) => {
    try {
      setLoading(true);

      // Abort previous fetch if any
      if (fetchMembersAbortController.current) {
        fetchMembersAbortController.current.abort();
      }
      fetchMembersAbortController.current = new AbortController();
      const signal = fetchMembersAbortController.current.signal;
      // Fetch data
      const response = await getTeamMembersFilterTableAPI({ signal, params });

      // Handle response
      if (response?.success) {
        if (
          response?.result === "there is no data" ||
          (Array.isArray(response?.result) && response?.result.length === 0)
        ) {
          // Handle no data case
          setData([]);
          setTotal(0);
          setRecordsToDisplay(0);
          setPaginationLinks([]);
          toast.info(t("teamMembers.noDataMessage"), {
            position: "bottom-right",
            theme: "dark",
          });
        } else {
          const { data, current_page, per_page, links, total, to } =
            response.result;
          setData(data);
          setCurrentPage(current_page);
          setRecordsPerPage(per_page);
          setPaginationLinks(links);
          setTotal(total);
          setRecordsToDisplay(to);
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      setLoading(false);
    }
  };

  // Fetch when tab becomes active (after restore) or pagination changes
  useEffect(() => {
    if (activeTab !== "members" || !restoredDone.current) return;

    const params = {
      from: formatDate(startDate), // current filter states
      to: formatDate(endDate),
      member: selectedTM?.id,
      role,
      currentPage,
      recordsPerPage,
    };
    fetchInitialData(params);
  }, [activeTab, currentPage, recordsPerPage]);

  // Mark restoration done immediately (state comes from Redux)
  useEffect(() => {
    restoredDone.current = true;
  }, []);

  // Fetch roles if not already loaded
  const rolesState = useSelector((state) => state.role.roles);
  useEffect(() => {
    const fetchRoles = async () => {
      if (!rolesState || rolesState.length === 0) {
        try {
          const res = await getAllRolesApi();
          if (res?.data) {
            dispatch(setRoles(res.data));
          }
        } catch (err) {
          console.error("Failed to fetch roles", err);
        }
      }
    };
    fetchRoles();
  }, [rolesState, dispatch]);

  const defaultColumn = useMemo(
    () => ({
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const { teamMembersColumns } = useTranslatedColumns();
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setPageSize,
    rows,
    state: { globalFilter },
  } = useTable(
    {
      columns: teamMembersColumns,
      data: data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handlePageChange = async (url) => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedTM?.id,
      role,
      url,
      recordsPerPage,
    };
    await fetchInitialData(params);
  };

  const handlePageSizeChange = async (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      member: selectedTM,
      role,
      currentPage,
      recordsPerPage: size,
    };
    await fetchInitialData(params);
  };

  return loading ? (
    <FetchingDataLoading className={"content-container"} />
  ) : (
    <>
      <div className={"content-container"}>
        <GlobalFilter
          preGlobalFilteredRows={preGlobalFilteredRows}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          // handleFilterByDuration={handleFilterByDuration}
          fetchInitialData={fetchInitialData}
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          selectedTM={selectedTM}
          setSelectedTM={setSelectedTM}
          recordsPerPage={recordsPerPage}
          currentPage={currentPage}
          role={role}
          setRole={setRole}
          activeTab={activeTab}
        />
      </div>
      <div className={"all-leads-table px-2"}>
        <Table
          responsive={"xl"}
          className="table text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <thead>
                {headerGroups?.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers?.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows?.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      key={row.original.id}
                    >
                      {row?.cells?.map((cell, j) => {
                        return (
                          <td
                            {...cell.getCellProps()}
                            key={j}
                            onClick={() => {
                              setSelectedTM({
                                name: row?.original?.name,
                                id: row?.original?.id,
                              });
                              setShowStatistics(true);
                            }}
                          >
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
        />
      </div>
    </>
  );
};

export default TeamMembersTable;
