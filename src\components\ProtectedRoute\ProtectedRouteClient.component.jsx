import {Navigate} from 'react-router-dom';
import {useSelector} from 'react-redux';
import usePackageCheck from "../../hooks/usePackageCheck";
import SubscriptionRequiredModal from "../../components/Modals/SubscriptionRequiredModal";
import { useLocation } from 'react-router-dom';
import {safeNumber} from "../../utils/safe-number";

function Protected({isSignedIn,
                      // requiredRoles,
                      requiredPermissions,
                      restrictTeamMember,
                      children}) {
    const { currentUserPermissions, user } = useSelector(state => state.auth);
    const { hasActivePackage, showSubscriptionModal, setShowSubscriptionModal } = usePackageCheck();
    const location = useLocation();
    if (isSignedIn) {
        if (user?.user?.flag === "admin"){
            return <Navigate to="/admin/dashboard"/>;
        } else if ((requiredPermissions && !requiredPermissions.every(permission => currentUserPermissions.includes(permission)))) {
            return <Navigate to="/unauthorized"/>;
        } else if (restrictTeamMember && user?.user?.parent_id && !(safeNumber(user?.user?.role) === 0 || safeNumber(user?.user?.role) === 1)) {
            return <Navigate to="/unauthorized"/>;
        }

        if (!hasActivePackage) {
            return (
                <>
                    <SubscriptionRequiredModal
                        show={showSubscriptionModal}
                        onHide={() => setShowSubscriptionModal(false)}
                    />
                    {location.pathname !== '/packages' && location.pathname !== '/account-settings' ? <Navigate to="/packages" /> : null}
                    {location.pathname === "/packages" || location.pathname === "/account-settings" ? children : null}
                </>
            );
        }

        return children;
    } else {
        return <Navigate to="/client/login"/>;
    }
}

export default Protected;
