import * as Yup from "yup";

export const validationSchema = Yup.object({
    result: Yup.string().required("Result is required"),
    // lead_status: Yup.string().required("Status is required"),
    next_date: Yup.string().required("Next date is required"),
    amount: Yup.string().test(
        "requiredForStatus8",
        "Reservation amount is required",
        function (value) {
            if (Number(this.parent.lead_status) === 8) {
                return !!value;
            }
            return true;
        },
    ),
    // quotation_amount: Yup.string().test(
    //     "requiredForStatus10",
    //     "Quotation amount is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === 10) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ),
    // quotation_status: Yup.string().test(
    //     "requiredForStatus10",
    //     "Quotation status is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === 10) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ).nullable(),
    // refuse_reason: Yup.string().test(
    //     "requiredForQuotationStatus3",
    //     "Refuse reason is required",
    //     function (value) {
    //         if (Number(this.parent.quotation_status) === 2) {
    //             return !!value;
    //         }
    //         return true;
    //     },
    // ),
    // action_proven: Yup.mixed().test(
    //     "requiredForActionStatus",
    //     "Action proof is required",
    //     function (value) {
    //       if (Number(this.parent.action) === 3) {
    //           return value && value instanceof File;
    //       }
    //       return true;
    //     },
    // ),
    // quotation_offer: Yup.mixed().test(
    //     "requiredForQuotationStatus",
    //     "Quotation offer is required",
    //     function (value) {
    //         if (Number(this.parent.lead_status) === 10) {
    //             return value && value instanceof File;
    //         }
    //         return true;
    //     }
    // ).nullable(),
});

export const actionStatusOptions = [
    { value: 0, label: "Pending" },
    { value: 11, label: "Assigned" },
    { value: 1, label: "In Progress" },
    { value: 2, label: "Completed" },
    { value: 3, label: "Rejected" },
    { value: 4, label: "Wrong Lead" },
    { value: 5, label: "Not Qualified" },
    { value: 6, label: "No Communication" },
    { value: 7, label: "Booked" },
    { value: 8, label: "Booked and Reserved" },
    { value: 9, label: "Canceled" },
    { value: 10, label: "Quotation" }
];

export const quotationStatus = [
    {value: 0, label: "Pending"},
    {value: 1, label: "Accepted"},
    {value: 2, label: "Rejected"}
]
