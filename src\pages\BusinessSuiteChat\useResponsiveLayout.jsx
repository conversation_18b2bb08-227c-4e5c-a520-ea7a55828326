import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  selectMessages,
  selectSelectedChat,
  selectWhatsappChatMessages,
  selectSelectedWhatsappChat,
  selectWhatsappChats,
} from "../../redux/features/metaBusinessChatSlice";

const useResponsiveLayout = () => {
  const [showMessagesSide, setShowMessagesSide] = useState(true);
  const [showChatContent, setShowChatContent] = useState(true);
  const [showWhatsAppAccountsSide, setShowWhatsAppAccountsSide] =
    useState(true);
  const [showWhatsAppAccountsContent, setShowWhatsAppContentSide] =
    useState(true);
  const messages = useSelector(selectMessages);
  const selectedChat = useSelector(selectSelectedChat);
  const whatsappChatMessages = useSelector(selectWhatsappChatMessages);
  const selectedWhatsAccount = useSelector(selectSelectedWhatsappChat);
  const whatsAppAccounts = useSelector(selectWhatsappChats);

  const updateLayout = () => {
    const isWide = window.innerWidth >= 1200;
    const hasSelected = !!selectedChat;

    if (isWide) {
      // On wide screens always show both columns
      setShowMessagesSide(true);
      setShowChatContent(true);
      return;
    }

    // Mobile / tablet view (<1200px)
    if (hasSelected) {
      // When a chat is chosen, hide list and show content
      setShowMessagesSide(false);
      setShowChatContent(true);
    } else {
      // No chat chosen, show list only
      setShowMessagesSide(true);
      setShowChatContent(false);
    }
  };

  const whatsAppLayout = () => {
    if (!selectedWhatsAccount && window.innerWidth >= 1200) {
      setShowWhatsAppAccountsSide(true);
      setShowWhatsAppContentSide(true);
    } else if (selectedWhatsAccount && window.innerWidth >= 1200) {
      setShowWhatsAppAccountsSide(true);
      setShowWhatsAppContentSide(true);
    } else if (
      selectedWhatsAccount &&
      window.innerWidth < 1200 &&
      whatsAppAccounts.length === 0
    ) {
      setShowWhatsAppAccountsSide(true);
      setShowWhatsAppContentSide(false);
    } else if (
      selectedWhatsAccount &&
      window.innerWidth < 1200 &&
      whatsAppAccounts.length !== 0
    ) {
      setShowWhatsAppAccountsSide(false);
      setShowWhatsAppContentSide(true);
    } else if (selectedWhatsAccount && window.innerWidth < 1200) {
      setShowWhatsAppAccountsSide(false);
      setShowWhatsAppContentSide(true);
    } else if (!selectedWhatsAccount && window.innerWidth < 1200) {
      setShowWhatsAppAccountsSide(true);
      setShowWhatsAppContentSide(false);
    } else if (selectedWhatsAccount && messages && window.innerWidth < 1200) {
      setShowWhatsAppAccountsSide(false);
      setShowWhatsAppContentSide(true);
    } else {
      setShowWhatsAppAccountsSide(true);
      setShowWhatsAppContentSide(true);
    }
  };

  useEffect(() => {
    whatsAppLayout();
    const handleResize = () => whatsAppLayout();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [selectedWhatsAccount, whatsAppAccounts]);

  useEffect(() => {
    updateLayout();
    const handleResize = () => updateLayout();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [selectedChat, messages]);

  return {
    showMessagesSide,
    showChatContent,
    showWhatsAppAccountsSide,
    showWhatsAppAccountsContent,
    setShowChatContent,
  };
};

export default useResponsiveLayout;
