import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import getSingleRoleApi from '../../services/roles/get-single-role.api';
import updateRoleApi from '../../services/roles/update-role.api';
import Cookies from 'js-cookie';
import { showErrorToast } from '../../utils/toast-success-error';
import { toast } from 'react-toastify';
import { setCurrentUserPermissions } from './authSlice';

const initialState = {
  roles: [],
  permissions: [],
  selectedPermissions: [],
  selectedRoleName: null,
  loading: false,
  allPermissions: [],
  defaultKey: null,
  roleId: null,
  dynamicOwnersRolesData: [],
  createRole: false,
};

export const fetchPermissions = createAsyncThunk(
  'role/fetchPermissions',
  async (_, { dispatch }) => {
    const userPermissions = JSON.parse(Cookies.get('userPermissions'));
    dispatch(setCurrentUserPermissions(userPermissions));
    return userPermissions;
  }
);

export const fetchSingleRoleData = createAsyncThunk(
  'role/fetchSingleRoleData',
  async (id, { rejectWithValue }) => {
    try {
      const response = await getSingleRoleApi(id);
      return response?.data;
    } catch (error) {
      showErrorToast(error?.response?.data?.message || 'Failed to fetch role data', { position: 'bottom-right', theme: 'dark' });
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch role data');
    }
  }
);

export const updateRole = createAsyncThunk(
  'role/updateRole',
  async ({ roleId, selectedPermissions }, { rejectWithValue }) => {
    try {
      await updateRoleApi(roleId, selectedPermissions);
      toast('Role updated successfully', { type: 'success', position: 'bottom-right', theme: 'dark' });
      return true;
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Failed to update role', { type: 'error', position: 'bottom-right', theme: 'dark' });
      return rejectWithValue(error?.response?.data?.message || 'Failed to update role');
    }
  }
);

export const handleTabSelect = createAsyncThunk(
  'role/handleTabSelect',
  async (selectedKey, { dispatch, getState }) => {
    const { roles } = getState().role;
    const selectedRole = roles.find(role => role.name === selectedKey);

    if (selectedRole) {
      // First update the state with the selected role info
      dispatch(setSelectedRoleName(selectedRole.name));
      dispatch(setDefaultKey(selectedKey));
      dispatch(setRoleId(selectedRole.id));

      // Then fetch the role's permissions
      try {
        const response = await getSingleRoleApi(selectedRole.id);
        return response?.data;
      } catch (error) {
        showErrorToast(error?.response?.data?.message || 'Failed to fetch role data', { position: 'bottom-right', theme: 'dark' });
        throw error;
      }
    }
    return null;
  }
);

const roleSlice = createSlice({
  name: 'role',
  initialState,
  reducers: {
    setRoles: (state, action) => {
      state.roles = action.payload;
    },
    setPermissions: (state, action) => {
      state.permissions = action.payload;
    },
    setSelectedPermissions: (state, action) => {
      state.selectedPermissions = action.payload;
    },
    setSelectedRoleName: (state, action) => {
      state.selectedRoleName = action.payload;
    },
    setCreateRole: (state, action) => {
      state.createRole = action.payload;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setAllPermissions: (state, action) => {
      state.allPermissions = action.payload;
    },
    setDefaultKey: (state, action) => {
      state.defaultKey = action.payload;
    },
    setRoleId: (state, action) => {
      state.roleId = action.payload;
    },
    setDynamicOwnersRolesData: (state, action) => {
      state.dynamicOwnersRolesData = action.payload;
    },
    handleSwitchChange: (state, action) => {
      const permissionName = action.payload;
      if (state.selectedPermissions.includes(permissionName)) {
        state.selectedPermissions = state.selectedPermissions.filter(name => name !== permissionName);
      } else {
        state.selectedPermissions = [...state.selectedPermissions, permissionName];
      }
    },
    handleVerticalTabSelect: (state, action) => {
      const selectedKey = action.payload;
      const selectedRole = state.dynamicOwnersRolesData.find(role => role.key === selectedKey);
      if (selectedRole) {
        state.defaultKey = selectedKey;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPermissions.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPermissions.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(fetchSingleRoleData.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSingleRoleData.fulfilled, (state, action) => {
        state.permissions = action.payload?.permissions || [];
        state.selectedPermissions = action.payload?.permissions?.map(permission => permission.name) || [];
        state.loading = false;
      })
      .addCase(fetchSingleRoleData.rejected, (state) => {
        state.loading = false;
      })
      .addCase(updateRole.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateRole.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updateRole.rejected, (state) => {
        state.loading = false;
      })
      .addCase(handleTabSelect.pending, (state) => {
        state.loading = true;
      })
      .addCase(handleTabSelect.fulfilled, (state, action) => {
        if (action.payload) {
          state.permissions = action.payload?.permissions || [];
          state.selectedPermissions = action.payload?.permissions?.map(permission => permission.name) || [];
        }
        state.loading = false;
      })
      .addCase(handleTabSelect.rejected, (state) => {
        state.loading = false;
      });
  }
});

export const {
  setRoles,
  setPermissions,
  setSelectedPermissions,
  setSelectedRoleName,
  setCreateRole,
  setLoading,
  setAllPermissions,
  setDefaultKey,
  setRoleId,
  setDynamicOwnersRolesData,
  handleSwitchChange,
  handleVerticalTabSelect
} = roleSlice.actions;

export default roleSlice.reducer;

