import { <PERSON><PERSON>, Col, Container, Form, Nav, Row, Tab } from "react-bootstrap";
import RolesTab from "../../components/Roles_Permissions_Tabs/RolesTab";
import "./roles-permissions.css";
import { useEffect, useLayoutEffect, useState } from "react";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import { Formik } from "formik";
import * as Yup from "yup";
import createRoleApi from "../../services/roles/create-role.api";
import { toast } from "react-toastify";
import { useSelector, useDispatch } from "react-redux";
import getAllPermissionsApi from "../../services/roles/get-all-permissions.api";
import getAllRolesApi from "../../services/roles/get-all-roles.api";
import { useTranslation } from "react-i18next";
import {
  setRoles,
  setCreateRole,
  setAllPermissions,
  setSelectedPermissions,
  setSelectedRoleName,
  setDefaultKey,
  setRoleId,
  setLoading,
  handleTabSelect,
} from "../../redux/features/roleSlice";

const RolesPermissions = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { user, currentUserPermissions } = useSelector((state) => state.auth);
  const { selectedRoleName, createRole, roles, loading } = useSelector(
    (state) => state.role
  );

  const [isMobileScreen, setIsMobileScreen] = useState(false);

  useLayoutEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setIsMobileScreen(true);
      } else {
        setIsMobileScreen(false);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const roleType =
          user.user.role === 0 || user.user.role === "Owner"
            ? user.user.flag === "admin"
              ? "admin"
              : "user"
            : user.user.flag;

        const promises = [
          await getAllPermissionsApi(roleType),
          currentUserPermissions.includes("role-list")
            ? await getAllRolesApi(roleType)
            : null,
        ];

        const [permissionsResponse, rolesResponse] = await Promise.all(
          promises
        );

        dispatch(
          setAllPermissions(
            roleType === "admin"
              ? permissionsResponse.data["Admin Permissions"]
              : permissionsResponse.data["User Permissions"]
          )
        );

        if (rolesResponse?.data?.length > 0) {
          const latestRole = rolesResponse.data[rolesResponse.data.length - 1];
          dispatch(
            setSelectedPermissions(
              latestRole.permissions.map((permission) => permission.name)
            )
          );
          dispatch(setSelectedRoleName(latestRole.name));
          dispatch(setDefaultKey(latestRole.name));
          dispatch(setRoleId(latestRole.id));
        }

        dispatch(setRoles(rolesResponse?.data || []));
        dispatch(setLoading(false));
      } catch (error) {
        if (
          error.config?.url.includes("getAllPermissionsApi") &&
          !toast.isActive("permissionsError")
        ) {
          toast.error(
            error?.response?.data?.message ||
              "Failed to fetch permissions data",
            {
              position: "bottom-right",
              theme: "dark",
              toastId: "permissionsError",
            }
          );
        } else {
          console.error("Failed to fetch data", {
            position: "bottom-right",
            theme: "dark",
          });
        }

        dispatch(setLoading(false));
      }
    })();
  }, [dispatch]);

  useEffect(() => {
    if (selectedRoleName !== null) {
      dispatch(handleTabSelect(selectedRoleName));
    }
  }, [dispatch, selectedRoleName]);

  const validationSchema = Yup.object().shape({
    roleName: Yup.string().required("Role Name is required"),
  });
  const handleCreateRole = async (values, { setSubmitting }) => {
    try {
      const largestRoleNumber = roles?.reduce(
        (max, role) => (parseInt(role.name) > max ? parseInt(role.name) : max),
        0
      );

      // Filter out undefined permissions
      values.permissions = values.permissions.filter(
        (permission) => permission !== undefined
      );

      const data = {
        name: (largestRoleNumber + 1).toString(), // Convert to string if needed by API
        roleName: values.roleName, // This will be used as show_name
        permissions: values.permissions,
      };

      const response = await createRoleApi(data);

      if (response && response.data) {
        dispatch(setRoles([...roles, response.data]));
        toast.success("Role Created Successfully", {
          position: "bottom-right",
          theme: "dark",
        });
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Failed to create role", {
        position: "bottom-right",
        theme: "dark",
      });
      console.error(error);
    } finally {
      setSubmitting(false);
      dispatch(setCreateRole(false));
    }
  };
  // const handleDeleteRole = async (roleId) => {
  //     try {
  //         const response = await deleteSingleRoleApi(roleId);
  //         toast.success(response.data.message, { position: "bottom-right", theme: "dark" });
  //         setRoles((prevRoles) => prevRoles.filter((role) => role.id !== roleId));
  //     }catch (error){
  //     toast.error(error?.response?.data?.message, { position: "bottom-right", theme: "dark" });
  //     }
  // }
  return (
    <>
      <h2>{t("roles.rolePermissions")}</h2>
      <div className={"content-container"}>
        {loading ? (
          <Container>
            <FetchingDataLoading />
          </Container>
        ) : (
          <>
            {!currentUserPermissions.includes("role-list") && (
              <div className={"mainColor fw-bold fs-2 text-center"}>
                Not Authorized to View Roles or Permissions
              </div>
            )}
            {isMobileScreen ? (
              <>
                <h2 className={"text-muted"}>Roles</h2>
                <Tab.Container
                  id="role-tabs"
                  activeKey={selectedRoleName}
                  onSelect={(selectedKey) =>
                    dispatch(handleTabSelect(parseInt(selectedKey)))
                  }
                >
                  {!createRole ? (
                    <Form.Group className="w-100 my-3">
                      <Form.Select
                        value={selectedRoleName}
                        onChange={(e) =>
                          dispatch(handleTabSelect(parseInt(e.target.value)))
                        }
                        className="text-capitalize rounded-pill w-100"
                      >
                        <option value="" disabled>
                          Select Role
                        </option>
                        {roles?.map((role) => (
                          <option key={role.id} value={String(role.name)}>
                            {role.show_name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  ) : null}
                  <Row className={"mt-2"}>
                    <Col>
                      {createRole ? (
                        <Formik
                          initialValues={{
                            roleName: "",
                            permissions: [],
                          }}
                          validationSchema={validationSchema}
                          onSubmit={(values, { setSubmitting }) =>
                            handleCreateRole(values, { setSubmitting })
                          }
                        >
                          {({
                            values,
                            handleChange,
                            handleSubmit,
                            touched,
                            errors,
                            setFieldValue,
                          }) => (
                            <Form noValidate onSubmit={handleSubmit}>
                              {/* Input field for role name */}
                              <Form.Group className="mx-auto w-25">
                                <Form.Label>{t("roles.roleName")}</Form.Label>
                                <Form.Control
                                  type="text"
                                  name="roleName"
                                  placeholder={t("roles.enterRole")}
                                  value={values.roleName}
                                  onChange={handleChange}
                                  isInvalid={
                                    touched.roleName && !!errors.roleName
                                  }
                                  isValid={touched.roleName && !errors.roleName}
                                />
                                <Form.Control.Feedback type={"invalid"}>
                                  {errors.roleName}
                                </Form.Control.Feedback>
                              </Form.Group>

                              {/* Add the RolesTab component here but with create role specific props */}
                              <RolesTab
                                isMobileScreen={isMobileScreen}
                                isCreateMode={true}
                                formikProps={{
                                  values,
                                  setFieldValue,
                                }}
                              />

                              <div className={"d-flex justify-content-center"}>
                                <Button
                                  className={"submit-btn"}
                                  type={"submit"}
                                >
                                  {t("roles.createRole")}
                                </Button>
                                <Button
                                  variant={"danger"}
                                  className={"rounded-pill ms-3"}
                                  onClick={() => dispatch(setCreateRole(false))}
                                >
                                  {t("roles.cancel")}
                                </Button>
                              </div>
                            </Form>
                          )}
                        </Formik>
                      ) : (
                        <Tab.Content>
                          {roles?.map((role) => (
                            <Tab.Pane
                              key={role.id}
                              eventKey={String(role.name)}
                            >
                              <RolesTab isMobileScreen={isMobileScreen} />
                            </Tab.Pane>
                          ))}
                        </Tab.Content>
                      )}
                    </Col>
                  </Row>
                </Tab.Container>
              </>
            ) : (
              <Tab.Container
                id="role-tabs"
                activeKey={selectedRoleName}
                onSelect={(selectedKey) =>
                  dispatch(handleTabSelect(parseInt(selectedKey)))
                }
              >
                {!createRole ? (
                  <Nav
                    variant="pills"
                    className={
                      "justify-content-center mx-auto roles-tabs-navs my-3"
                    }
                  >
                    {roles?.map((role) => (
                      <Nav.Item key={role.id}>
                        <Nav.Link
                          eventKey={String(role.name)}
                          className={"text-capitalize"}
                        >
                          {role.show_name}
                        </Nav.Link>
                      </Nav.Item>
                    ))}
                  </Nav>
                ) : null}
                <Row className={"mt-2"}>
                  <Col>
                    {createRole ? (
                      <Formik
                        initialValues={{
                          roleName: "",
                          permissions: [],
                        }}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) =>
                          handleCreateRole(values, { setSubmitting })
                        }
                      >
                        {({
                          values,
                          handleChange,
                          handleSubmit,
                          touched,
                          errors,
                          setFieldValue,
                        }) => (
                          <Form noValidate onSubmit={handleSubmit}>
                            {/* Input field for role name */}
                            <Form.Group className="mx-auto w-25">
                              <Form.Label>{t("roles.roleName")}</Form.Label>
                              <Form.Control
                                type="text"
                                name="roleName"
                                placeholder={t("roles.enterRole")}
                                value={values.roleName}
                                onChange={handleChange}
                                isInvalid={
                                  touched.roleName && !!errors.roleName
                                }
                                isValid={touched.roleName && !errors.roleName}
                              />
                              <Form.Control.Feedback type={"invalid"}>
                                {errors.roleName}
                              </Form.Control.Feedback>
                            </Form.Group>

                            {/* Add the RolesTab component here but with create role specific props */}
                            <RolesTab
                              isMobileScreen={isMobileScreen}
                              isCreateMode={true}
                              formikProps={{
                                values,
                                setFieldValue,
                              }}
                            />

                            <div className={"d-flex justify-content-center"}>
                              <Button className={"submit-btn"} type={"submit"}>
                                {t("roles.createRole")}
                              </Button>
                              <Button
                                variant={"danger"}
                                className={"rounded-pill ms-3"}
                                onClick={() => dispatch(setCreateRole(false))}
                              >
                                {t("roles.cancel")}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    ) : (
                      <Tab.Content>
                        {roles?.map((role) => (
                          <Tab.Pane key={role.id} eventKey={String(role.name)}>
                            <RolesTab isMobileScreen={isMobileScreen} />
                          </Tab.Pane>
                        ))}
                      </Tab.Content>
                    )}
                  </Col>
                </Row>
              </Tab.Container>
            )}
          </>
        )}
        {!createRole ? (
          <div className={"mt-3 d-flex justify-content-center"}>
            {currentUserPermissions.includes("role-edit") ? (
              <Button
                className={"submit-btn"}
                type={"submit"}
                form={"role-form"}
              >
                {t("roles.updateRole")}
              </Button>
            ) : null}
            {/*<Button className={"mx-3 rounded-pill"} variant={"outline-danger"} onClick={()=>handleDeleteRole(selectedRoleName)}>*/}
            {/*    Delete Role*/}
            {/*</Button>*/}
            {currentUserPermissions.includes("role-create") ? (
              <Button
                className={"rounded-pill ms-3"}
                variant={"outline-primary"}
                onClick={() => dispatch(setCreateRole(true))}
              >
                {t("roles.createRole")}
              </Button>
            ) : null}
          </div>
        ) : null}
      </div>
    </>
  );
};

export default RolesPermissions;
