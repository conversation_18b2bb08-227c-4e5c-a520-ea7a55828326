import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  useFilters,
  useGlobalFilter,
  usePagination,
  useSortBy,
  useTable,
} from "react-table";
import FetchingDataLoading from "../LoadingAnimation/FetchingDataLoading";
import { Button, Col, Form, Row, Table } from "react-bootstrap";
import { BsFillCaretDownFill } from "react-icons/bs";
import { FaCalendarAlt } from "react-icons/fa";
import { TiArrowForward } from "react-icons/ti";
import DatePicker from "react-datepicker";
import { toast } from "react-toastify";
import { getLeadsFilterTableAPI } from "../../services/reports/get-leads-reports.api";
import { useTranslation } from "react-i18next";

import PaginationRecordsForReports from "./PaginationRecordsForReports";
import { useTranslatedColumns } from "./ColumnsForTables.module";
import { ExportLeadAssignmentReportsApi } from "../../services/reports/export-reports.api";
import {
  setLeadAssignmentFilters,
  clearLeadAssignmentFilters,
} from "../../redux/features/reportsSlice";

const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split(".")[0];
};

function GlobalFilter({
  setGlobalFilter,
  fetchInitialData,
  recordsPerPage,
  currentPage,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  leadsSelectedTM,
  setLeadsSelectedTM,
  source,
  setSource,
}) {
  const { t } = useTranslation();
  const teamMembers = useSelector((state) => state.client.teamMembers);
  const dispatchRedux = useDispatch();

  const activeFiltersRef = useRef({
    activeFromDateLeads: null,
    activeToDateLeads: null,
    activeAssignedLeads: null,
    activeSourceLeads: null,
  });

  const setActiveStatesForExport = () => {
    activeFiltersRef.current = {
      activeFromDateLeads: startDate,
      activeToDateLeads: endDate,
      activeAssignedLeads: leadsSelectedTM?.id,
      activeSourceLeads: source,
    };
  };

  const handleExport = async () => {
    setActiveStatesForExport();
    const params = {
      from: activeFiltersRef.current.activeFromDateLeads,
      to: activeFiltersRef.current.activeToDateLeads,
      assigned: activeFiltersRef.current.activeAssignedLeads,
      source: activeFiltersRef.current.activeSourceLeads,
      page: currentPage,
      number_of_records: recordsPerPage,
    };
    await ExportLeadAssignmentReportsApi(params);
  };

  const applyFilters = async () => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: leadsSelectedTM?.id,
      source,
      recordsPerPage,
      currentPage,
    };
    await fetchInitialData(params);
    dispatchRedux(
      setLeadAssignmentFilters({
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        selectedTM: leadsSelectedTM?.id,
        source,
      })
    );
  };

  const clearFilters = () => {
    setGlobalFilter("");
    setStartDate(null);
    setEndDate(null);
    setLeadsSelectedTM(null);
    setSource(null);
    fetchInitialData({ currentPage: 1, recordsPerPage });
    dispatchRedux(clearLeadAssignmentFilters());
  };

  return (
    <Row className={"align-items-center justify-content-evenly"}>
      <Col lg={2} sm={6} className={"my-2"}>
        <select
          value={source || ""}
          onChange={(e) => setSource(e.target.value)}
          className="form-select rounded-pill"
          aria-label="Default select example"
        >
          <option value="">{t("filters.selectSource")}</option>
          <option value={1}>Facebook</option>
          <option value={2}>Google Adsense</option>
          <option value={3}>SnapChat</option>
          <option value={4}>TikTok</option>
          <option value={5}>Instagram</option>
          <option value={6}>LinkedIn</option>
          <option value={7}>Phone</option>
          <option value={8}>WhatsApp</option>
          <option value={9}>Web (Generic)</option>
          <option value={10}>Messenger DM</option>
          <option value={11}>Instagram</option>
        </select>
      </Col>
      <Col lg={2} sm={6} className={"my-2"}>
        <Form.Select
          value={leadsSelectedTM?.id || ""}
          className={"rounded-pill"}
          onChange={(e) => setLeadsSelectedTM({ id: e.target.value })}
        >
          <option value="">{t("filters.selectTeamMember")}</option>
          {teamMembers?.length > 0
            ? teamMembers?.map((teamMember) => (
                <option key={teamMember?.id} value={teamMember?.id}>
                  {teamMember?.name}
                </option>
              ))
            : null}
        </Form.Select>
      </Col>

      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={startDate}
          selected={startDate || null}
          onChange={(date) => setStartDate(date)}
          selectsStart
          startDate={startDate || null}
          endDate={endDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("filters.from")}
                defaultValue={
                  startDate ? new Date(startDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col lg={2} sm={6} className={"date-picker-container my-2"}>
        <DatePicker
          key={endDate}
          selected={endDate}
          onChange={(date) => setEndDate(date)}
          selectsEnd
          startDate={startDate || null}
          endDate={endDate || null}
          minDate={startDate}
          customInput={
            <div className="position-relative">
              <FaCalendarAlt
                className="position-absolute top-50 end-0 translate-middle-y me-2"
                style={{ pointerEvents: "none" }}
              />
              <input
                className="form-control rounded-pill"
                placeholder={t("filters.to")}
                defaultValue={
                  endDate ? new Date(endDate).toLocaleDateString() : ""
                }
              />
            </div>
          }
        />
      </Col>
      <Col
        lg={4}
        className={"d-flex justify-content-center justify-content-lg-end my-2"}
      >
        <Button onClick={applyFilters} className="px-3 py-2 apply-btn">
          {t("filters.apply")}
        </Button>
        <Button
          onClick={clearFilters}
          className="rounded-pill clear-btn px-3 mx-2"
        >
          {t("filters.clear")}
        </Button>
        <div
          className={
            "d-flex justify-content-between align-items-center rounded-pill bg-dark text-white px-3 py-1 fs-6"
          }
          role={"button"}
          onClick={handleExport}
        >
          <TiArrowForward />
          <div>{t("filters.export")}</div>
        </div>
      </Col>
    </Row>
  );
}

function DefaultColumnFilter({
  column: { filterValue, preFilteredRows, setFilter },
}) {
  const count = preFilteredRows.length;

  return (
    <input
      className="form-control"
      value={filterValue || ""}
      onChange={(e) => {
        setFilter(e.target.value || undefined);
      }}
      placeholder={`Search ${count} records...`}
    />
  );
}

const LeadAssignmentTable = ({
  activeTab,
  leadsSelectedTM,
  setLeadsSelectedTM,
}) => {
  const savedLeadFilters = useSelector(
    (state) => state.reports.leadAssignmentFilters
  );

  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [data, setData] = useState([]);
  const [paginationLinks, setPaginationLinks] = useState([]);
  const [total, setTotal] = useState(0);
  const [recordsToDisplay, setRecordsToDisplay] = useState(10); // Default value
  const [startDate, setStartDate] = useState(
    savedLeadFilters.startDate ? new Date(savedLeadFilters.startDate) : null
  );
  const [endDate, setEndDate] = useState(
    savedLeadFilters.endDate ? new Date(savedLeadFilters.endDate) : null
  );
  const [source, setSource] = useState(savedLeadFilters.source);
  const fetchLeadsAbortController = useRef(null);

  const fetchInitialData = async (params) => {
    try {
      setLoading(true);

      // Abort previous fetch if any
      if (fetchLeadsAbortController.current) {
        fetchLeadsAbortController.current.abort();
      }
      fetchLeadsAbortController.current = new AbortController();
      const signal = fetchLeadsAbortController.current.signal;
      // Fetch data
      const response = await getLeadsFilterTableAPI({ signal, params });

      // Handle response
      if (response?.success) {
        if (
          response?.result === "there is no data" ||
          (Array.isArray(response?.result) && response?.result.length === 0)
        ) {
          // Handle no data case
          setData([]);
          setTotal(0);
          setRecordsToDisplay(0);
          setPaginationLinks([]);
          toast.info("No data available for the selected filters.", {
            position: "bottom-right",
            theme: "dark",
          });
        } else {
          const { data, current_page, per_page, links, total, to } =
            response.result;
          setData(data);
          setCurrentPage(current_page);
          setRecordsPerPage(per_page);
          setPaginationLinks(links);
          setTotal(total);
          setRecordsToDisplay(to);
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab !== "assignment") {
      return;
    }
    fetchInitialData({ currentPage, recordsPerPage });
  }, [activeTab]);

  const defaultColumn = useMemo(
    () => ({
      Filter: DefaultColumnFilter,
    }),
    []
  );
  const { leadAssignmentColumns } = useTranslatedColumns();
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    preGlobalFilteredRows,
    setGlobalFilter,
    setPageSize,
    rows,
    state: { globalFilter },
  } = useTable(
    {
      columns: leadAssignmentColumns,
      data: data,
      defaultColumn,
      initialState: { pageIndex: currentPage - 1, pageSize: recordsPerPage },
    },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const handlePageChange = async (url) => {
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: leadsSelectedTM,
      source,
      url,
      recordsPerPage,
    };
    await fetchInitialData(params);
  };

  const handlePageSizeChange = async (size) => {
    setRecordsPerPage(size);
    setPageSize(size);
    const params = {
      from: formatDate(startDate),
      to: formatDate(endDate),
      assigned: leadsSelectedTM?.id,
      source,
      currentPage,
      recordsPerPage: size,
    };
    await fetchInitialData(params);
  };

  // const handleFilterByDuration = (start, end, assigned, filterStatus) => {
  //   // Set start and end date for filtering
  //   const formatedStartDate = new Date(start).toLocaleDateString();
  //   const formatedEndDate = new Date(end).toLocaleDateString();
  //   setStartDate(start ? formatedStartDate : null);
  //   setEndDate(end ? formatedEndDate : null);
  //   setSelectedTM(assigned || null);
  //   setFilterStatus(Number(filterStatus) || null);
  //   console.log(start, end, assigned, Number(filterStatus));
  //   // assigned,source,to,page,number_of_records,from
  // };

  return loading ? (
    <FetchingDataLoading className={"content-container"} />
  ) : (
    <>
      <div className={"content-container"}>
        <GlobalFilter
          preGlobalFilteredRows={preGlobalFilteredRows}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          // handleFilterByDuration={handleFilterByDuration}
          fetchInitialData={fetchInitialData}
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          leadsSelectedTM={leadsSelectedTM}
          setLeadsSelectedTM={setLeadsSelectedTM}
          recordsPerPage={recordsPerPage}
          currentPage={currentPage}
          source={source}
          setSource={setSource}
        />
      </div>
      <div className={"all-leads-table px-2"}>
        <Table
          responsive={"xl"}
          className="table text-center position-relative"
          {...getTableProps()}
        >
          {loading ? (
            <FetchingDataLoading />
          ) : (
            <>
              <thead>
                {headerGroups?.map((headerGroup, index) => (
                  <tr {...headerGroup.getHeaderGroupProps()} key={index}>
                    {headerGroup.headers?.map((column, j) => (
                      <th
                        {...column.getHeaderProps(
                          column.getSortByToggleProps()
                        )}
                        key={j}
                      >
                        {column.render("Header")}
                        <span>
                          {column.isSorted ? (
                            column.isSortedDesc ? (
                              " 🔽"
                            ) : (
                              " 🔼"
                            )
                          ) : (
                            <> {column.accessor && <BsFillCaretDownFill />}</>
                          )}
                        </span>
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody {...getTableBodyProps()}>
                {rows?.map((row) => {
                  prepareRow(row);
                  return (
                    <tr
                      {...row.getRowProps()}
                      className={"client-table-row filter-table-rows"}
                      style={{ cursor: "default" }}
                      key={row.original.id}
                    >
                      {row?.cells?.map((cell, j) => {
                        return (
                          <td {...cell.getCellProps()} key={j}>
                            {cell.render("Cell")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </>
          )}
        </Table>
        <PaginationRecordsForReports
          onPageChange={handlePageChange}
          links={paginationLinks}
          handlePageSizeChange={handlePageSizeChange}
          per_page={recordsPerPage}
          to={recordsToDisplay}
          total={total}
        />
      </div>
    </>
  );
};

export default LeadAssignmentTable;
