import { useEffect, useState } from "react";
import { Accordion, Col, Form, Row } from "react-bootstrap";
import { useSelector, useDispatch } from "react-redux";
import {
  setDynamicOwnersRolesData,
  handleSwitchChange,
  setSelectedPermissions,
  updateRole,
} from "../../redux/features/roleSlice";

const RolesTab = ({ isMobileScreen, isCreateMode = false, formikProps }) => {
  const dispatch = useDispatch();
  const {
    allPermissions,
    dynamicOwnersRolesData,
    selectedPermissions,
    roleId,
  } = useSelector((state) => state.role);
  const [expandedPanels, setExpandedPanels] = useState([]);

  // Add the handleSubmit function
  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(updateRole({ roleId, selectedPermissions }));
  };

  // Handle permissions for both create and edit modes
  const handlePermissionChange = (permissionName) => {
    if (isCreateMode && formikProps) {
      const { values, setFieldValue } = formikProps;
      const currentPermissions = values.permissions || [];

      if (currentPermissions.includes(permissionName)) {
        // Remove permission if already selected
        setFieldValue(
          "permissions",
          currentPermissions.filter((name) => name !== permissionName)
        );
      } else {
        // Add permission if not selected
        setFieldValue("permissions", [...currentPermissions, permissionName]);
      }
    } else {
      // Use Redux for existing roles
      dispatch(handleSwitchChange(permissionName));
    }
  };

  useEffect(() => {
    const generateDynamicRolesData = () => {
      if (!Array.isArray(allPermissions)) {
        return;
      }
      let idCounter = 0;
      const dynamicData = [];
      const permissionsMap = {};

      allPermissions?.forEach((permission) => {
        const [key, action] = permission.name.split("-");
        if (action === "leads" && key === "All") {
          permissionsMap["lead"] = permissionsMap["lead"] || [];
          permissionsMap["lead"].push({
            ...permission,
            title: "List Leads",
          });
          permissionsMap["lead"].push({
            title: "Assigned Leads",
            disabled: true,
          });
          return;
        }

        const title =
          action.replace(/-/g, " ").charAt(0).toUpperCase() + action.slice(1);

        permissionsMap[key] = permissionsMap[key] || [];
        permissionsMap[key].push({
          ...permission,
          title: title,
        });
      });

      Object.entries(permissionsMap).forEach(([key, permissions]) => {
        const category = {
          id: idCounter++,
          key: key,
          title: key.charAt(0).toUpperCase() + key.slice(1),
          content: permissions,
        };
        dynamicData.push(category);
      });

      dispatch(setDynamicOwnersRolesData(dynamicData));
    };

    generateDynamicRolesData();
  }, [allPermissions]);

  const handleSelectAll = (permissions) => {
    const categoryPermissionNames = permissions
      .filter((permission) => !permission.disabled)
      .map((permission) => permission.name)
      .filter(Boolean);

    if (isCreateMode) {
      const currentPermissions = formikProps.values.permissions || [];
      const areAllSelected = categoryPermissionNames.every((permissionName) =>
        currentPermissions.includes(permissionName)
      );
      const otherPermissions = currentPermissions.filter(
        (permission) => !categoryPermissionNames.includes(permission)
      );

      formikProps.setFieldValue(
        "permissions",
        areAllSelected
          ? otherPermissions
          : [...otherPermissions, ...categoryPermissionNames]
      );
    } else {
      // Existing logic for edit mode
      const areAllSelected = categoryPermissionNames.every((permissionName) =>
        selectedPermissions?.includes(permissionName)
      );
      const otherSelections = selectedPermissions.filter(
        (permission) => !categoryPermissionNames.includes(permission)
      );
      dispatch(
        setSelectedPermissions(
          areAllSelected
            ? otherSelections
            : [...otherSelections, ...categoryPermissionNames]
        )
      );
    }
  };

  const handleGlobalSelectAll = () => {
    const allPermissionNames = dynamicOwnersRolesData
      ?.flatMap((category) =>
        category.content
          .filter((permission) => !permission.disabled)
          .map((permission) => permission.name)
      )
      .filter(Boolean);

    if (isCreateMode) {
      const areAllSelected = allPermissionNames?.every((permissionName) =>
        formikProps.values.permissions?.includes(permissionName)
      );
      formikProps.setFieldValue(
        "permissions",
        areAllSelected ? [] : allPermissionNames
      );
    } else {
      const areAllSelected = allPermissionNames?.every((permissionName) =>
        selectedPermissions?.includes(permissionName)
      );
      dispatch(
        setSelectedPermissions(areAllSelected ? [] : allPermissionNames)
      );
    }
  };

  const handleExpandCollapseAll = () => {
    if (expandedPanels.length === dynamicOwnersRolesData?.length) {
      setExpandedPanels([]);
    } else {
      setExpandedPanels(
        dynamicOwnersRolesData?.map((_, index) => String(index)) || []
      );
    }
  };

  const handleAccordionToggle = (panelId) => {
    setExpandedPanels((prev) => {
      if (prev.includes(panelId)) {
        return prev.filter((id) => id !== panelId);
      } else {
        return [...prev, panelId];
      }
    });
  };

  return (
    <Form
      onSubmit={isCreateMode ? undefined : handleSubmit}
      id={isCreateMode ? undefined : "role-form"}
    >
      <div className="global-controls mb-4 d-flex justify-content-end align-items-center gap-4">
        <Form.Check
          type="switch"
          id="expand-collapse-all"
          label="Expand/Collapse All"
          checked={expandedPanels.length === dynamicOwnersRolesData?.length}
          onChange={handleExpandCollapseAll}
          className="global-control-switch"
        />
        <Form.Check
          type="switch"
          id="select-deselect-all"
          label="Select/Deselect All Permissions"
          checked={dynamicOwnersRolesData
            ?.flatMap((category) =>
              category.content.filter((permission) => !permission.disabled)
            )
            ?.every((permission) =>
              isCreateMode
                ? formikProps.values.permissions?.includes(permission.name)
                : selectedPermissions?.includes(permission.name)
            )}
          onChange={handleGlobalSelectAll}
          className="global-control-switch"
        />
      </div>
      <Row className="g-4">
        {dynamicOwnersRolesData?.map((category, index) => (
          <Col lg={4} md={6} sm={12} key={index}>
            <Accordion
              activeKey={expandedPanels}
              onSelect={() => handleAccordionToggle(String(index))}
            >
              <Accordion.Item
                eventKey={String(index)}
                className="permission-accordion"
              >
                <Accordion.Header>
                  <div className="d-flex justify-content-between align-items-center w-100">
                    <span className="fw-bold">{category.title}</span>
                    <div className="d-flex align-items-center">
                      <Form.Check
                        type="checkbox"
                        className="permission-select-all ms-2"
                        id={`select-all-${category.id}`}
                        label="Select All"
                        checked={category.content.every(
                          (permission) =>
                            permission.disabled ||
                            (isCreateMode
                              ? formikProps.values.permissions?.includes(
                                  permission.name
                                )
                              : selectedPermissions?.includes(permission.name))
                        )}
                        onChange={() => handleSelectAll(category.content)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                  </div>
                </Accordion.Header>
                <Accordion.Body>
                  {category.content.map((permission, idx) => (
                    <div key={idx} className="permission-item">
                      <Form.Group className="d-flex justify-content-between align-items-center">
                        <Form.Label className="mb-0">
                          {permission.title}
                        </Form.Label>
                        <Form.Check
                          type="switch"
                          className="permission-switch"
                          id={`permission-switch-${permission.id}`}
                          onChange={
                            permission.disabled
                              ? null
                              : () => handlePermissionChange(permission.name)
                          }
                          checked={
                            permission.disabled ||
                            (isCreateMode
                              ? formikProps.values.permissions?.includes(
                                  permission.name
                                )
                              : selectedPermissions?.includes(permission.name))
                          }
                          disabled={permission.disabled}
                        />
                      </Form.Group>
                    </div>
                  ))}
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          </Col>
        ))}
      </Row>
    </Form>
  );
};

export default RolesTab;
