import React, { useEffect, useMemo, useState } from "react";
import clientService from "../../services/clients";
import { useTranslation } from "react-i18next";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import AdminPaginationComponent from "../../components/AdminPagination/AdminPaginationComponent";
import { format } from "date-fns";
import { Card, Col, Form, InputGroup, Row } from "react-bootstrap";
import { BsSearch } from "react-icons/bs";
import "./NewClientsDataTable.page.css";
import { Link } from "react-router-dom";
import { FaMagnifyingGlass } from "react-icons/fa6";

export default function NewClientsDataTable() {
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const { t } = useTranslation();

  useEffect(() => {
    const fetchClients = async () => {
      setLoading(true);
      try {
        const response = await clientService.getAllNewClientsApi({
          current_page: currentPage,
          per_page: recordsPerPage,
        });
        const data = response?.data?.["All Clients"] || [];
        setClients(data);
        setTotalPages(response?.data?.["Number Of Pages"] || 1);
      } catch (error) {
        setClients([]);
        setTotalPages(0);
      } finally {
        setLoading(false);
      }
    };
    fetchClients();
  }, [currentPage, recordsPerPage]);

  // Map backend data to table data structure
  const mappedData = useMemo(() => {
    return (Array.isArray(clients) ? clients : []).map((client, idx) => ({
      id: client?.id,
      name: client?.name || "-",
      email: client?.email || "-",
      phone: client?.phone || "-",
      status: client?.status || "-",
      createdAt: client?.created_at,
      numberOfLeads: client?.["Number Of Leads"] ?? "-",
      packageId: client?.package_id || "-",
    }));
  }, [clients]);

  // Filter data based on search term
  const data = useMemo(() => {
    if (!searchTerm.trim()) return mappedData;

    const lowercasedSearch = searchTerm.toLowerCase();
    return mappedData.filter((client) => {
      return (
        client.name.toLowerCase().includes(lowercasedSearch) ||
        client.email.toLowerCase().includes(lowercasedSearch) ||
        client.phone.toLowerCase().includes(lowercasedSearch) ||
        client.status.toLowerCase().includes(lowercasedSearch)
      );
    });
  }, [mappedData, searchTerm]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const columns = useMemo(
    () => [
      {
        Header: t("clientsTable.columns.name") || "Name",
        accessor: "name",
      },
      {
        Header: t("clientsTable.columns.email") || "Email",
        accessor: "email",
      },
      {
        Header: t("clientsTable.columns.phone") || "Phone",
        accessor: "phone",
      },
      {
        Header: t("clientsTable.columns.status") || "Status",
        accessor: "status",
      },
      {
        Header: t("clientsTable.columns.createdAt") || "Created At",
        accessor: "createdAt",
        Cell: ({ value }) => {
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              return format(parsedDate, "yyyy-MM-dd HH:mm:ss");
            }
          }
          return value || "-";
        },
      },
      {
        Header: t("clientsTable.columns.numberOfLeads") || "Number Of Leads",
        accessor: "numberOfLeads",
      },
      {
        Header: "Package",
        accessor: "packageId",
        Cell: ({ row }) => {
          const packageId = row.original.packageId;
          return packageId ? (
            <Link to={`/admin/clients/leads/${row.original.id}`}>
              <span className="text-success fw-bold">
                {packageId === 2 ? "Plus" : "Free"}
              </span>
            </Link>
          ) : null;
        },
      },
    ],
    [t]
  );

  return (
    <Card bg="dark" text="light" className="mb-5 border-success">
      <Card.Header className="text-center fs-5">
        {t("clientsTable.newClientsTitle") || "New Clients"}
      </Card.Header>
      <Card.Body>
        {loading ? (
          <FetchingDataLoading className="my-5" />
        ) : (
          <>
            <div
              className="filter-container p-3 mb-3"
              style={{
                backgroundColor: "#2c3033",
                borderRadius: "8px",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.15)",
              }}
            >
              <Row className="justify-content-end align-items-center">
                <Col lg={4} md={4} sm={12}>
                  <Form.Group className="position-relative">
                    <Form.Control
                      placeholder={`${t(
                        "tableControls.placeholders.searchTable"
                      )} ${mappedData.length} ${t(
                        "tableControls.placeholders.records"
                      )}...`}
                      value={searchTerm}
                      onChange={handleSearchChange}
                      className="rounded-pill"
                    />
                    <FaMagnifyingGlass
                      className="text-muted position-absolute"
                      style={{
                        right: "10px",
                        top: "50%",
                        transform: "translateY(-50%)",
                      }}
                    />
                  </Form.Group>
                </Col>
              </Row>
            </div>
            <div className="admin-theme">
              <DataTableComponent
                columns={columns}
                data={data}
                loading={loading}
                initialSortBy={[]}
                hiddenColumns={[]}
                noDataFound={
                  t("clientsTable.noClientsFound") || "No Clients Found"
                }
              />
            </div>
            <AdminPaginationComponent
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={recordsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setRecordsPerPage}
              itemsPerPageOptions={[10, 20, 30, 40, 50]}
            />
          </>
        )}
      </Card.Body>
    </Card>
  );
}
