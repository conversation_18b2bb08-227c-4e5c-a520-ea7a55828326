import "./admin-signin.css";
import Form from "react-bootstrap/Form";
import { Formik } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import SignInAdminApi from "../../services/auth/admin";
import { useNavigate } from "react-router-dom";
import useAuth from "../../redux/hooks/useAuth";
import { Button } from "react-bootstrap";

const AdminLoginPage = () => {
  const { login, setCurrentUserPermissions } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const initialValues = {
    email: "",
    password: "",
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const navigate = useNavigate();
  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    password: Yup.string().required("Password is required"),
  });

  const handleSubmit = async (values, { setSubmitting }) => {
    setSubmitting(false);
    const response = await SignInAdminApi(values);
    const userData = {
      token: response.data.token,
      user: response.data.Admin,
    };
    login(userData);
    setCurrentUserPermissions(
      response.data.Permissions.map((permission) => permission.name)
    );
    navigate("/admin/dashboard", { replace: true });
  };

  return (
    <>
      <div className={"admin-sign-in-page"} />
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          isSubmitting,
        }) => (
          <div className={"admin-sign-in-container"}>
            <center className={"fs-3 fw-bold text-white mb-2"}>
              Admin Login
            </center>
            <Form
              className={"admin-sign-in-form"}
              onSubmit={handleSubmit}
              noValidate
            >
              <Form.Group className={"mb-3"}>
                <Form.Label>Email address</Form.Label>
                <Form.Control
                  type="email"
                  placeholder="Enter email"
                  name="email"
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={touched.email && !!errors.email}
                  className={"text-black bg-white"}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.email}
                </Form.Control.Feedback>
              </Form.Group>

              <Form.Group className="mb-4 position-relative password-input-container">
                <Form.Label>Password</Form.Label>
                <Form.Control
                  type={showPassword ? "text" : "password"}
                  placeholder="Password"
                  name="password"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  isInvalid={touched.password && !!errors.password}
                  className={"text-black bg-white"}
                />
                <div
                  className={"password-input-icon-TM"}
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <AiOutlineEye size={25} />
                  ) : (
                    <AiOutlineEyeInvisible size={25} />
                  )}
                </div>
                <Form.Control.Feedback type="invalid">
                  {errors.password}
                </Form.Control.Feedback>
              </Form.Group>
              <center className={"my-4"}>
                <Button
                  className={"bg-white text-black border-0 fw-bold fs-6 px-3"}
                  type="submit"
                  disabled={isSubmitting}
                >
                  Sign In
                </Button>
              </center>
            </Form>
          </div>
        )}
      </Formik>
    </>
  );
};

export default AdminLoginPage;
