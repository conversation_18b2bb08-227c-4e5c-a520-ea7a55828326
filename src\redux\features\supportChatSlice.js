import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  collection,
  doc,
  getDocs,
  query,
  orderBy,
  onSnapshot,
  addDoc,
  serverTimestamp,
  updateDoc,
  where,
  writeBatch,
  setDoc,
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import { getFileType } from '../../utils/getFileType';
import uploadAttachmentApi from '../../services/upload-attachments';

// Initial state
const initialState = {
  messages: [],
  messageText: '',
  attachment: null,
  loading: false,
  error: null,
  hasNewMessages: false,
  unreadCount: 0,
  showChatModal: false,
};

// Async thunks
export const sendMessage = createAsyncThunk(
  'supportChat/sendMessage',
  async ({ clientId, clientName, messageText, attachment }, { rejectWithValue }) => {
    try {
      const userIdStr = clientId.toString();
      const formattedDate = new Date().toISOString();
      const chatDocRef = doc(db, 'support', userIdStr);
      const messagesCollectionRef = collection(chatDocRef, 'messages');

      // Client-side timestamp for immediate consistency in UI
      const clientTimestamp = new Date();

      // Handle file upload if attachment exists
      let fileUrl = null;
      let fileType = 'text';
      let filename = null;

      if (attachment && attachment instanceof File) {
        fileType = getFileType(attachment);
        filename = attachment.name;

        // Use uploadAttachmentApi for file uploads
        const formData = new FormData();
        formData.append('file', attachment);

        const response = await uploadAttachmentApi(formData);

        if (response && response.success && response.file_path) {
          fileUrl = response.file_path.replace(/\/\.\.\//g, "/");
          fileUrl = fileUrl.replace(/([^:])\/\/+/, '$1/');
          fileUrl = fileUrl.replace(/\/public\/public\//, '/public/');
        } else {
          return rejectWithValue('Upload failed: No URL returned');
        }
      }

      // Create message data - use fileUrl as the message content for attachments
      const messageData = {
        message: fileUrl || messageText, // This sets the file_path as the message content
        sender_id: userIdStr,
        sender_name: clientName || 'User',
        flag: 'client',
        created_time: serverTimestamp(),
        client_time: clientTimestamp.toISOString(),
        read: false,
        type: attachment ? fileType : 'text', // Explicitly set text type when no attachment
      };

      // Add filename if it exists
      if (filename) {
        messageData.filename = filename;
      }

      // Update the parent chat document with client information
      await setDoc(chatDocRef, {
        client_id: userIdStr,
        client_name: clientName || 'User',
        latest_message: fileUrl || messageText,
        lastMessageTime: formattedDate,
        lastMessageSender: 'client',
        has_unread_from_client: true,
        has_unread_from_support: false,
        created_at: serverTimestamp()
      }, { merge: true });

      // Add message to Firestore
      const messageRef = await addDoc(messagesCollectionRef, messageData);

      // Return data for immediate UI update
      return {
        messageText: fileUrl || messageText,
        messageId: messageRef.id,
        clientTime: clientTimestamp.toISOString(),
        senderName: clientName || 'User',
        senderId: userIdStr,
        flag: 'client',
        type: fileType,
        filename: filename
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const markMessagesAsRead = createAsyncThunk(
  'supportChat/markMessagesAsRead',
  async (userId, { rejectWithValue }) => {
    try {
      const userIdStr = userId.toString();
      const chatDocRef = doc(db, 'support', userIdStr);
      const messagesCollectionRef = collection(chatDocRef, 'messages');
      const unreadMessagesQuery = query(
        messagesCollectionRef,
        where('flag', '==', 'support'),
        where('read', '==', false)
      );

      const querySnapshot = await getDocs(unreadMessagesQuery);

      if (!querySnapshot.empty) {
        const batch = writeBatch(db);

        querySnapshot.forEach((doc) => {
          const messageDocRef = doc.ref;
          batch.update(messageDocRef, { read: true });
        });

        // Also update the main chat document to reset the unread flag
        batch.update(chatDocRef, { has_unread_from_support: false });

        await batch.commit();
      }

      return userId;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const setupGlobalChatListener = createAsyncThunk(
  'supportChat/setupGlobalChatListener',
  async (userId, { dispatch }) => {
    if (!userId) return { success: false };

    const userIdStr = userId.toString();

    try {
      // Set up listener for the chat document
      const chatDocRef = doc(db, 'support', userIdStr);

      // First listener: for the main chat document to detect unread flag changes
      const mainDocUnsubscribe = onSnapshot(chatDocRef, (docSnapshot) => {
        if (docSnapshot.exists()) {
          const chatData = docSnapshot.data();

          // If there are unread messages from support
          if (chatData.has_unread_from_support) {
            dispatch(setHasNewMessages(true));
          } else {
            dispatch(setHasNewMessages(false));
            dispatch(setUnreadCount(0));
          }

          // Update latest message in UI if needed
          if (chatData.latest_message) {
            // You can dispatch an action here if you want to display the latest message elsewhere
            // dispatch(setLatestMessage(chatData.latest_message));
          }
        }
      });

      // Second listener: for the messages collection to get all messages
      const messagesCollectionRef = collection(chatDocRef, 'messages');
      const messagesQuery = query(messagesCollectionRef, orderBy('created_time', 'asc'));

      const messagesUnsubscribe = onSnapshot(messagesQuery, (querySnapshot) => {
        const messages = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();

          // Convert Firebase Timestamp to serializable format
          if (data.created_time && typeof data.created_time.toDate === 'function') {
            messages.push({
              id: doc.id,
              ...data,
              created_time: data.created_time.toDate().toISOString()
            });
          } else {
            messages.push({
              id: doc.id,
              ...data
            });
          }
        });

        // Sort messages by time
        const sortedMessages = [...messages].sort((a, b) => {
          const dateA = new Date(a.created_time || a.client_time);
          const dateB = new Date(b.created_time || b.client_time);
          return dateA - dateB;
        });

        dispatch(setMessages(sortedMessages));

        // Count unread messages from support
        const unreadCount = sortedMessages.filter(
          (msg) => msg.flag === 'support' && !msg.read
        ).length;

        dispatch(setUnreadCount(unreadCount));
      });

      // Create a combined unsubscribe function and store it globally
      window.__userChatUnsubscribe = () => {
        mainDocUnsubscribe();
        messagesUnsubscribe();
      };

      return { success: true };
    } catch (error) {
      console.error('Error setting up chat listeners:', error);
      return { success: false, error: error.message };
    }
  }
);

// Slice
const supportChatSlice = createSlice({
  name: 'supportChat',
  initialState,
  reducers: {
    setMessages: (state, action) => {
      state.messages = action.payload;
    },
    setMessageText: (state, action) => {
      state.messageText = action.payload;
    },
    setAttachment: (state, action) => {
      state.attachment = action.payload;
    },
    setHasNewMessages: (state, action) => {
      state.hasNewMessages = action.payload;
    },
    setUnreadCount: (state, action) => {
      state.unreadCount = action.payload;
    },
    setChatModalVisibility: (state, action) => {
      state.showChatModal = action.payload;
      // Reset state when modal is closed
      if (!action.payload) {
        state.messageText = '';
        state.attachment = null;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendMessage.pending, (state) => {
        state.loading = true;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.loading = false;
        state.messageText = '';
        state.attachment = null;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(markMessagesAsRead.fulfilled, (state) => {
        state.hasNewMessages = false;
        state.unreadCount = 0;
      });
  },
});

// Actions
export const {
  setMessages,
  setMessageText,
  setAttachment,
  setHasNewMessages,
  setUnreadCount,
  setChatModalVisibility,
} = supportChatSlice.actions;

// Selectors
export const selectMessages = (state) => state.supportChat.messages;
export const selectMessageText = (state) => state.supportChat.messageText;
export const selectAttachment = (state) => state.supportChat.attachment;
export const selectLoading = (state) => state.supportChat.loading;
export const selectError = (state) => state.supportChat.error;
export const selectHasNewMessages = (state) => state.supportChat.hasNewMessages;
export const selectUnreadCount = (state) => state.supportChat.unreadCount;
export const selectShowChatModal = (state) => state.supportChat.showChatModal;

export default supportChatSlice.reducer;
