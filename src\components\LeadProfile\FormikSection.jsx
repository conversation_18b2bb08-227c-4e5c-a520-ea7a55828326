import Form from "react-bootstrap/Form";
// import {actionStatusOptions, quotationStatus} from "./ActivitiesTabsData.module";
// import QuotationOfferFile from "./QuotationOfferFile";
import CustomDateTimePicker from "./CustomDateTimePicker";
import ActionApproveFile from "./ActionApproveFile";
import { useTranslation } from "react-i18next";

const FormikSection = ({formik, setActionProven,
    // setQuotationOffer,
    // leadDetails
}) => {
    const { t } = useTranslation();

    return (<>
            {/* <Form.Group className={"mb-3"}>
                <Form.Label>{t("forms.selectStatus")}:</Form.Label>
                <Form.Select
                    value={formik.values.lead_status || ""}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    isValid={formik.touched.lead_status && !formik.errors.lead_status}
                    isInvalid={formik.touched.lead_status && formik.errors.lead_status}
                    name="lead_status"
                >
                    <option value="" label={t("forms.selectStatus")}/>
                    {actionStatusOptions.map((option) => (<option key={option.value} value={option.value}>
                        {option.label}
                    </option>))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                    {formik.errors.lead_status}
                </Form.Control.Feedback>
            </Form.Group> */}
            {/* {Number(formik.values.lead_status) === 8 ? (
                <Form.Group className={"mb-3"}>
                    <Form.Label>{t("forms.amount")}</Form.Label>
                    <Form.Control
                        type="number"
                        placeholder={t("forms.placeholder.amount")}
                        value={formik.values.amount}
                        onChange={formik.handleChange}
                        isValid={formik.touched.amount && !formik.errors.amount}
                        isInvalid={formik.touched.amount && formik.errors.amount}
                        name="amount"
                    />
                    <Form.Control.Feedback type="invalid">
                        {formik.errors.amount}
                    </Form.Control.Feedback>
                </Form.Group>) : null} */}
            {/* {Number(formik.values.lead_status) === 10 ? (<>
                <Form.Group className={"mb-3"}>
                    <Form.Label>{t("forms.quotationAmount")}</Form.Label>
                    <Form.Control
                        type="number"
                        placeholder={t("forms.placeholder.amount")}
                        value={formik.values.quotation_amount}
                        onChange={formik.handleChange}
                        isValid={formik.touched.quotation_amount && !formik.errors.quotation_amount}
                        isInvalid={formik.touched.quotation_amount && formik.errors.quotation_amount}
                        name="quotation_amount"
                    />
                    <Form.Control.Feedback type="invalid">
                        {formik.errors.quotation_amount}
                    </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                    <Form.Label>{t("forms.uploadQuotation")}</Form.Label>
                    <QuotationOfferFile
                        setQuotationOffer={(file) => formik.setFieldValue("quotation_offer", file)}
                    />
                    <Form.Control
                        type="file"
                        hidden
                        isValid={formik.touched.quotation_offer && !formik.errors.quotation_offer}
                        isInvalid={formik.touched.quotation_offer && formik.errors.quotation_offer}
                        name="quotation_offer"
                        onChange={(e) => {
                            const file = e.target.files[0];
                            if (file) {
                                // Set the file in your form state
                                setQuotationOffer(file);
                                formik.setFieldValue("quotation_offer", file);
                            }
                        }}
                    />
                    <Form.Control.Feedback type="invalid">
                        {formik.errors.quotation_offer}
                    </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                    <Form.Label>{t("forms.selectQuotationStatus")}:</Form.Label>
                    <Form.Select
                        value={formik.values.quotation_status || ""}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        isValid={formik.touched.quotation_status && !formik.errors.quotation_status}
                        isInvalid={formik.touched.quotation_status && formik.errors.quotation_status}
                        name="quotation_status"
                    >
                        <option value="" label={t("forms.selectQuotationStatus")}/>
                        {quotationStatus.map((option) => (<option key={option.value} value={option.value}>
                            {option.label}
                        </option>))}
                    </Form.Select>
                    <Form.Control.Feedback type="invalid">
                        {formik.errors.quotation_status}
                    </Form.Control.Feedback>
                </Form.Group>
                {Number(leadDetails?.quotation_status) === 2 || Number(formik.values.quotation_status) === 2 ? (
                    <Form.Group className={"mb-3"}>
                        <Form.Label>{t("forms.addRejectionReason")} :</Form.Label>
                        <Form.Control
                            as="textarea"
                            placeholder={t("forms.placeholder.result")}
                            style={{height: "100px"}}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            value={formik.values.refuse_reason}
                            isValid={formik.touched.refuse_reason && !formik.errors.refuse_reason}
                            isInvalid={formik.touched.refuse_reason && formik.errors.refuse_reason}
                            name="refuse_reason"
                        />
                        <Form.Control.Feedback type="invalid">
                            {formik.errors.refuse_reason}
                        </Form.Control.Feedback>
                    </Form.Group>) : null}
            </>) : null} */}
            <Form.Group className={"mb-3"}>
                <Form.Label>{t("forms.addResult")} :</Form.Label>
                <Form.Control
                    as="textarea"
                    placeholder={t("forms.placeholder.result")}
                    style={{height: "100px"}}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.result}
                    isValid={formik.touched.result && !formik.errors.result}
                    isInvalid={formik.touched.result && formik.errors.result}
                    name="result"
                />
                <Form.Control.Feedback type="invalid">
                    {formik.errors.result}
                </Form.Control.Feedback>
            </Form.Group>
            <Form.Group
                className={"mb-3 d-flex justify-content-between flex-column"}
            >
                <Form.Label>{t("forms.selectDateTime")} :</Form.Label>
                <CustomDateTimePicker
                    handleDateChange={(date) => {
                        formik.setFieldValue("next_date", date);
                    }}
                    value={formik.values.next_date}
                    onBlur={() => formik.setFieldTouched("next_date", true)}
                />
                {formik.touched.next_date && formik.errors.next_date ? (<small className={"text-danger"}>
                    {formik.errors.next_date}
                </small>) : null}
            </Form.Group>
            <Form.Group className={"mb-3"}>
                <Form.Label>{t("forms.uploadProof")} :</Form.Label>
                <ActionApproveFile
                    setActionProven={(file) => formik.setFieldValue("action_proven", file)}/>
                <Form.Control
                    type="file"
                    hidden
                    onChange={(e) => {
                        const file = e.target.files[0];
                        if (file) {
                            // Set the file in your form state
                            setActionProven(file);
                            formik.setFieldValue("action_proven", file);
                        }
                    }}
                    isValid={formik.touched.action_proven && !formik.errors.action_proven}
                    isInvalid={formik.touched.action_proven && formik.errors.action_proven}
                    name="action_proven"
                />
                <Form.Control.Feedback type="invalid">
                    {formik.errors.action_proven}
                </Form.Control.Feedback>
            </Form.Group>
            <Form.Group>
                <Form.Label>{t("forms.addNote")} :</Form.Label>
                <Form.Control
                    as="textarea"
                    placeholder={t("forms.placeholder.note")}
                    style={{height: "100px"}}
                    onChange={formik.handleChange}
                    value={formik.values.note}
                    name="note"
                />
            </Form.Group>
            <center>
                <div
                    className={"submit-btn py-1 my-3"}
                    onClick={() => {
                        formik.handleSubmit();
                        // console.log(formik.values);
                        console.log(formik.errors);
                    }}
                >
                    {t("buttons.save")}
                </div>
            </center>
        </>)
};

export default FormikSection;
