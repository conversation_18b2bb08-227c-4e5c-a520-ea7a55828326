import React, {useEffect, useMemo, useState} from 'react';
import {Badge, Button, Card, Col, Dropdown, FormControl, InputGroup, Row, Table} from 'react-bootstrap';
import {AiOutlineSearch} from 'react-icons/ai';
import {LuPackageSearch} from "react-icons/lu";
import {format} from 'date-fns';
import {usePagination, useSortBy, useTable} from 'react-table';
import useDebounce from '../../../utils/use-debounce';
import AdminPaginationComponent from '../../../components/AdminPagination/AdminPaginationComponent';
import {getSubscriptionsDataTableApi} from '../../../services/admin';
import {useDropdownClose} from '../../../hooks/useDropdownClose';
import "react-datepicker/dist/react-datepicker.css";
import {MdClear} from 'react-icons/md';
import { cleanData } from '../../../utils/clean-data';
import DateFilter from '../../../components/DateFilter/DateFilter';

// Separate component for the search bar
const SearchBar = ({ searchTerm, onSearch }) => {
  const [isSearching, setIsSearching] = useState(false);

  const handleClearSearch = () => {
    onSearch('');
    setIsSearching(false);
  };

  return (
    <InputGroup>
      <FormControl
        placeholder="Search for user subscription"
        className="bg-secondary text-light border-0"
        value={searchTerm}
        onChange={(e) => {
          onSearch(e.target.value);
          setIsSearching(e.target.value !== '');
        }}
      />
      <Button
        variant="outline-light"
        onClick={isSearching ? handleClearSearch : null}
      >
        {isSearching ? (
          <MdClear size={20} />
        ) : (
          <AiOutlineSearch size={20} />
        )}
      </Button>
    </InputGroup>
  );
};

// Separate component for package filter
const PackageFilter = ({ showFilter, onToggle, selectedPackage, onSelect, availablePackages }) => {
  const dropdownRef = useDropdownClose(onToggle);

  return (
    <div className="position-relative" ref={dropdownRef}>
      <Button
        variant="outline-light"
        size="md"
        onClick={() => onToggle(!showFilter)}
        className="d-flex align-items-center gap-2"
      >
        <LuPackageSearch />
      </Button>
      {showFilter && (
        <Dropdown.Menu variant={"dark"} show={true} className="position-absolute mt-2 admin-theme" style={{ right: 0, zIndex: 1000 }}>
          {availablePackages.map((packageItem) => (
            <Dropdown.Item
              key={packageItem.id}
              onClick={() => onSelect(packageItem.id)}
              active={selectedPackage === packageItem.id}
              className={" text-white"}
            >
              {packageItem.title}
            </Dropdown.Item>
          ))}
          {/*<Dropdown.Divider />*/}
          {/*<Dropdown.Item*/}
          {/*  onClick={() => onSelect(null)}*/}
          {/*  active={selectedPackage === null}*/}
          {/*  className={"text-white"}*/}
          {/*>*/}
          {/*  Clear Filter*/}
          {/*</Dropdown.Item>*/}
        </Dropdown.Menu>
      )}
    </div>
  );
};



// Separate component for active filters display
const ActiveFilters = ({
  selectedPackage,
  appliedDateRange,
  availablePackages,
  onClearPackage,
  onClearDate,
  searchTerm,
  onClearSearch
}) => {
  const handleClearAll = () => {
    onClearPackage();
    onClearDate();
    onClearSearch();
  };

  if (!selectedPackage && (!appliedDateRange?.startDate && !appliedDateRange?.endDate) && !searchTerm) {
    return null;
  }

  return (
    <div className="mb-3 d-flex align-items-center">
      <small className="text-muted me-2">Active Filters:</small>
      <div className="d-flex gap-2 flex-wrap align-items-center">
        {searchTerm && (
          <Badge
            bg="secondary"
            className="d-flex align-items-center gap-2"
            style={{ cursor: 'pointer' }}
            onClick={onClearSearch}
          >
            Search: {searchTerm}
            <MdClear />
          </Badge>
        )}
        {selectedPackage && (
          <Badge
            bg="secondary"
            className="d-flex align-items-center gap-2"
            style={{ cursor: 'pointer' }}
            onClick={onClearPackage}
          >
            Package: {availablePackages.find(p => p.id === selectedPackage)?.title || 'Unknown'}
            <MdClear />
          </Badge>
        )}
        {appliedDateRange?.startDate && appliedDateRange?.endDate && (
          <Badge
            bg="secondary"
            className="d-flex align-items-center gap-2"
            style={{ cursor: 'pointer' }}
            onClick={onClearDate}
          >
            Date: {format(appliedDateRange.startDate, 'MM/dd/yyyy')} - {format(appliedDateRange.endDate, 'MM/dd/yyyy')}
            <MdClear />
          </Badge>
        )}
        {(selectedPackage || appliedDateRange?.startDate || searchTerm) && (
          <Badge
            bg="secondary"
            className="d-flex align-items-center gap-2"
            style={{ cursor: 'pointer' }}
            onClick={handleClearAll}
          >
            Clear All
            <MdClear />
          </Badge>
        )}
      </div>
    </div>
  );
};

export default function SubscriptionsDataTable() {
  // State management
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showPackageFilter, setShowPackageFilter] = useState(false);
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [dateRange, setDateRange] = useState({ startDate: null, endDate: null });
  const [appliedDateRange, setAppliedDateRange] = useState({ startDate: null, endDate: null });
  const [subscriptions, setSubscriptions] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(0);

  const debouncedSearchTerm = useDebounce(searchTerm, 2000);

  // Available packages data
  const availablePackages = [
    { id: 1, title: 'Free' },
    { id: 2, title: 'Plus' },
    { id: 3, title: 'Custom' }
  ];

  // Handlers
  const handleSearch = (term) => setSearchTerm(term);
  const handlePackageSelect = (packageId) => {
    setSelectedPackage(packageId);
    setShowPackageFilter(false);
    setCurrentPage(1);
  };
  const handleDateChange = (newRange) => setDateRange(newRange);
  const handleApplyDateFilter = () => {
    if (dateRange.startDate && dateRange.endDate) {
      setAppliedDateRange({
        startDate: new Date(dateRange.startDate),
        endDate: new Date(dateRange.endDate)
      });
      setShowDateFilter(false);
      setCurrentPage(1);
    }
  };
  const handleClearDateFilter = () => {
    const clearedRange = { startDate: null, endDate: null };
    setDateRange(clearedRange);
    setAppliedDateRange(clearedRange);
    setShowDateFilter(false);
    setCurrentPage(1);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Fetch data
  useEffect(() => {
    const fetchSubscriptions = async () => {
      setLoading(true);
      try {
        const params = {
          name: debouncedSearchTerm,
          package: selectedPackage,
          start_date: appliedDateRange.startDate,
          end_date: appliedDateRange.endDate,
          page: currentPage,
          per_page: recordsPerPage
        };
        const validParams = cleanData(params);
        const response = await getSubscriptionsDataTableApi(validParams);
        setSubscriptions(response.data.data.map(item => ({
          id: item.id,
          name: item.user.name,
          package: item.package.title,
          packageId: item.package.id,
          endDate: item.end_date,
          duration: calculateDuration(item.subscription_at, item.end_date),
          status: item.status
        })));

        setTotalItems(response.data.total);
        setTotalPages(response.data.last_page);
      } catch (error) {
        console.error('Error fetching subscriptions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptions();
  }, [
    debouncedSearchTerm,
    selectedPackage,
    appliedDateRange.startDate,
    appliedDateRange.endDate,
    currentPage,
    recordsPerPage
  ]);

  // Helper function to calculate duration
  const calculateDuration = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 +
                      (end.getMonth() - start.getMonth());
    return `${diffMonths} months`;
  };

  // Define table columns
  const columns = useMemo(() => [
    {
      Header: '#',
      Cell: ({ row }) => {
        return row.index + 1;
      },
      style: { width: '50px' }
    },
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'Package',
      accessor: 'package',
    },
    {
      Header: 'End Date',
      accessor: 'endDate',
    },
    {
      Header: 'Duration',
      accessor: 'duration',
    },
    {
      Header: 'Status',
      accessor: 'status',
      Cell: ({ value }) => {
        // Define badge colors based on status
        const getBadgeColor = (status) => {
          switch (status.toLowerCase()) {
            case 'active':
              return 'success';
            case 'expired':
              return 'danger';
            case 'pending':
              return 'warning';
            case 'cancelled':
              return 'secondary';
            default:
              return 'info';
          }
        };

        return (
          <Badge
            bg={getBadgeColor(value)}
            className="px-3 py-2"
          >
            {value}
          </Badge>
        );
      }
    },
  ], []);

  // Initialize react-table
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
  } = useTable(
    {
      columns,
      data: subscriptions,
      initialState: {
        pageIndex: currentPage - 1,
        pageSize: recordsPerPage
      },
      manualPagination: true,
      pageCount: totalPages,
    },
    useSortBy,
    usePagination
  );

  return (
    <Card bg="dark" text="light" className="mb-5 border-success">
      <Card.Header className='text-center fs-5'>Clients Subscriptions</Card.Header>
      <Card.Body>
        <Row className="align-items-center mb-4 px-2">
          <Col md={6}>
            <SearchBar searchTerm={searchTerm} onSearch={handleSearch} />
          </Col>
          <Col md={6} className="text-end d-flex gap-3 justify-content-end">
            <PackageFilter
              showFilter={showPackageFilter}
              onToggle={setShowPackageFilter}
              selectedPackage={selectedPackage}
              onSelect={handlePackageSelect}
              availablePackages={availablePackages}
            />
            <DateFilter
              showFilter={showDateFilter}
              onToggle={setShowDateFilter}
              dateRange={dateRange}
              onDateChange={handleDateChange}
              onApply={handleApplyDateFilter}
              onClear={handleClearDateFilter}
            />
          </Col>
        </Row>

        <ActiveFilters
          selectedPackage={selectedPackage}
          appliedDateRange={appliedDateRange}
          availablePackages={availablePackages}
          onClearPackage={() => {
            setSelectedPackage(null);
            setCurrentPage(1);
          }}
          onClearDate={handleClearDateFilter}
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
        />

        {loading ? (
          <div className="text-center p-5">
            <span className="spinner-border text-light" role="status" />
          </div>
        ) : (
          <>
            {subscriptions.length === 0 ? (
              <div className="text-center py-5 text-white d-flex flex-column align-items-center gap-4">
                <h5>No Clients Found</h5>
                <p>Try adjusting your search or filter criteria</p>

              </div>
            ) : (
              <>
                <Table {...getTableProps()} responsive striped bordered hover variant="dark">
                  <thead>
                    {headerGroups.map(headerGroup => (
                      <tr {...headerGroup.getHeaderGroupProps()}>
                        {headerGroup.headers.map(column => (
                          <th {...column.getHeaderProps(column.getSortByToggleProps())}>
                            {column.render('Header')}
                            <span>
                              {column.isSorted
                                ? column.isSortedDesc
                                  ? ' 🔽'
                                  : ' 🔼'
                                : ''}
                            </span>
                          </th>
                        ))}
                      </tr>
                    ))}
                  </thead>
                  <tbody {...getTableBodyProps()}>
                    {page.map(row => {
                      prepareRow(row);
                      return (
                        <tr {...row.getRowProps()}>
                          {row.cells.map(cell => (
                            <td {...cell.getCellProps()}>{cell.render('Cell')}</td>
                          ))}
                        </tr>
                      );
                    })}
                  </tbody>
                </Table>

                <AdminPaginationComponent
                  currentPage={currentPage}
                  totalPages={totalPages}
                  itemsPerPage={recordsPerPage}
                  onPageChange={(page) => {
                    setCurrentPage(page);
                  }}
                  onItemsPerPageChange={(size) => {
                    setRecordsPerPage(size);
                    setCurrentPage(1);
                  }}
                  itemsPerPageOptions={[10, 20, 30, 40, 50]}
                />
              </>
            )}
          </>
        )}
      </Card.Body>
    </Card>
  );
}
