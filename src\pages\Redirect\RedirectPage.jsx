import { Link, useLocation } from "react-router-dom";
import { useEffect } from "react";
import TikTokService from "../../services/integrations/tiktok";
import { Button } from "react-bootstrap";

const RedirectPage = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const accessToken = queryParams.get("access_token");

  useEffect(() => {
    if (accessToken) {
      const getAccessToken = async () => {
        try {
          const response = await TikTokService.getAccessTokenTikTok(
            accessToken
          );
          console.log(response);
        } catch (error) {
          console.error("Error fetching or updating access token:", error);
        }
      };
      getAccessToken();
    } else {
      // Handle the case where the access token is not present
      console.log("No access token found");
    }
  }, [accessToken]);

  return (
    <div
      className={"d-flex justify-content-center align-items-center flex-column"}
      style={{ height: "100vh" }}
    >
      <h1>Redirect Page</h1>
      {accessToken ? (
        <>
          <p>TikTok Integrated Successfully</p>
          <Button href={"/"}>Finish</Button>
        </>
      ) : (
        <>
          <p>No access token found, you need to login first</p>
          <Button as={Link} to={"/client/login"}>
            Login
          </Button>
        </>
      )}
    </div>
  );
};

export default RedirectPage;
