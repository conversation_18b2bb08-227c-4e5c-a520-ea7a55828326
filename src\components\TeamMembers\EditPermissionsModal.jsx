import React, {useState} from 'react';
import {HiMiniXMark} from "react-icons/hi2";
import Form from "react-bootstrap/Form";
import {MdOutlineModeEditOutline} from "react-icons/md";
import {FaUsersGear, FaUserXmark} from "react-icons/fa6";
import {IoEye} from "react-icons/io5";
import {Collapse} from "react-bootstrap";

const EditPermissionsModal = ({handleClose}) => {
    const [open, setOpen] = useState(false);
    return (
        <div className={"p-3"}>
            <div className={"position-relative w-100 text-center mb-2"}>
                <h5 className={"fw-bold"}>Edit Permissions</h5>
                <div className={"member-modal-close-icon"} onClick={() => handleClose()}>
                    <HiMiniXMark size={25} color={"#E35757"}/>
                </div>
            </div>
            <div className={"member-modal-permission-edit"}>
                <div className={"d-flex justify-content-between align-items-center"} onClick={() => setOpen(!open)}>
                    <IoEye size={54} className={"edit-content-icon"}/>
                    <div className={"ms-3"}>
                        <h5 className={"fw-bold"}>
                            View Clients
                        </h5>
                        <p>
                            The unseen of spending three years at Pixelgrade
                        </p>
                    </div>
                </div>
                <Collapse in={open} className={"member-show-collapse"}>
                    <div id="example-collapse-text">
                        <Form.Check
                            type="switch"
                            id="assigned-to-themselves"
                            label="Clients assigned to themselves"
                            className={"members-status-switch edit-per-switch my-3"}
                        />
                        <Form.Check
                            type="switch"
                            id="assigned-to-others"
                            label="Clients assigned to others"
                            className={"members-status-switch edit-per-switch my-3"}
                        />
                        <Form.Check
                            type="checkbox"
                            id="unassigned-clients"
                            label="Unassigned Clients"
                            className={"unassigned-clients-checkbox edit-per-switch my-3"}
                        />
                    </div>
                </Collapse>
            </div>
            <div className={"member-modal-permission"}>
                <MdOutlineModeEditOutline size={54} className={"edit-content-icon"}/>
                <div className={"ms-3"}>
                    <h5 className={"fw-bold"}>
                        Add & Edit Content
                    </h5>
                    <p>
                        How to design your site footer like we did
                    </p>
                </div>
            </div>
            <div className={"member-modal-permission"}>
                <FaUsersGear size={54} className={"green-label"}/>
                <div className={"ms-3"}>
                    <h5 className={"fw-bold"}>
                        Add & Edit Groups
                    </h5>
                    <p>
                        Lessons and insights from 8 years of Pixelgrade
                    </p>
                </div>
            </div>
            <div className={"member-modal-permission"}>
                <FaUserXmark size={54} className={"green-label"}/>
                <div className={"ms-3"}>
                    <h5 className={"fw-bold"}>
                        Delete Clients
                    </h5>
                    <p>
                        Starting your traveling blog with Vasco
                    </p>
                </div>
            </div>
        </div>
    );
};

export default EditPermissionsModal;