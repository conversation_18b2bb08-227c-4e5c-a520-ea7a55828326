import { useEffect, useRef, useState } from "react";
import { useTranslation } from 'react-i18next';
import leadService from "../../../services/leads";
import WidgestsRow from "../../AdminDashboard/WidgestsRow";

const StatisticsComponent = () => {
    const [widgetData, setWidgetData] = useState([]);
    const { i18n } = useTranslation();
    const fetchLeadsCountDashboardAbortController = useRef(null);
    const currentLanguageRef = useRef(i18n.language);

    useEffect(() => {
        const fetchData = async () => {
            if (fetchLeadsCountDashboardAbortController.current) {
                fetchLeadsCountDashboardAbortController.current.abort();
            }

            fetchLeadsCountDashboardAbortController.current = new AbortController();

            try {
                const response = await leadService.getLeadsCountDashboardApi(
                    fetchLeadsCountDashboardAbortController.current.signal
                );
                if (!fetchLeadsCountDashboardAbortController.current.signal.aborted) {
                    setWidgetData(response?.result || []);
                }
            } catch (error) {
                // Only log errors that are not related to request cancellation
                if (error.name !== 'AbortError' && 
                    error.name !== 'CanceledError' && 
                    error.code !== 'ERR_CANCELED' &&
                    !error.message?.includes('canceled')) {
                    console.error("Error fetching widget data:", error);
                }
            }
        };

        // Only fetch if language actually changed
        if (currentLanguageRef.current !== i18n.language) {
            currentLanguageRef.current = i18n.language;
            fetchData();
        } else if (!widgetData.length) {
            // Initial fetch if no data
            fetchData();
        }

        return () => {
            if (fetchLeadsCountDashboardAbortController.current) {
                fetchLeadsCountDashboardAbortController.current.abort();
            }
        };
    }, [i18n.language]); // Only depend on language changes

    return <WidgestsRow widgetData={widgetData} classes={'admin-widgets'}/>;
};

export default StatisticsComponent;
