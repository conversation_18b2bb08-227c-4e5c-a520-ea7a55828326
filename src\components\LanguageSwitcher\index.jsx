import { useTranslation } from 'react-i18next';
import {IoLanguageSharp} from "react-icons/io5";

function LanguageSwitcher() {
    const { i18n } = useTranslation();

    const toggleLanguage = () => {
        const newLang = i18n.language === 'en' ? 'ar' : 'en';
        i18n.changeLanguage(newLang);

        // Optional: Update document direction for RTL languages
        document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
    };

    return (
        <div onClick={toggleLanguage} className={"menu-item my-1 nav-link"}>
            <IoLanguageSharp size={20} />
            {" "}
            <div className={"ms-3 menu-item_label"}>
                {i18n.language === 'en' ? 'عربي' : 'English'}
            </div>
        </div>
    );
}

export default LanguageSwitcher;
