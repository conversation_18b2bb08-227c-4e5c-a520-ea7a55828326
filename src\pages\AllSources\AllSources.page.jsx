import { useEffect, useState } from "react";
import {
  Button,
  Col,
  Row,
  Stack,
  OverlayTrigger,
  Tooltip,
} from "react-bootstrap";
import { IoIosSwap } from "react-icons/io";
import { Link } from "react-router-dom";
import { FaTrash } from "react-icons/fa6";
import { TbListDetails } from "react-icons/tb";
import { MdOutlineRefresh } from "react-icons/md";
import Cookies from "js-cookie";
import metaService from "../../services/integrations/meta";
import Lot<PERSON> from "lottie-react";
import integrationAnimation from "../../assets/media/animations/integration.json";
import { ReactSVG } from "react-svg";
import DVLogo from "../../assets/media/Icons/IntegrationDVLogo.svg";
import CenteredModal from "../../components/Shared/modals/CenteredModal/CenteredModal";
import SuccessModalContent from "../Integrations/SingleIntegration/SuccessModalContent";
import CancelIntegrationModal from "../../components/Modals/CancelIntegrationModal";
import tiktokService from "../../services/integrations/tiktok";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import "./AllSources.styles.css";
import useIntegration from "../../hooks/useIntegration";

const AllSourcesPage = () => {
  const user = useSelector((state) => state.auth.user);
  const authURL = `https://business-api.tiktok.com/portal/auth?app_id=7386579004146335745&state=${user?.user?.id}&redirect_uri=https%3A%2F%2Fdvconnect.digitalvibesmarketing.com%2Fpublic%2Fapi%2Fauth%2Ftiktok`;
  const {
    integrationData,
    handleSignInWithPlatform,
    setShowCenteredModal,
    showCenteredModal,
    handleDisconnectFromFB,
    accounts,
    showCancelIntegrationModal,
    setShowCancelIntegrationModal,
    setIntegratedAlready,
    integratedAlready,
    tiktokIntegrated,
    setTiktokIntegrated,
    handleDisconnectFromTiktok,
    setShowCancelIntegrationModal2,
    showCancelIntegrationModal2,
  } = useIntegration();
  const currentIntegration = integrationData[0];
  const tiktokIntegration = integrationData[1];

  // Add state for token validity
  const [isTokenValid, setIsTokenValid] = useState(true);

  useEffect(() => {
    const cookieAccessToken = Cookies.get("access_token") || null;

    const checkAccessToken = async () => {
      try {
        const results = await Promise.allSettled([
          metaService.getAccessTokenApi(),
          tiktokService.checkAccessTokenTikTok(),
        ]);

        const [accessTokenResult, tiktokAccessTokenResult] = results;

        // Handle the access token response
        if (accessTokenResult.status === "fulfilled") {
          const response = accessTokenResult.value;
          if (response.data && response.data !== cookieAccessToken) {
            Cookies.remove("userPages");
          }
          if (response?.data.length !== 0) {
            Cookies.set("access_token", response.data);
            setIntegratedAlready(true);

            // Check if the token is valid
            if (
              user?.user &&
              (user?.user["access-token"] || cookieAccessToken) &&
              user?.user?.access_token_status === false
            ) {
              setIsTokenValid(false);
            }
          } else {
            Cookies.remove("access_token");
            setIntegratedAlready(false);
          }
        } else {
          console.error(
            "Error fetching access token:",
            accessTokenResult.reason
          );
        }

        // Handle the TikTok access token response
        if (tiktokAccessTokenResult.status === "fulfilled") {
          const result = tiktokAccessTokenResult.value;
          if (result?.data.length !== 0) {
            Cookies.set("access_token_tiktok", result.data);
            setTiktokIntegrated(true);
          } else {
            Cookies.remove("access_token_tiktok");
            setTiktokIntegrated(false);
          }
        } else {
          console.error(
            "Error checking TikTok access token:",
            tiktokAccessTokenResult.reason
          );
        }
      } catch (error) {
        console.error("Error during access token checks:", error);
      }
    };

    checkAccessToken();
  }, [user]);

  const { t } = useTranslation();

  // Function to handle reintegration
  const handleReintegrate = () => {
    handleSignInWithPlatform(currentIntegration?.platform);
  };
  console.log(integratedAlready, isTokenValid);
  return (
    <>
      <h2 className={"page-title"}>{t("pageHeaders.integrations")}</h2>

      <Row
        className={
          "column-gap-1 justify-content-center justify-content-md-evenly mt-5"
        }
      >
        <Col
          lg={5}
          xs={10}
          className="integration-card align-items-center col-offset-2 position-relative"
        >
          <div>
            <Stack
              direction={"horizontal"}
              className={"justify-content-center align-content-center"}
            >
              {currentIntegration?.icon}
              <Lottie
                animationData={integrationAnimation}
                className={"integration-animation"}
              />
              <ReactSVG src={DVLogo} />
            </Stack>
          </div>

          <p className={"integration-description my-5"}>
            {currentIntegration?.description}
          </p>
          <div className="d-flex flex-column flex-sm-row justify-content-center align-items-center w-100 gap-4">
            {integratedAlready && isTokenValid ? (
              <>
                <Button
                  className={"submit-btn me-2 fs-6 fw-normal"}
                  as={Link}
                  to={`/integrations/facebook/connected`}
                >
                  {t("integrations.viewIntegration")} <TbListDetails />
                </Button>
                <Button
                  variant={"outline-danger"}
                  className={"rounded-3 fs-6"}
                  style={{ padding: "12px 25px" }}
                  onClick={() => setShowCancelIntegrationModal(true)}
                >
                  {t("integrations.cancelIntegration")} <FaTrash />
                </Button>
              </>
            ) : integratedAlready && !isTokenValid ? (
              <Button
                className="submit-btn fw-bold fs-6 mx-auto"
                onClick={handleReintegrate}
                aria-label="Reintegrate Facebook"
              >
                {t("integrations.facebook.reintegrate")}{" "}
                <MdOutlineRefresh size={20} />
              </Button>
            ) : (
              <Button
                disabled={integratedAlready === null}
                className="submit-btn fw-bold fs-6 mx-auto"
                onClick={
                  integratedAlready === null
                    ? null
                    : () =>
                        handleSignInWithPlatform(currentIntegration?.platform)
                }
              >
                {t("integrations.connectTo")} {currentIntegration?.platform}{" "}
                <IoIosSwap />
              </Button>
            )}
            <CenteredModal
              show={showCenteredModal}
              children={
                <SuccessModalContent
                  currentIntegration={currentIntegration}
                  setShowCenteredModal={setShowCenteredModal}
                  onHide={() => setShowCenteredModal(false)}
                />
              }
              onHide={() => setShowCenteredModal(false)}
            />
            <CenteredModal
              show={showCancelIntegrationModal}
              children={
                <CancelIntegrationModal
                  cancelIntegrationFun={() =>
                    handleDisconnectFromFB(accounts?.id)
                  }
                  setShowCancelIntegrationModal={setShowCancelIntegrationModal}
                  onHide={() => setShowCancelIntegrationModal(false)}
                />
              }
              onHide={() => setShowCancelIntegrationModal(false)}
            />
          </div>
        </Col>
        <Col
          lg={5}
          xs={10}
          className="integration-card align-items-center col-offset-2"
        >
          <div>
            <Stack
              direction={"horizontal"}
              className={"justify-content-center align-content-center"}
            >
              {tiktokIntegration?.icon}
              <Lottie
                animationData={integrationAnimation}
                className={"integration-animation"}
              />
              <ReactSVG src={DVLogo} />
            </Stack>
          </div>

          <p className={"integration-description my-5"}>
            {tiktokIntegration?.description}
          </p>
          <div className="d-flex flex-column flex-sm-row justify-content-center align-items-center w-100 gap-4">
            {tiktokIntegrated ? (
              <>
                <Button
                  className={"submit-btn me-2 fs-6 fw-normal"}
                  as={Link}
                  to={`/integrations/tiktok/connected`}
                >
                  {t("integrations.viewIntegration")} <TbListDetails />
                </Button>
                <Button
                  variant={"outline-danger"}
                  className={"rounded-3 fs-6"}
                  style={{ padding: "12px 25px" }}
                  onClick={() => setShowCancelIntegrationModal2(true)}
                >
                  {t("integrations.cancelIntegration")} <FaTrash />
                </Button>
              </>
            ) : (
              <Button
                disabled={tiktokIntegrated === null}
                className="submit-btn fw-normal fs-6 mb-3 mx-auto"
                href={authURL}
              >
                {t("integrations.connectTo")} {tiktokIntegration?.platform}{" "}
                <IoIosSwap />
              </Button>
            )}
            <CenteredModal
              show={showCancelIntegrationModal2}
              children={
                <CancelIntegrationModal
                  cancelIntegrationFun={handleDisconnectFromTiktok}
                  setShowCancelIntegrationModal={setShowCancelIntegrationModal2}
                  onHide={() => setShowCancelIntegrationModal2(false)}
                />
              }
              onHide={() => setShowCancelIntegrationModal2(false)}
            />
          </div>
        </Col>
      </Row>
      {/*<Pagination className="data-table-pagination d-flex justify-content-center">*/}
      {/*    <Pagination.Prev onClick={() => setCurrentPage(currentPage - 1)}*/}
      {/*                     disabled={currentPage === 1 || filteredData.length === 0}/>*/}
      {/*    {Array.from({length: Math.ceil(filteredData.length / itemsPerPage)}).map((_, index) => (*/}
      {/*        <Pagination.Item key={index + 1} active={index + 1 === currentPage}*/}
      {/*                         onClick={() => paginate(index + 1)}>*/}
      {/*            {index + 1}*/}
      {/*        </Pagination.Item>*/}
      {/*    ))}*/}
      {/*    <Pagination.Next onClick={() => setCurrentPage(currentPage + 1)}*/}
      {/*                     disabled={currentPage === Math.ceil(filteredData.length / itemsPerPage) || filteredData.length === 0}/>*/}
      {/*</Pagination>*/}
    </>
  );
};

export default AllSourcesPage;
