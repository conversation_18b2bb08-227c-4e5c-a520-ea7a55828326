import { useState } from "react";
import * as Components from "../../components/Styles/SignIn_SignUp/SignIn_SignUp_Client.styles";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import "../../components/Styles/SignIn_SignUp/signin_signup.css";
import Form from "react-bootstrap/Form";
import { ReactSVG } from "react-svg";
import dvconnect from "../../assets/media/Icons/dvconnect-gray.svg";
import { Link, useNavigate, useLocation } from "react-router-dom";
import * as formik from "formik";
import * as yup from "yup";
import { Button } from "react-bootstrap";
import clientService from "../../services/auth/client";
import OTPComponent from "../../components/OTP/OTP.component";
import useAuth from "../../redux/hooks/useAuth";
import { isValidPhoneNumber } from "react-phone-number-input";
import { toast } from "react-toastify";
// import SignUpApi from "../../services/auth/client/signup-api";
import { FacebookAuth, GoogleAuth } from "../../utils/firebase.config";
import { FaFacebookF, FaUser, FaEnvelope } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";
import { FaLock, FaTiktok } from "react-icons/fa6";
import { useTranslation } from "react-i18next";
import styled from "styled-components";
import ValidatedPhoneInput from "../../components/PhoneInput/ValidatedPhoneInput";
import { showErrorToast } from "../../utils/toast-success-error";

const LanguageSwitcherContainer = styled.div`
  position: absolute;
  top: 20px;
  right: ${({ $isRTL }) => ($isRTL ? "auto" : "20px")};
  left: ${({ $isRTL }) => ($isRTL ? "20px" : "auto")};
  color: black;
  z-index: 1000;
`;

const SignIn_SignUp_Client = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === "ar";
  const toggleLanguage = () => {
    const newLang = i18n.language === "en" ? "ar" : "en";
    i18n.changeLanguage(newLang);

    // Optional: Update document direction for RTL languages
    document.documentElement.dir = newLang === "ar" ? "rtl" : "ltr";
  };

  const location = useLocation();
  const [signIn, toggle] = useState(
    location.state?.signin ?? !location.state?.signup
  );

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showOTPComponent, setShowOTPComponent] = useState(false);
  const navigate = useNavigate();
  const { Formik } = formik;
  const { login, setCurrentUserPermissions, signUpFlow } = useAuth();
  const signUpValidationSchema = yup.object().shape({
    name: yup.string().required(t("auth.signInSignUp.validation.nameRequired")),
    email: yup
      .string()
      .email(t("auth.signInSignUp.validation.emailInvalid"))
      .required(t("auth.signInSignUp.validation.emailRequired")),
    phone: yup
      .string()
      .test(
        "is-phone-valid",
        t("auth.signInSignUp.validation.phoneInvalid"),
        (value) => {
          return !value || isValidPhoneNumber(value);
        }
      )
      .required(t("auth.signInSignUp.validation.phoneRequired")),
    password: yup
      .string()
      .min(8, t("auth.signInSignUp.validation.passwordMin"))
      .required(t("auth.signInSignUp.validation.passwordRequired")),
    confirmPassword: yup
      .string()
      .oneOf(
        [yup.ref("password")],
        t("auth.signInSignUp.validation.passwordsMustMatch")
      )
      .required(t("auth.signInSignUp.validation.confirmPasswordRequired")),
    terms: yup
      .boolean()
      .oneOf([true], t("auth.signInSignUp.validation.termsRequired")),
  });

  const signInValidationSchema = yup.object().shape({
    password: yup
      .string()
      .required(t("auth.signInSignUp.validation.passwordRequired")),
    username: yup
      .string()
      .required(t("auth.signInSignUp.validation.usernameRequired")),
  });

  const handleSignInSubmit = async (values) => {
    const trimmedValues = {
      username: values?.username?.replace(/\s/g, ""),
      password: values?.password?.trim(),
    };
    try {
      const res = await clientService.SignInApi(
        values.flag ? values : trimmedValues
      );
      const permissions = res.data.Permissions.map(
        (permission) => permission.name
      );
      setCurrentUserPermissions(permissions);
      if (res.status === 200) {
        login({
          token: res.data.token,
          user: res.data.user,
        });
        const storedData = sessionStorage.getItem("packagePurchase");
        navigate(storedData ? "/package-purchase" : "/", { replace: true });
      }
    } catch (err) {
      showErrorToast(err.response.data.message);
      console.error(err);
    }
  };

  const handleSignUpSubmit = async (values) => {
    const data = {
      phone: values.phone,
      email: values.email,
    };
    try {
      if (values.flag) {
        await signUpFlow(values);
      }
      const res = await clientService.CheckPhoneApi(data);
      if (res.status === 200) {
        await signUpFlow(values);
        // window.location.reload();
      } else {
        showErrorToast(res.message[0]);
      }
    } catch (err) {
      showErrorToast(err.response.data.message);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };
  async function FacebookAuthButtonClicked(isSignUp) {
    try {
      const userCredential = await FacebookAuth();
      const { user, _tokenResponse } = userCredential;
      if (user && _tokenResponse && "oauthAccessToken" in _tokenResponse) {
        const data = {
          name: user.displayName,
          email: user.email || user?.providerData[0]?.email,
          phone: user.phoneNumber,
          access_token: _tokenResponse.oauthAccessToken,
          social: 0,
        };

        const filteredData = Object.fromEntries(
          Object.entries(data).filter(([key, value]) => value != null)
        );

        // await handleSignInSubmit(filteredData)

        // if (isSignUp) {
        //   await handleSignUpSubmit(registerData);
        // } else {
        try {
          const res = await clientService.SignInApi(filteredData);

          if (res.status === 200) {
            const permissions = res.data.Permissions.map(
              (permission) => permission.name
            );
            login({
              token: res.data.token,
              user: res.data.user,
            });
            navigate("/", { replace: true });
          }
        } catch (err) {
          showErrorToast(err.response.data.message);
          console.error(err);
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  async function GoogleAuthButtonClicked(isSignUp, flag) {
    try {
      const userCredential = await GoogleAuth();
      const { user, _tokenResponse } = userCredential;
      if (user && _tokenResponse && "oauthAccessToken" in _tokenResponse) {
        // Extract additional user data
        const googleUserData = {
          uid: user.uid,
          displayName: user.displayName,
          email: user.email || user.providerData[0].email,
          photo: user.photoURL,
          phoneNumber: user.phoneNumber,
          providerData: user.providerData[0], // Contains additional provider info
          metadata: {
            creationTime: user.metadata.creationTime,
            lastSignInTime: user.metadata.lastSignInTime,
          },
        };

        const registerData = {
          name: googleUserData.displayName,
          email: googleUserData.email,
          phone: googleUserData.phoneNumber,
          accessToken: _tokenResponse.oauthAccessToken,
          photo: googleUserData.photoURL, // Add profile photo
          googleUID: googleUserData.uid, // Add Google UID
          socialData: {
            // Structured social data
            provider: "google",
            ...googleUserData,
          },
          flag: flag,
        };

        const userData = {
          username: googleUserData.email,
          accessToken: _tokenResponse.oauthAccessToken,
          socialLogin: true,
          socialData: {
            provider: "google",
            uid: googleUserData.uid,
            photo: googleUserData.photoURL,
          },
          flag: flag,
          social: 1,
        };

        if (isSignUp) {
          await handleSignUpSubmit(registerData);
        } else {
          await handleSignInSubmit(userData);
        }
      }
    } catch (error) {
      console.error("Google Auth Error:", error);
      toast.error(error.message);
    }
  }
  const handleRequestTikTokToken = async () => {
    await clientService.TikTokRequestTokenApi();
  };

  const routeState = {
    from: location.pathname,
    signin: signIn,
  };
  return (
    <Components.Container dir={isRTL ? "rtl" : "ltr"}>
      <LanguageSwitcherContainer $isRTL={isRTL}>
        <div
          onClick={toggleLanguage}
          className={"menu-item my-1 nav-link fs-5 text-bold"}
          role="button"
        >
          <div className={"ms-3 menu-item_label"}>
            {i18n.language === "en" ? "عربي" : "English"}
          </div>
        </div>
      </LanguageSwitcherContainer>
      <Components.SignUpContainer dir={isRTL ? "rtl" : "ltr"} $signIn={signIn}>
        <Components.Form dir={isRTL ? "rtl" : "ltr"}>
          <Formik
            initialValues={{
              name: "",
              email: "",
              phone: "",
              password: "",
              terms: false,
              confirmPassword: "",
            }}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                const data = {
                  phone: values.phone,
                  email: values.email,
                };
                const res = await clientService.CheckPhoneApi(data);
                if (res.status === 200) {
                  setShowOTPComponent(true);
                } else {
                  toast.error(res.message[0], {
                    position: "bottom-right",
                    theme: "dark",
                  });
                }
              } catch (err) {
                toast.error(err.response.data.message, {
                  position: "bottom-right",
                  theme: "dark",
                });
              } finally {
                setSubmitting(false);
              }
            }}
            validationSchema={signUpValidationSchema}
            validateOnChange={false} // Only validate on submit and blur
            validateOnBlur={true} // Validate when field loses focus
          >
            {({
              handleSubmit,
              handleChange,
              handleBlur,
              values,
              touched,
              errors,
            }) =>
              showOTPComponent ? (
                <OTPComponent
                  userData={values}
                  setShowOTPComponent={setShowOTPComponent}
                  handleSignUpSubmit={handleSignUpSubmit}
                />
              ) : (
                <Form
                  noValidate
                  onSubmit={handleSubmit}
                  className={"signUpForm"}
                  autoComplete="off"
                  data-form-type="other"
                >
                  <Components.Title
                    className={`mb-2 mobile-title ${
                      isRTL ? "text-right" : "text-left"
                    } text-md-center`}
                  >
                    {t("auth.signInSignUp.signUp.title")}
                  </Components.Title>
                  <div
                    style={{ color: "rgba(0, 0, 0, 0.6)" }}
                    className={`${
                      isRTL ? "text-right" : "text-left"
                    } text-md-center`}
                  >
                    <div>{t("auth.signInSignUp.signUp.subtitle.line1")}</div>
                    <span>{t("auth.signInSignUp.signUp.subtitle.line2")}</span>
                  </div>
                  <Form.Group className={"input-with-icon"}>
                    <div className={"input-icon-container"}>
                      <FaUser
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name="name"
                        type="text"
                        placeholder={t("auth.signInSignUp.signUp.form.name")}
                        value={values.name}
                        onChange={handleChange}
                        isInvalid={touched.name && errors.name}
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.name}
                      </Form.Control.Feedback>
                    </div>
                  </Form.Group>
                  <Form.Group className="input-with-icon">
                    <div className="input-icon-container">
                      <FaEnvelope
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name="email"
                        type="email"
                        placeholder={t("auth.signInSignUp.signUp.form.email")}
                        value={values.email}
                        onChange={handleChange}
                        isInvalid={touched.email && errors.email}
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.email}
                      </Form.Control.Feedback>
                    </div>
                  </Form.Group>
                  <Form.Group className="mobile-otp rounded-2 validated-phone-input-container">
                    <ValidatedPhoneInput
                      name="phone"
                      value={values.phone}
                      onChange={(value) =>
                        handleChange({ target: { name: "phone", value } })
                      }
                      touched={touched.phone}
                      error={errors.phone}
                      international={false}
                      defaultCountry="EG"
                      showValidationMessage={true}
                      placeholder={t("auth.signInSignUp.signUp.form.phone")}
                      style={{
                        direction: isRTL ? "rtl" : "ltr",
                        textAlign: isRTL ? "right" : "left",
                      }}
                    />
                  </Form.Group>
                  <Form.Group className="input-with-icon password-input-container">
                    <div className="input-icon-container">
                      <FaLock
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name="password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t(
                          "auth.signInSignUp.signUp.form.password"
                        )}
                        value={values.password}
                        onChange={handleChange}
                        onBlur={handleBlur} // Add this to trigger validation on blur
                        isInvalid={touched.password && errors.password}
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.password}
                      </Form.Control.Feedback>
                      <div
                        className={`password-input-icon ${
                          isRTL ? "password-icon-rtl" : ""
                        }`}
                        onClick={togglePasswordVisibility}
                      >
                        {showPassword ? (
                          <AiOutlineEye
                            className={"password-toggle-icon"}
                            size={25} // Slightly reduced size
                          />
                        ) : (
                          <AiOutlineEyeInvisible
                            className={"password-toggle-icon"}
                            size={25} // Slightly reduced size
                          />
                        )}
                      </div>
                    </div>
                  </Form.Group>
                  <Form.Group className="input-with-icon password-input-container">
                    <div className="input-icon-container">
                      <FaLock
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t(
                          "auth.signInSignUp.signUp.form.confirmPassword"
                        )}
                        value={values.confirmPassword}
                        onChange={handleChange}
                        isInvalid={
                          touched.confirmPassword && errors.confirmPassword
                        }
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.confirmPassword}
                      </Form.Control.Feedback>
                      <div
                        className={`password-input-icon ${
                          isRTL ? "password-icon-rtl" : ""
                        }`}
                        onClick={toggleConfirmPasswordVisibility}
                      >
                        {showConfirmPassword ? (
                          <AiOutlineEye
                            className={"password-toggle-icon"}
                            size={25}
                          />
                        ) : (
                          <AiOutlineEyeInvisible
                            className={"password-toggle-icon"}
                            size={25}
                          />
                        )}
                      </div>
                    </div>
                  </Form.Group>
                  <Form.Group className={"mt-3"}>
                    <Form.Check
                      required
                      inline
                      label={
                        <>
                          <span className="gray-label">
                            {t("auth.signInSignUp.terms.iAgree")}{" "}
                          </span>
                          <Link
                            state={routeState}
                            to="/terms"
                            className="green-label"
                          >
                            {t("terms.title")}{" "}
                          </Link>
                          <span className="gray-label">
                            {t("auth.signInSignUp.terms.and")}{" "}
                          </span>
                          <Link
                            state={routeState}
                            to="/privacy"
                            className="green-label"
                          >
                            {t("privacy.title")}
                          </Link>
                        </>
                      }
                      name="terms"
                      type={"checkbox"}
                      id={`inline-checkbox-1`}
                      className={`align-self-${
                        isRTL ? "end" : "start"
                      } checkbox-success`}
                      value={values.terms}
                      onChange={handleChange}
                      isValid={touched.terms && !errors.terms}
                      isInvalid={touched.terms && errors.terms}
                    />
                    {touched.terms && errors.terms && (
                      <p className="text-danger" style={{ fontSize: "12px" }}>
                        {errors.terms}
                      </p>
                    )}
                  </Form.Group>
                  <div
                    className={
                      "d-flex justify-content-between align-items-center flex-column gap-4 mt-4"
                    }
                  >
                    <Button className={"submit-btn"} type={"submit"}>
                      {t("auth.signInSignUp.signUp.form.button")}
                    </Button>
                    <div className={"d-flex justify-content-center"}>
                      <div
                        className={"social-icon-sign"}
                        onClick={() => FacebookAuthButtonClicked(true, "f")}
                      >
                        <FaFacebookF color={"#3D6AD6"} size={25} />
                      </div>
                      <div
                        className={"social-icon-sign mx-3"}
                        onClick={() => GoogleAuthButtonClicked(true, "g")}
                      >
                        <FcGoogle size={25} />
                      </div>
                      {/*<div className={"social-icon-sign ms-3"}>*/}
                      {/*  <FaLinkedinIn color={"#0073B1"} size={25}/>*/}
                      {/*</div>*/}
                      <div
                        className={"social-icon-sign"}
                        onClick={handleRequestTikTokToken}
                      >
                        <FaTiktok color={"#333"} size={25} />
                      </div>
                    </div>
                    <ReactSVG src={dvconnect} className={"dvconnect-gray"} />
                    <Components.MobileBtn
                      className={"btn btn-outline-secondary"}
                      onClick={() => toggle(true)}
                      style={{ zIndex: 1000 }}
                    >
                      Have An Account?
                    </Components.MobileBtn>
                    {/*<div className={"d-flex justify-content-center mt-5"}>*/}
                    {/*  <div*/}
                    {/*    className={"social-icon-sign"}*/}
                    {/*    onClick={() => FacebookAuthButtonClicked(true, "fb")}*/}
                    {/*  >*/}
                    {/*    <FaFacebookF color={"#3D6AD6"} size={25} />*/}
                    {/*  </div>*/}
                    {/*  <div*/}
                    {/*    className={"social-icon-sign mx-3"}*/}
                    {/*    onClick={() => GoogleAuthButtonClicked(true, "g")}*/}
                    {/*  >*/}
                    {/*    <FcGoogle size={25} />*/}
                    {/*  </div>*/}
                    {/*  <div className={"social-icon-sign"}>*/}
                    {/*    <FaLinkedinIn color={"#0073B1"} size={25} />*/}
                    {/*  </div>*/}
                    {/*</div>*/}
                  </div>
                </Form>
              )
            }
          </Formik>
        </Components.Form>
      </Components.SignUpContainer>
      <Components.SignInContainer dir={isRTL ? "rtl" : "ltr"} $signIn={signIn}>
        <Components.Form
          className={isRTL ? "ms-3" : "me-3"}
          dir={isRTL ? "rtl" : "ltr"}
        >
          <Formik
            initialValues={{
              username: "",
              password: "",
              rememberMe: false,
            }}
            onSubmit={handleSignInSubmit}
            validationSchema={signInValidationSchema}
            autoComplete="off"
            validateOnChange={false} // Add this to prevent validation while typing
            validateOnBlur={false} // Only validate when field loses focus
          >
            {({
              handleSubmit,
              handleChange,
              handleBlur,
              values,
              touched,
              errors,
            }) => (
              <Form
                noValidate
                onSubmit={handleSubmit}
                className={"signInForm"}
                autoComplete="off"
                data-form-type="other"
              >
                <Components.Title
                  className={"mobile-title text-left text-md-center"}
                >
                  {t("auth.signInSignUp.signIn.title")}
                </Components.Title>
                <div
                  style={{ color: "rgba(0, 0, 0, 0.6)" }}
                  className={"text-left text-md-center"}
                >
                  <div>{t("auth.signInSignUp.signIn.subtitle.line1")}</div>
                  <span>{t("auth.signInSignUp.signIn.subtitle.line2")}</span>
                </div>
                <div>
                  <Form.Group className="input-with-icon">
                    <div className="input-icon-container">
                      <FaEnvelope
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name={"username"}
                        type={"text"}
                        placeholder={t(
                          "auth.signInSignUp.signIn.form.emailPhone"
                        )}
                        value={values.username}
                        onChange={handleChange}
                        isInvalid={touched.username && errors.username}
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      {values.username && !isNaN(values.username) && (
                        <small className="text-muted">
                          {t("auth.signInSignUp.signIn.form.usernameHint")}
                        </small>
                      )}
                      <Form.Control.Feedback type="invalid">
                        {errors.username}
                      </Form.Control.Feedback>
                    </div>
                  </Form.Group>
                  <Form.Group className="input-with-icon password-input-container">
                    <div className="input-icon-container">
                      <FaLock
                        className={`input-icon ${
                          isRTL ? "input-icon-rtl" : ""
                        }`}
                      />
                      <Form.Control
                        name={"password"}
                        type={showPassword ? "text" : "password"}
                        placeholder={t(
                          "auth.signInSignUp.signIn.form.password"
                        )}
                        value={values.password}
                        onChange={handleChange}
                        isInvalid={touched.password && errors.password}
                        style={{
                          paddingRight: isRTL ? "30px" : "40px",
                          paddingLeft: isRTL ? "40px" : "30px",
                        }}
                      />
                      <Form.Control.Feedback type="invalid">
                        {errors.password}
                      </Form.Control.Feedback>
                      <div
                        className={"password-input-icon"}
                        onClick={togglePasswordVisibility}
                        style={{
                          right: isRTL ? "unset" : "10px",
                          left: isRTL ? "20px" : "unset",
                        }}
                      >
                        {showPassword ? (
                          <AiOutlineEye
                            className={"password-toggle-icon"}
                            size={25}
                          />
                        ) : (
                          <AiOutlineEyeInvisible
                            className={"password-toggle-icon"}
                            size={25}
                          />
                        )}
                      </div>
                    </div>
                  </Form.Group>
                </div>

                <div
                  className={
                    "d-flex w-100 justify-content-between align-self-start align-items-center flex-sm-row flex-column"
                  }
                >
                  <Form.Check
                    inline
                    label={
                      <span className="gray-label">
                        {t("auth.signInSignUp.signIn.form.rememberMe")}
                      </span>
                    }
                    name="rememberMe"
                    type={"checkbox"}
                    id={`inline-checkbox-2`}
                    onChange={handleChange}
                    value={values.rememberMe}
                  />{" "}
                  <Link to={"/client/forgot-password"}>
                    <Components.Anchor className={"green-label"}>
                      {t("auth.signInSignUp.signIn.form.forgotPassword")}
                    </Components.Anchor>
                  </Link>
                </div>
                <div className={"text-center social-submit-button"}>
                  <Button type={"submit"} className={"submit-btn mx-auto"}>
                    {t("auth.signInSignUp.signIn.form.button")}
                  </Button>
                  <Components.MobileBtn
                    className={"btn btn-outline-secondary"}
                    onClick={() => toggle(false)}
                  >
                    {t("auth.signInSignUp.signIn.form.createAccount")}
                  </Components.MobileBtn>

                  <div className={"gray-label"}>
                    {t("auth.signInSignUp.signIn.form.orUseEmail")}
                  </div>
                  <div className={"d-flex justify-content-center"}>
                    <div
                      className={"social-icon-sign"}
                      onClick={() => FacebookAuthButtonClicked(false, "f")}
                    >
                      <FaFacebookF color={"#3D6AD6"} size={25} />
                    </div>
                    <div
                      className={"social-icon-sign mx-3"}
                      onClick={() => GoogleAuthButtonClicked(false, "g")}
                    >
                      <FcGoogle size={25} />
                    </div>
                    {/*<div className={"social-icon-sign ms-3"}>*/}
                    {/*  <FaLinkedinIn color={"#0073B1"} size={25} />*/}
                    {/*</div>*/}
                    <div
                      className={"social-icon-sign"}
                      onClick={handleRequestTikTokToken}
                    >
                      <FaTiktok color={"#333"} size={25} />
                    </div>
                  </div>
                  <ReactSVG src={dvconnect} className={"dvconnect-gray"} />
                </div>
              </Form>
            )}
          </Formik>
        </Components.Form>
      </Components.SignInContainer>

      <Components.OverlayContainer dir={isRTL ? "rtl" : "ltr"} $signIn={signIn}>
        <Components.Overlay $signIn={signIn} dir={isRTL ? "rtl" : "ltr"}>
          <Components.LeftOverlayPanel
            $signIn={signIn}
            dir={isRTL ? "rtl" : "ltr"}
          >
            <Components.Title>
              {t("auth.signInSignUp.overlay.welcome.title")}
            </Components.Title>
            <Components.Paragraph>
              {t("auth.signInSignUp.overlay.welcome.message")}
            </Components.Paragraph>
            <Components.GhostButton onClick={() => toggle(true)}>
              {t("auth.signInSignUp.overlay.welcome.button")}
            </Components.GhostButton>
          </Components.LeftOverlayPanel>
          <Components.RightOverlayPanel
            $signIn={signIn}
            dir={isRTL ? "rtl" : "ltr"}
          >
            <Components.Title>
              {t("auth.signInSignUp.overlay.welcomeDv.title")}
            </Components.Title>
            <Components.Paragraph>
              {t("auth.signInSignUp.overlay.welcomeDv.message")}
            </Components.Paragraph>
            <Components.GhostButton onClick={() => toggle(false)}>
              {t("auth.signInSignUp.overlay.welcomeDv.button")}
            </Components.GhostButton>
          </Components.RightOverlayPanel>
        </Components.Overlay>
      </Components.OverlayContainer>
    </Components.Container>
  );
};

export default SignIn_SignUp_Client;
