import { Button } from "react-bootstrap";
const DeleteTeamMemberModal = (props) => {
  return (
    <div
      className={"d-flex flex-column justify-content-around align-items-center"}
    >
      <h3 className={"fw-bold text-center my-5"}>{props.title}</h3>
      <div className={"d-flex justify-content-center"}>
        <Button
          variant={"secondary"}
          className={"me-3 rounded-pill"}
          onClick={() => props.handleClose()}
        >
          Cancel
        </Button>
        <Button
          variant={"danger"}
          className={"rounded-pill"}
          onClick={() =>
            props.deleteMemberFunction({
              teamMemberId: props.id,
              handleClose: props.handleClose,
              afterDelete: props?.afterDelete,
            })
          }
        >
          Delete
        </Button>
      </div>
    </div>
  );
};

export default DeleteTeamMemberModal;
