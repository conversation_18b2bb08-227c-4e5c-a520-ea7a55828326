import { <PERSON><PERSON>, Container, Modal } from "react-bootstrap";
import "./CreateClient.css";
import Form from "react-bootstrap/Form";
import * as Yup from "yup";
import { Formik } from "formik";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>utlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { useState } from "react";
import useAdmin from "../../redux/hooks/useAdmin";

const CreateClientAdminModal = (props) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { handleAddClient } = useAdmin();
  const { t } = useTranslation();
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const { show, onHide } = props;

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    phone: Yup.string()
      .matches(/^\+?[0-9\s-]*$/, {
        message: "Invalid phone number format",
        excludeEmptyString: true,
      })
      .required("Phone is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    password: Yup.string()
      .required("Password is required")
      .min(8, "Password must be at least 8 characters"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password"), null], "Passwords must match")
      .required("Confirm Password is required"),
  });

  return (
    <>
      <Modal
        show={show}
        onHide={onHide}
        className={"create-client-modal"}
        centered
      >
        <Container
          className={`rounded-1 p-4 create-client-form ${props.className}`}
        >
          <Formik
            initialValues={{
              name: "",
              phone: "",
              email: "",
              password: "",
              confirmPassword: "",
            }}
            validationSchema={validationSchema}
            onSubmit={(values, { resetForm }) => {
              // Use dispatch with the handleAddClient action creator
              handleAddClient(values, onHide, resetForm);
            }}
            validateOnChange={false}
            validateOnBlur={true}
          >
            {({ errors, touched, handleChange, handleSubmit, values }) => (
              <Form noValidate onSubmit={handleSubmit}>
                <div className={"text-center fw-bold mb-3"}>Add New Client</div>
                <Form.Group className={"mb-3"}>
                  <Form.Label>Client Name</Form.Label>
                  <Form.Control
                    type={"text"}
                    value={values.name}
                    isInvalid={touched.name && errors.name}
                    onChange={handleChange}
                    name={"name"}
                    placeholder={"Enter Lead Name"}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.name}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>Mobile Number</Form.Label>
                  <Form.Control
                    type="tel"
                    value={values.phone}
                    isInvalid={touched.phone && errors.phone}
                    onChange={handleChange}
                    name={"phone"}
                    placeholder={"Enter Mobile Number"}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.phone}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group className={"mb-3"}>
                  <Form.Label>Email Address</Form.Label>
                  <Form.Control
                    type={"email"}
                    value={values.email}
                    isInvalid={touched.email && errors.email}
                    onChange={handleChange}
                    name={"email"}
                    placeholder={"Enter Email Address"}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.email}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group
                  className={"mb-3 position-relative password-input-container"}
                >
                  <Form.Label>Password</Form.Label>
                  <Form.Control
                    type={showPassword ? "text" : "password"}
                    value={values.password}
                    isInvalid={touched.password && errors.password}
                    onChange={handleChange}
                    name={"password"}
                    placeholder={"Enter Password"}
                  />
                  <div
                    className={"password-input-icon-TM"}
                    onClick={togglePasswordVisibility}
                  >
                    {showPassword ? (
                      <AiOutlineEye size={25} />
                    ) : (
                      <AiOutlineEyeInvisible size={25} />
                    )}
                  </div>
                  <Form.Control.Feedback type="invalid">
                    {errors.password}
                  </Form.Control.Feedback>
                </Form.Group>
                <Form.Group
                  className={"mb-3 position-relative password-input-container"}
                >
                  <Form.Label>Confirm Password</Form.Label>
                  <Form.Control
                    type={showConfirmPassword ? "text" : "password"}
                    value={values.confirmPassword}
                    isInvalid={
                      touched.confirmPassword && errors.confirmPassword
                    }
                    onChange={handleChange}
                    name={"confirmPassword"}
                    placeholder={"Enter Confirm Password"}
                  />
                  <div
                    className={"password-input-icon-TM"}
                    onClick={toggleConfirmPasswordVisibility}
                  >
                    {showConfirmPassword ? (
                      <AiOutlineEye size={25} />
                    ) : (
                      <AiOutlineEyeInvisible size={25} />
                    )}
                  </div>
                  <Form.Control.Feedback type="invalid">
                    {errors.confirmPassword}
                  </Form.Control.Feedback>
                </Form.Group>
                <center>
                  <Button
                    type={"submit"}
                    className={"submit-btn mx-auto"}
                    style={{ width: "fit-content" }}
                  >
                    Add Client
                  </Button>
                </center>
              </Form>
            )}
          </Formik>
        </Container>
      </Modal>
    </>
  );
};

export default CreateClientAdminModal;
