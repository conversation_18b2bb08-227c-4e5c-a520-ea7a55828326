import CenteredModal from "../CenteredModal/CenteredModal";
import { FaXmark } from "react-icons/fa6";
import { Spinner } from "react-bootstrap";
import "./FilePreviewModal.css";
import { useEffect, useState } from "react";

const FilePreviewModal = ({ file, handleClose, show }) => {
  const [pdfDataUrl, setPdfDataUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState(null);

  const getFileType = (file) => {
    if (!file) return null;

    // If file has a type property, use it
    if (file.type) return file.type;

    // Otherwise try to determine from URL or filename
    const url = file.url || file.message;
    if (!url || typeof url !== "string") return null;

    // Check filename first if available
    if (file.filename) {
      const filenameLower = file.filename.toLowerCase();
      if (filenameLower.endsWith(".pdf")) return "pdf";
      if (
        ["jpg", "jpeg", "png", "gif", "webp"].some((ext) =>
          filenameLower.endsWith(`.${ext}`)
        )
      )
        return "image";
      if (
        ["mp4", "webm", "mov"].some((ext) => filenameLower.endsWith(`.${ext}`))
      )
        return "video";
      if (
        ["mp3", "wav", "ogg"].some((ext) => filenameLower.endsWith(`.${ext}`))
      )
        return "audio";
      if (["doc", "docx"].some((ext) => filenameLower.endsWith(`.${ext}`)))
        return "document";
    }

    // Fall back to URL extension
    const extension = url.split(".").pop().toLowerCase().split("?")[0];

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension)) {
      return "image";
    } else if (["mp4", "webm", "mov"].includes(extension)) {
      return "video";
    } else if (["pdf"].includes(extension)) {
      return "pdf";
    } else if (["doc", "docx"].includes(extension)) {
      return "document";
    } else if (["mp3", "wav", "ogg"].includes(extension)) {
      return "audio";
    }

    return "file";
  };

  const fileType = getFileType(file);
  const fileUrl = file?.url || file?.message;
  const fileName =
    file?.filename ||
    (fileUrl && typeof fileUrl === "string"
      ? fileUrl.split("/").pop().split("?")[0]
      : "File");

  const responsiveMediaStyles = {
    maxWidth: "80vw",
    maxHeight: "80vh",
    width: "100%",
    height: "auto",
    objectFit: "contain",
  };

  // Fetch PDF as blob to prevent download managers from intercepting
  useEffect(() => {
    if (
      show &&
      fileType === "pdf" &&
      fileUrl &&
      (file?.preventDownload || true)
    ) {
      setIsLoading(true);
      setLoadError(null);
      setPdfDataUrl(null);

      // Add a random parameter to prevent caching
      const urlWithParam = fileUrl.includes("?")
        ? `${fileUrl}&nodownload=${Date.now()}`
        : `${fileUrl}?nodownload=${Date.now()}`;

      fetch(urlWithParam)
        .then((response) => {
          if (!response.ok) {
            throw new Error(
              `Failed to load PDF: ${response.status} ${response.statusText}`
            );
          }
          return response.blob();
        })
        .then((blob) => {
          const dataUrl = URL.createObjectURL(blob);
          setPdfDataUrl(dataUrl);
          setIsLoading(false);
        })
        .catch((error) => {
          console.error("Error fetching PDF:", error);
          setLoadError(error.message);
          setIsLoading(false);
        });
    }

    // Clean up blob URL when modal closes
    return () => {
      if (pdfDataUrl) {
        URL.revokeObjectURL(pdfDataUrl);
      }
    };
  }, [show, fileType, fileUrl, file?.preventDownload]);

  return (
    <CenteredModal
      size="lg"
      show={show}
      onHide={handleClose}
      className="file-preview-modal"
    >
      <div className="file-preview-container">
        <FaXmark
          color="red"
          size={30}
          onClick={handleClose}
          className="modal-close-icon rounded-circle bg-white p-1"
        />

        {!fileUrl ? (
          <div className="file-error">File not available</div>
        ) : isLoading ? (
          <div className="loading-container">
            <div className="spinner-border text-light" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="text-light mt-3">Loading file...</p>
          </div>
        ) : loadError ? (
          <div className="file-error">
            <p>Error loading file: {loadError}</p>
            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary mt-3"
              download={fileName}
            >
              Download Instead
            </a>
          </div>
        ) : fileType === "image" ? (
          <img
            src={fileUrl}
            alt={fileName}
            style={responsiveMediaStyles}
            onError={(e) => {
              console.error("Modal image failed to load:", e.target.src);
              e.target.onerror = null;
              e.target.style.display = "none";

              // Create error message
              const errorDiv = document.createElement("div");
              errorDiv.textContent = "Image could not be loaded";
              errorDiv.style.padding = "20px";
              errorDiv.style.backgroundColor = "#f8d7da";
              errorDiv.style.color = "#721c24";
              errorDiv.style.borderRadius = "5px";
              errorDiv.style.margin = "20px";
              errorDiv.style.textAlign = "center";

              e.target.parentNode.appendChild(errorDiv);
            }}
          />
        ) : fileType === "video" ? (
          <video controls style={responsiveMediaStyles}>
            <source src={fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : fileType === "audio" ? (
          <audio controls style={{ width: "100%" }}>
            <source src={fileUrl} type="audio/mpeg" />
            Your browser does not support the audio tag.
          </audio>
        ) : fileType === "pdf" ? (
          <div className="pdf-container">
            <div className="pdf-filename mb-2">{fileName}</div>
            {pdfDataUrl ? (
              <iframe
                src={`${pdfDataUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                width="100%"
                height="600px"
                title={fileName}
                frameBorder="0"
                className="pdf-iframe"
              />
            ) : (
              <iframe
                src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1&nodownload=true&t=${Date.now()}`}
                width="100%"
                height="600px"
                title={fileName}
                frameBorder="0"
                className="pdf-iframe"
                sandbox="allow-scripts allow-same-origin"
              />
            )}
            <div className="pdf-controls mt-3">
              <a
                href={fileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary"
                download={fileName}
              >
                Download PDF
              </a>
            </div>
          </div>
        ) : (
          <div className="file-download-container">
            <p>This file type cannot be previewed directly.</p>
            <p className="file-name">{fileName}</p>
            <a
              href={fileUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary"
              download={fileName}
            >
              Download File
            </a>
          </div>
        )}
      </div>
    </CenteredModal>
  );
};

export default FilePreviewModal;
